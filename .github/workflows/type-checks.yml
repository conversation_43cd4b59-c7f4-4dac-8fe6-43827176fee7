# .github/workflows/type-checks.yml
name: Type Safety Checks

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  php-type-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          extensions: mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, gd
          coverage: none

      - name: Install Composer Dependencies
        run: composer install --prefer-dist --no-interaction --no-progress

      - name: Run PHPStan
        run: vendor/bin/phpstan analyze

      - name: Run Rector Type Safety Check
        run: vendor/bin/rector process --dry-run --config=rector-type-safety.php

      - name: Run Laravel Pint
        run: vendor/bin/pint --test

  ts-type-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install Dependencies
        run: pnpm install --frozen-lockfile

      - name: TypeScript Type Check
        run: pnpm tsc --noEmit

      - name: ESLint Check
        run: pnpm eslint --ext .ts,.tsx resources/js
