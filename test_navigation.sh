#!/bin/bash

echo "Testing Navigation Links in Guidelines Documents"
echo "================================================"

# Define the expected navigation sequence
declare -a docs=(
    ".ai/guidelines/010-project-overview.md"
    ".ai/guidelines/020-documentation-standards.md"
    ".ai/guidelines/030-development-standards.md"
    ".ai/guidelines/040-workflow-guidelines.md"
    ".ai/guidelines/050-testing-standards.md"
    ".ai/guidelines/060-testing/000-index.md"
    ".ai/guidelines/090-security-standards.md"
    ".ai/guidelines/100-performance-standards.md"
)

echo "1. Checking if all documents exist..."
all_exist=true
for doc in "${docs[@]}"; do
    if [[ -f "$doc" ]]; then
        echo "✓ $doc exists"
    else
        echo "✗ $doc NOT FOUND"
        all_exist=false
    fi
done

echo ""
echo "2. Checking navigation sections exist..."
nav_sections=true
for doc in "${docs[@]}"; do
    if grep -q "## Navigation" "$doc"; then
        echo "✓ $doc has Navigation section"
    else
        echo "✗ $doc missing Navigation section"
        nav_sections=false
    fi
done

echo ""
echo "3. Checking navigation sequence..."
sequence_correct=true

# Check first document (010) - should only have Next
if grep -q "Next →.*020-documentation-standards.md" ".ai/guidelines/010-project-overview.md" && \
   ! grep -q "← Previous" ".ai/guidelines/010-project-overview.md"; then
    echo "✓ 010-project-overview.md has correct navigation (Next only)"
else
    echo "✗ 010-project-overview.md has incorrect navigation"
    sequence_correct=false
fi

# Check last document (100) - should only have Previous
if grep -q "← Previous.*090-security-standards.md" ".ai/guidelines/100-performance-standards.md" && \
   ! grep -q "Next →" ".ai/guidelines/100-performance-standards.md"; then
    echo "✓ 100-performance-standards.md has correct navigation (Previous only)"
else
    echo "✗ 100-performance-standards.md has incorrect navigation"
    sequence_correct=false
fi

# Check middle documents - should have both Previous and Next
middle_docs=(
    "020-documentation-standards.md:010-project-overview.md:030-development-standards.md"
    "030-development-standards.md:020-documentation-standards.md:040-workflow-guidelines.md"
    "040-workflow-guidelines.md:030-development-standards.md:050-testing-standards.md"
    "050-testing-standards.md:040-workflow-guidelines.md:060-testing/000-index.md"
    "090-security-standards.md:060-testing/000-index.md:100-performance-standards.md"
)

for entry in "${middle_docs[@]}"; do
    IFS=':' read -r current prev next <<< "$entry"
    current_path=".ai/guidelines/$current"

    if grep -q "← Previous.*$prev" "$current_path" && \
       grep -q "Next →.*$next" "$current_path"; then
        echo "✓ $current has correct navigation (Previous and Next)"
    else
        echo "✗ $current has incorrect navigation"
        sequence_correct=false
    fi
done

# Special check for 060-testing/000-index.md (uses relative paths)
if grep -q "← Previous.*../050-testing-standards.md" ".ai/guidelines/060-testing/000-index.md" && \
   grep -q "Next →.*../090-security-standards.md" ".ai/guidelines/060-testing/000-index.md"; then
    echo "✓ 060-testing/000-index.md has correct navigation with relative paths"
else
    echo "✗ 060-testing/000-index.md has incorrect navigation"
    sequence_correct=false
fi

echo ""
echo "4. Summary"
echo "=========="
if $all_exist && $nav_sections && $sequence_correct; then
    echo "✅ All navigation tests PASSED!"
    echo "   - All documents exist"
    echo "   - All documents have Navigation sections"
    echo "   - Navigation sequence is correct"
    exit 0
else
    echo "❌ Some navigation tests FAILED!"
    [[ $all_exist == false ]] && echo "   - Some documents are missing"
    [[ $nav_sections == false ]] && echo "   - Some documents missing Navigation sections"
    [[ $sequence_correct == false ]] && echo "   - Navigation sequence has errors"
    exit 1
fi
