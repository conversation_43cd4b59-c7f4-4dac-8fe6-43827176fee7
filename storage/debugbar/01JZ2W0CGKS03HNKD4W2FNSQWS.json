{"__meta": {"id": "01JZ2W0CGKS03HNKD4W2FNSQWS", "datetime": "2025-07-01 11:29:56", "utime": **********.755987, "method": "GET", "uri": "/admin/profile", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.614706, "end": **********.755994, "duration": 0.14128804206848145, "duration_str": "141ms", "measures": [{"label": "Booting", "start": **********.614706, "relative_start": 0, "end": **********.692662, "relative_end": **********.692662, "duration": 0.*****************, "duration_str": "77.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.692671, "relative_start": 0.*****************, "end": **********.755995, "relative_end": 9.5367431640625e-07, "duration": 0.***************, "duration_str": "63.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.693953, "relative_start": 0.*****************, "end": **********.694184, "relative_end": **********.694184, "duration": 0.00023102760314941406, "duration_str": "231μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::30c1d878896da9becaa5c742f9c433ec", "start": **********.719223, "relative_start": 0.*****************, "end": **********.719223, "relative_end": **********.719223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.723896, "relative_start": 0.*****************, "end": **********.723896, "relative_end": **********.723896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.725922, "relative_start": 0.11121606826782227, "end": **********.725922, "relative_end": **********.725922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.727496, "relative_start": 0.11278986930847168, "end": **********.727496, "relative_end": **********.727496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6880515bef9fee3b6a53f72c8a43c470", "start": **********.73322, "relative_start": 0.1185140609741211, "end": **********.73322, "relative_end": **********.73322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.736039, "relative_start": 0.12133288383483887, "end": **********.736039, "relative_end": **********.736039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.742542, "relative_start": 0.12783598899841309, "end": **********.742577, "relative_end": **********.742577, "duration": 3.504753112792969e-05, "duration_str": "35μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.755704, "relative_start": 0.14099788665771484, "end": **********.755743, "relative_end": **********.755743, "duration": 3.910064697265625e-05, "duration_str": "39μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5618360, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "__components::30c1d878896da9becaa5c742f9c433ec", "param_count": null, "params": [], "start": **********.719215, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/30c1d878896da9becaa5c742f9c433ec.blade.php__components::30c1d878896da9becaa5c742f9c433ec", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F30c1d878896da9becaa5c742f9c433ec.blade.php&line=1", "ajax": false, "filename": "30c1d878896da9becaa5c742f9c433ec.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.72389, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.725916, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.727491, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::6880515bef9fee3b6a53f72c8a43c470", "param_count": null, "params": [], "start": **********.733215, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/6880515bef9fee3b6a53f72c8a43c470.blade.php__components::6880515bef9fee3b6a53f72c8a43c470", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F6880515bef9fee3b6a53f72c8a43c470.blade.php&line=1", "ajax": false, "filename": "6880515bef9fee3b6a53f72c8a43c470.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.736035, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0011099999999999999, "accumulated_duration_str": "1.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = '4ZNUdoRjoIz4k7SKHmBC7D5wAL2jgQyjeosHDPDb' limit 1", "type": "query", "params": [], "bindings": ["4ZNUdoRjoIz4k7SKHmBC7D5wAL2jgQyjeosHDPDb"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.703399, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 44.144}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.713056, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 44.144, "width_percent": 9.91}, {"sql": "update \"sessions\" set \"payload\" = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiNVZZQjkzaHhmQ0d5U2RTZHZrRmxVeno5V3lGejR5d1RETmZyekZWciI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzE6Imh0dHBzOi8vbGZzbC50ZXN0L2FkbWluL3Byb2ZpbGUiO31zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkSlllSkRxbTJoblhmeVNlazRKZi8xZVdnUU9GTEcwdC42UUFxZkdPaWg2RUtNUEZpV3hnaHEiO30=', \"last_activity\" = **********, \"user_id\" = 1, \"ip_address\" = '127.0.0.1', \"user_agent\" = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where \"id\" = '4ZNUdoRjoIz4k7SKHmBC7D5wAL2jgQyjeosHDPDb'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiNVZZQjkzaHhmQ0d5U2RTZHZrRmxVeno5V3lGejR5d1RETmZyekZWciI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzE6Imh0dHBzOi8vbGZzbC50ZXN0L2FkbWluL3Byb2ZpbGUiO31zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkSlllSkRxbTJoblhmeVNlazRKZi8xZVdnUU9GTEcwdC42UUFxZkdPaWg2RUtNUEZpV3hnaHEiO30=", **********, 1, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "4ZNUdoRjoIz4k7SKHmBC7D5wAL2jgQyjeosHDPDb"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.7548661, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 54.054, "width_percent": 45.946}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.auth.pages.edit-profile #x8unwLl4MxU5qXdDRYtb": "array:4 [\n  \"data\" => array:8 [\n    \"data\" => array:16 [\n      \"id\" => 1\n      \"name\" => \"System User\"\n      \"email\" => \"<EMAIL>\"\n      \"email_verified_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"created_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"updated_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"public_id\" => \"01JZ259ZK8T7A00R9ZC5X9DYEA\"\n      \"slug\" => \"system-user\"\n      \"deleted_at\" => null\n      \"created_by\" => null\n      \"updated_by\" => 1\n      \"deleted_by\" => null\n      \"has_email_authentication\" => false\n      \"password\" => null\n      \"passwordConfirmation\" => null\n      \"currentPassword\" => null\n    ]\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:1 [\n      0 => \"form\"\n    ]\n  ]\n  \"name\" => \"filament.auth.pages.edit-profile\"\n  \"component\" => \"Filament\\Auth\\Pages\\EditProfile\"\n  \"id\" => \"x8unwLl4MxU5qXdDRYtb\"\n]", "filament.livewire.simple-user-menu #paKLXXw43d5cRPL2voLI": "array:4 [\n  \"data\" => array:7 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.simple-user-menu\"\n  \"component\" => \"Filament\\Livewire\\SimpleUserMenu\"\n  \"id\" => \"paKLXXw43d5cRPL2voLI\"\n]", "filament.livewire.notifications #MzZBrsgzZWQoWe52ugop": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#7682\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"MzZBrsgzZWQoWe52ugop\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/admin/profile", "action_name": "filament.admin.auth.profile", "controller_action": "Filament\\Auth\\Pages\\EditProfile", "uri": "GET admin/profile", "controller": "Filament\\Auth\\Pages\\EditProfile@render<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:49-57</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, verified:filament.admin.auth.email-verification.prompt", "duration": "136ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1252 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjVjUlhpNDd3eWhDa0s3QjNxb21taFE9PSIsInZhbHVlIjoiREplYnhIaEpXSGkwRmZIWkJhY09vTXNJaDh1NHpuc0ozckd6bzJyT3J3UEtWeWx0WkdTTGtwMW5mNmFSaUlqNzR6RXNmQmQrMThYMTFWVVRaeFBGR2pJem94Si9EUFh0aW4vdnNHb0l0OWRGU20wRjB5TEhvOGI4TlpFSmFSeGFPSkVZdmdjc09SMk92MHdHUEc0ZVhqK2VuQ25maUtFMVBkS1l5MFRWb0l5OUpYSEI2WVZTWUJFdzludVVzRElLVWsvdy9DbjBvKzVZemFXSEVKMUZSRUIwMy84N0JMTnVTbjlYSzU0Mno4Yz0iLCJtYWMiOiIxNTE0N2MxYzhiZWQ5YWNmMDE5Y2U4Yjc2M2I3MjhhZTNhMDE1YzcyMzJiMjgwMTZiNmZlYmE4ZWIwN2M1NTA4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im50OTB2TERPQXZEOVFCTThHVWNWb0E9PSIsInZhbHVlIjoiSlI1TWh1VnBCQ002dGIxck1QTnA1VUNxU2dqb2ZCWnZ4TVFsYVh4bnlySTdrU1V5dm4rOHJlUXN6ZzliZkZzaXJXaVBVYWxGN0pTaVdremFYYktBQ1AyaVUwQ0RFdHE1M2xzZkFuSExlYklPRXIrNTQrS3JNaUp2SjZGckpkYVIiLCJtYWMiOiI1NWM2NDhhNTllNmFjZDYzZjU5NTVhNmFiOTI3OTQwYjg4YzI5Y2I5NjFkYjEzODgwNzc3Y2NkYjU4ZWZjOTU1IiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6IitHMVZjbm11Qmp4YmtCdENnOE5Bcnc9PSIsInZhbHVlIjoiL3pha0NoakUvVGo0bXF4SUhGd1BEdnp3RDI0dkZDSld0bDZTcHNwQ0FHMFBKc2FYWVl1ckJJbEp5ZnhRM1ZaR2hhNWpxMnpMM01tUGpyN0tVc0dqYmxPT3B5aEpFZjVNUGsxaEZvSnlpT25KdlN4Q3YwRHNRWnNXV09HU29qeUgiLCJtYWMiOiI5ZGI1NjhjODMyMmE3YzRlZjEwY2ZmYjIxNjdjNWE0MzI4YzY2ODUyNDM0MWNmMzgxYzVmMjQ0NzJjZjhmZTRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://lfsl.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1303662754 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|Bw1MCtSuwJvp5hm5Vwls3Q5yU08MR2P1tvkLZacWBy5hLBIUaF8OmqqjbIy1|$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5VYB93hxfCGySdSdvkFlUzz9WyFz4ywTDNfrzFVr</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4ZNUdoRjoIz4k7SKHmBC7D5wAL2jgQyjeosHDPDb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303662754\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1259420907 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 11:29:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IkU2NVlUNEMxc2dNQlhRaDAzV2pKd1E9PSIsInZhbHVlIjoiTkE4YkFmV1ZKRDRnSS9BUlVuNVE1cGJDdWRRVWpEUjgrcjNkNXM2TXl1UHZqWUZxZnp2anBxSGplNThlbEs4cW9QTmxtRFBIaTlMTmxYcDB2UCtiaXBvM01kSWp0Vkg2K21UaEZFV2hwaGp2MXYrczlobThIdjNtQ1c4Z2lydGciLCJtYWMiOiJjZjVhNjAxNTk0ZWRjYTU5N2MzYjU2MTRhYWU3YTkwYWZmNGIzMDY2MWNkNDE3NGE2MDBmMjBiN2ViZTkyYTVlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 13:29:56 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">lfsl_session=eyJpdiI6Ik5NNFE0OENiZWYyVlZPVzQ2bDQ0N3c9PSIsInZhbHVlIjoiMjdZa0JSQzFFOG5XYzB2M1FoVTM1ak4vVDRuS293a0k4RWNLTENrKzJZQTYrTjVVaE9ZU3M1bEFpYit6T3UxL1luclJ5cXRvRTJMNVdlellKeGx5N2wwU1U2ZVNWdmFKTStud2pCTWYzRlVlQ0VKYW1yeWFKOGtrZjZ0Z25pT2YiLCJtYWMiOiI2N2VlYThlZmZhZDYxY2I0YTM5ZTFhZTFlY2U3ZTdhNTUzNDQ1M2E3ZDk3OTQ1ZTE5MWI1YjRlN2Q2YzVjZTI0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 13:29:56 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IkU2NVlUNEMxc2dNQlhRaDAzV2pKd1E9PSIsInZhbHVlIjoiTkE4YkFmV1ZKRDRnSS9BUlVuNVE1cGJDdWRRVWpEUjgrcjNkNXM2TXl1UHZqWUZxZnp2anBxSGplNThlbEs4cW9QTmxtRFBIaTlMTmxYcDB2UCtiaXBvM01kSWp0Vkg2K21UaEZFV2hwaGp2MXYrczlobThIdjNtQ1c4Z2lydGciLCJtYWMiOiJjZjVhNjAxNTk0ZWRjYTU5N2MzYjU2MTRhYWU3YTkwYWZmNGIzMDY2MWNkNDE3NGE2MDBmMjBiN2ViZTkyYTVlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 13:29:56 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">lfsl_session=eyJpdiI6Ik5NNFE0OENiZWYyVlZPVzQ2bDQ0N3c9PSIsInZhbHVlIjoiMjdZa0JSQzFFOG5XYzB2M1FoVTM1ak4vVDRuS293a0k4RWNLTENrKzJZQTYrTjVVaE9ZU3M1bEFpYit6T3UxL1luclJ5cXRvRTJMNVdlellKeGx5N2wwU1U2ZVNWdmFKTStud2pCTWYzRlVlQ0VKYW1yeWFKOGtrZjZ0Z25pT2YiLCJtYWMiOiI2N2VlYThlZmZhZDYxY2I0YTM5ZTFhZTFlY2U3ZTdhNTUzNDQ1M2E3ZDk3OTQ1ZTE5MWI1YjRlN2Q2YzVjZTI0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 13:29:56 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1259420907\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1882861928 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5VYB93hxfCGySdSdvkFlUzz9WyFz4ywTDNfrzFVr</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882861928\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/admin/profile", "action_name": "filament.admin.auth.profile", "controller_action": "Filament\\Auth\\Pages\\EditProfile"}, "badge": null}}