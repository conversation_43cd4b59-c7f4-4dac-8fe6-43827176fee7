{"__meta": {"id": "01JZ2TD34D9G29XNPYCW5YW5C5", "datetime": "2025-07-01 11:01:55", "utime": **********.981285, "method": "GET", "uri": "/livewire/livewire.js?id=df3a17f2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.549307, "end": **********.981291, "duration": 0.43198394775390625, "duration_str": "432ms", "measures": [{"label": "Booting", "start": **********.549307, "relative_start": 0, "end": **********.980227, "relative_end": **********.980227, "duration": 0.***************, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.980234, "relative_start": 0.****************, "end": **********.981292, "relative_end": 9.5367431640625e-07, "duration": 0.0010581016540527344, "duration_str": "1.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.980747, "relative_start": 0.****************, "end": **********.980843, "relative_end": **********.980843, "duration": 9.608268737792969e-05, "duration_str": "96μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.981162, "relative_start": 0.****************, "end": **********.981191, "relative_end": **********.981191, "duration": 2.8848648071289062e-05, "duration_str": "29μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.981196, "relative_start": 0.****************, "end": **********.981205, "relative_end": **********.981205, "duration": 9.059906005859375e-06, "duration_str": "9μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 2121504, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/livewire.js?id=df3a17f2", "action_name": "generated::HqGy7hg1J9PrtSja", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "430ms", "peak_memory": "4MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2017098272 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">df3a17f2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017098272\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1858446148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1858446148\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1252640243 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1170 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlZBL0I4c3lZOXF4S3lHRzEzQ3pKTnc9PSIsInZhbHVlIjoiRUh3QzJFUzU5YitDSFNBSVE4SStuTXJ6RDcxZm1qKzlCVEdBMFRvdGZGWDJaZld6RFk3WVd1ZldGVGhQRnVob2ZxdHBJSC9ueDQ4WmZKdUR5dlNBYmlVSG5KbU1ldEZtd1VCaGhLYVluTnp1ZWV3NU5iQkFqVDM2Y20zVU1oRGkwUHRaRTAvZXpKcWZHQkZPWjVHeW56VXREWGlPT21aN25iZHorQ1BaWWdjPSIsIm1hYyI6ImRiNjI1ZDdjMjM4NGQ5YzMwMDhhZTUzMjY5OTlmNDU0YjdhYjk0OGNjNTVjNDhkZjNkNDQxYTViNDJkYzY1ZDciLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6Imt3ZWhWdWZNZkV2c0xZRGd0a050R2c9PSIsInZhbHVlIjoiZzRMWkJvbzkrUGh5RWJORUsvc2lDMEJMaEFTVFc2VFp3dTVNWVg5MUtVbVRqYm9Xb3dDMzhraEtMdXVUQ3Zkc2d4NkI1cGNkM1d0K255ZnNkZURvS21VK1B6SnZsVld2V1d5MkRYalFQUTZrTitMVUhKQ0YrdEUwRWY4SUg2TTUiLCJtYWMiOiIzNTljZmMyZmQyMDNjZjQzNjRlOGNlNjYyZDVlZjkwMWE1ODJiMDI5ZjgwNzk5OWZkNTcwZjYxZGUwNzQ4MTZjIiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6IjNlRTZHR2ZyNElNWjJUdmN5Vlg0L1E9PSIsInZhbHVlIjoiaE9JSG9ZbUtyYjJPU2IxSEZWdW13QnJ4bktzcUZSYUdQWmhneG9WVEllVGNiRXEySE9LQ01VdWFKOGNhQ2loOEVHTGJqNTlzOWtLejlxc0VRZEQzZHZ2UGYzMUlIQ05JUFBLNXEvWUVpUjhLT3kzVVdaQUZCTUZHanZGTU9hd3QiLCJtYWMiOiI4MTcwN2FlYWU1NzNmMDgyZmRlNTIwODQ3N2YyODYxOGM0Y2QyYTFkZDhlOTI3MWE5MDlhMGY5NjZlZmMyOWVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252640243\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1554092356 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"400 characters\">eyJpdiI6IlZBL0I4c3lZOXF4S3lHRzEzQ3pKTnc9PSIsInZhbHVlIjoiRUh3QzJFUzU5YitDSFNBSVE4SStuTXJ6RDcxZm1qKzlCVEdBMFRvdGZGWDJaZld6RFk3WVd1ZldGVGhQRnVob2ZxdHBJSC9ueDQ4WmZKdUR5dlNBYmlVSG5KbU1ldEZtd1VCaGhLYVluTnp1ZWV3NU5iQkFqVDM2Y20zVU1oRGkwUHRaRTAvZXpKcWZHQkZPWjVHeW56VXREWGlPT21aN25iZHorQ1BaWWdjPSIsIm1hYyI6ImRiNjI1ZDdjMjM4NGQ5YzMwMDhhZTUzMjY5OTlmNDU0YjdhYjk0OGNjNTVjNDhkZjNkNDQxYTViNDJkYzY1ZDciLCJ0YWciOiIifQ==</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Imt3ZWhWdWZNZkV2c0xZRGd0a050R2c9PSIsInZhbHVlIjoiZzRMWkJvbzkrUGh5RWJORUsvc2lDMEJMaEFTVFc2VFp3dTVNWVg5MUtVbVRqYm9Xb3dDMzhraEtMdXVUQ3Zkc2d4NkI1cGNkM1d0K255ZnNkZURvS21VK1B6SnZsVld2V1d5MkRYalFQUTZrTitMVUhKQ0YrdEUwRWY4SUg2TTUiLCJtYWMiOiIzNTljZmMyZmQyMDNjZjQzNjRlOGNlNjYyZDVlZjkwMWE1ODJiMDI5ZjgwNzk5OWZkNTcwZjYxZGUwNzQ4MTZjIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjNlRTZHR2ZyNElNWjJUdmN5Vlg0L1E9PSIsInZhbHVlIjoiaE9JSG9ZbUtyYjJPU2IxSEZWdW13QnJ4bktzcUZSYUdQWmhneG9WVEllVGNiRXEySE9LQ01VdWFKOGNhQ2loOEVHTGJqNTlzOWtLejlxc0VRZEQzZHZ2UGYzMUlIQ05JUFBLNXEvWUVpUjhLT3kzVVdaQUZCTUZHanZGTU9hd3QiLCJtYWMiOiI4MTcwN2FlYWU1NzNmMDgyZmRlNTIwODQ3N2YyODYxOGM0Y2QyYTFkZDhlOTI3MWE5MDlhMGY5NjZlZmMyOWVjIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554092356\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1453671656 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 11:01:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Apr 2025 22:26:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 11:01:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453671656\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1982822220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1982822220\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/livewire.js?id=df3a17f2", "action_name": "generated::HqGy7hg1J9PrtSja", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}