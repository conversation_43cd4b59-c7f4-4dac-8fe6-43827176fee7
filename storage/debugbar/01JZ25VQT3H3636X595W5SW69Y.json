{"__meta": {"id": "01JZ25VQT3H3636X595W5SW69Y", "datetime": "2025-07-01 05:02:55", "utime": **********.811243, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.577206, "end": **********.811249, "duration": 0.23404312133789062, "duration_str": "234ms", "measures": [{"label": "Booting", "start": **********.577206, "relative_start": 0, "end": **********.703808, "relative_end": **********.703808, "duration": 0.****************, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.703823, "relative_start": 0.****************, "end": **********.811249, "relative_end": 0, "duration": 0.*****************, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.70574, "relative_start": 0.*****************, "end": **********.7061, "relative_end": **********.7061, "duration": 0.0003600120544433594, "duration_str": "360μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::30c1d878896da9becaa5c742f9c433ec", "start": **********.753988, "relative_start": 0.*****************, "end": **********.753988, "relative_end": **********.753988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.786033, "relative_start": 0.*****************, "end": **********.786033, "relative_end": **********.786033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.791833, "relative_start": 0.21462702751159668, "end": **********.791833, "relative_end": **********.791833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.792902, "relative_start": 0.21569609642028809, "end": **********.792902, "relative_end": **********.792902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.807012, "relative_start": 0.22980618476867676, "end": **********.807012, "relative_end": **********.807012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.810799, "relative_start": 0.23359298706054688, "end": **********.811101, "relative_end": **********.811101, "duration": 0.0003020763397216797, "duration_str": "302μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7374352, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 5, "nb_templates": 5, "templates": [{"name": "__components::30c1d878896da9becaa5c742f9c433ec", "param_count": null, "params": [], "start": **********.753975, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/30c1d878896da9becaa5c742f9c433ec.blade.php__components::30c1d878896da9becaa5c742f9c433ec", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F30c1d878896da9becaa5c742f9c433ec.blade.php&line=1", "ajax": false, "filename": "30c1d878896da9becaa5c742f9c433ec.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.786027, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.791827, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.792898, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.807006, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00041, "accumulated_duration_str": "410μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = 'UHHHxcVErMSEx54Y0bWPgM0w7wDqwE1jtlIpN6pe' limit 1", "type": "query", "params": [], "bindings": ["UHHHxcVErMSEx54Y0bWPgM0w7wDqwE1jtlIpN6pe"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.7239969, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 60.976}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.747066, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 60.976, "width_percent": 39.024}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.auth.pages.edit-profile #XtuZdMRdQW91I6mgSZz7": "array:4 [\n  \"data\" => array:8 [\n    \"data\" => array:16 [\n      \"id\" => 1\n      \"name\" => \"System User\"\n      \"email\" => \"<EMAIL>\"\n      \"email_verified_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"created_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"updated_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"public_id\" => \"01JZ259ZK8T7A00R9ZC5X9DYEA\"\n      \"slug\" => \"adam-mason-2680\"\n      \"deleted_at\" => null\n      \"created_by\" => null\n      \"updated_by\" => null\n      \"deleted_by\" => null\n      \"has_email_authentication\" => false\n      \"password\" => null\n      \"passwordConfirmation\" => null\n      \"currentPassword\" => null\n    ]\n    \"mountedActions\" => array:4 [\n      0 => array:3 [\n        \"name\" => \"setUpAppAuthentication\"\n        \"arguments\" => array:1 [\n          \"encrypted\" => \"eyJpdiI6IkhMaDZXSThTMktYMXJYTmdJaGo5SXc9PSIsInZhbHVlIjoiT2t5bGo1SzgzanRXeHlxckpnZ1pYQzhDNElURThwZkpFNS95NEUxQ0RBb0NtRmxnSTlKbGJrMnRRTW1MMHZXTUVQR0FqQXQyLzRYS3liT3lnZUxkMXovK3BBUTllK05GcnkyUk9vd0QwWkFBM0VkckpYTTdHeGdMZXdCa2ZsVnJHSEZLcW9jRjBpWCtjYnVmZkdOSWFsazJ6d0hpN1VBSVFacFZrWlFraGNNdkhyTDFMMEE0cXBoRnYva1Y5SGJLeEZoSjlxQ3hhUWQwTkdrTEROWkVTbmdWc3hYZzJjYWVZZmN2NkZDRjdKMFEwUUVuQkE1cTF1ZGk2ZXFRMzkxd3NkZE8wMnJhU3VHbG9hWFRZakc1TnZHRCtnTE81N1BVV1VWQkdrZFFpQlJBeEFjRTlOTGNCcmZlU0xPeHJ1aG9aV3hmenhzeHN3QXNWdVQ0SkRsbHdpOGc1RUJjYXpoUTNubktJeE5pemlOYmNwbUdDZ2pJSmszZmNHUHBLNjVkSFVIb1FLZ1RrT3hxUzBlQ3VONU0rL2xKaTBFOTR1ZTBmVmh3Uk1QNmtTRjI5TmxiT3VpUlpibGloQTRLYVBBTE9KVWFCTk42R0VFRDcvdUw2MkljV2VacFNBa1JPaG8xckswWHMxZWpCUzBxQmY5Mys0cXVxZXY5SVdOYjlUcWgrdVY5MTNIUTJ0ZExORmVWY1VvakxNclpjQTVwNXR0T2w2aUM2bDRoQzEyWGNubXhtNlpodU1WV3YwS3VNS3QzIiwibWFjIjoiNGEyNmNmMzUzNDgxZDhlYjk1YWViOWEzOTAzODJjOWY1NGRmMDliYmRjNWQwOTQxZDQ1YjVmNWJmYmEyN2U0NiIsInRhZyI6IiJ9\"\n        ]\n        \"context\" => array:1 [\n          \"schemaComponent\" => \"content.app\"\n        ]\n      ]\n      1 => array:3 [\n        \"name\" => \"setUpEmailAuthentication\"\n        \"arguments\" => []\n        \"context\" => array:1 [\n          \"schemaComponent\" => \"content.email_code\"\n        ]\n      ]\n      2 => array:3 [\n        \"name\" => \"setUpEmailAuthentication\"\n        \"arguments\" => []\n        \"context\" => array:1 [\n          \"schemaComponent\" => \"content.email_code\"\n        ]\n      ]\n      3 => array:3 [\n        \"name\" => \"setUpAppAuthentication\"\n        \"arguments\" => array:1 [\n          \"encrypted\" => \"eyJpdiI6Im1OQ3RXUy9HczJzZHFEcklkSWIrM1E9PSIsInZhbHVlIjoidDBiWVFKcjZnelc1T2hjVEE2cFhFZ21GdWtjZzROUzdhQkhvUGwwaHZDbmNhUzRaeEJuOGJSS3l1dVJ3TnY2VVBBd3dXVjgraEhpRStTOTk2TTdUL3lOTU00SUs4eWZVL2pKT3MwZ0dyU21OK3NpVHZFekZ0TUVtdHNMNUNqNFVWY0w5a3hnWmlqOWtyVXVKRHRqNjhMZ3ViaDdQR2lZZmFBN3BDZmZqbU1UaCtkRlA2dHZhVjZRSUNvc1Q2WUdpa1ZkNElHVmhLV3kxcWo1UUJWU01RbXlHTXpyM3JyakM5dTdqTXZyWWN4bGNFUzk2Sk5jeHpRZGo2eGtWVEg2MXQ3UklsRlE4THdBMjllRGtaRk1HL29pT3A4VVh5OWtncllMT3ZaUXhnYkdFUXUxSHVFWEthOHFmL0RHN0oxaG84R2srV0pROHVHT1FLRllUL0tZYTZrOFlaYTRsR0dDQitWQjhPS0FqWk5pV3dOdGIzSkVYd2laY2NwM3ZiUEorNk5rOVN4Qk1wdHZuRWRJOVVwTWY0TVg5MDNjYk92c1lIb0w3ZDV2R3ErQjkwdHJIMmNZNGZadW5vY1hWU1FoVEJ5Z096MW02MXRuTXA0ODFFRVhuaUNUYUxrZlIxVVNqbVBIRVBlQVAxK1pUSGNoVGR1eWNleDFmY1V6cWwwK1g1RTJFTVlsR1c3ajdzTjFORnVlWmx3RVdsZWhHMnJZWlJmcVVXUzV4TXplQ1RsTjJiVlcrVVdKZ0crbHpmbnFSIiwibWFjIjoiZTVjOWZlMTJhMGM3OWZiZTk1MGY2ZjhkNzExNWYyOGJkZGNiYTNjNDAwZDI0MTYwYzEzYWUzMGRhNmQ5NGVlZCIsInRhZyI6IiJ9\"\n        ]\n        \"context\" => array:1 [\n          \"schemaComponent\" => \"content.app\"\n        ]\n      ]\n    ]\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:2 [\n      0 => \"form\"\n      1 => \"content\"\n    ]\n  ]\n  \"name\" => \"filament.auth.pages.edit-profile\"\n  \"component\" => \"Filament\\Auth\\Pages\\EditProfile\"\n  \"id\" => \"XtuZdMRdQW91I6mgSZz7\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Filament\\Auth\\Pages\\EditProfile@mountAction<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=89\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=89\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:89-164</a>", "middleware": "web", "duration": "227ms", "peak_memory": "10MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-105756661 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-105756661\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-305691321 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">En0ESbCdOv8mGck1kPu5UTU1yKyFhqCFYln5SFSX</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"2363 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;System User&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;email_verified_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;created_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;public_id&quot;:&quot;01JZ259ZK8T7A00R9ZC5X9DYEA&quot;,&quot;slug&quot;:&quot;adam-mason-2680&quot;,&quot;deleted_at&quot;:null,&quot;created_by&quot;:null,&quot;updated_by&quot;:null,&quot;deleted_by&quot;:null,&quot;has_email_authentication&quot;:false,&quot;password&quot;:null,&quot;passwordConfirmation&quot;:null,&quot;currentPassword&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[[{&quot;name&quot;:&quot;setUpAppAuthentication&quot;,&quot;arguments&quot;:[{&quot;encrypted&quot;:&quot;eyJpdiI6IkhMaDZXSThTMktYMXJYTmdJaGo5SXc9PSIsInZhbHVlIjoiT2t5bGo1SzgzanRXeHlxckpnZ1pYQzhDNElURThwZkpFNS95NEUxQ0RBb0NtRmxnSTlKbGJrMnRRTW1MMHZXTUVQR0FqQXQyLzRYS3liT3lnZUxkMXovK3BBUTllK05GcnkyUk9vd0QwWkFBM0VkckpYTTdHeGdMZXdCa2ZsVnJHSEZLcW9jRjBpWCtjYnVmZkdOSWFsazJ6d0hpN1VBSVFacFZrWlFraGNNdkhyTDFMMEE0cXBoRnYva1Y5SGJLeEZoSjlxQ3hhUWQwTkdrTEROWkVTbmdWc3hYZzJjYWVZZmN2NkZDRjdKMFEwUUVuQkE1cTF1ZGk2ZXFRMzkxd3NkZE8wMnJhU3VHbG9hWFRZakc1TnZHRCtnTE81N1BVV1VWQkdrZFFpQlJBeEFjRTlOTGNCcmZlU0xPeHJ1aG9aV3hmenhzeHN3QXNWdVQ0SkRsbHdpOGc1RUJjYXpoUTNubktJeE5pemlOYmNwbUdDZ2pJSmszZmNHUHBLNjVkSFVIb1FLZ1RrT3hxUzBlQ3VONU0rL2xKaTBFOTR1ZTBmVmh3Uk1QNmtTRjI5TmxiT3VpUlpibGloQTRLYVBBTE9KVWFCTk42R0VFRDcvdUw2MkljV2VacFNBa1JPaG8xckswWHMxZWpCUzBxQmY5Mys0cXVxZXY5SVdOYjlUcWgrdVY5MTNIUTJ0ZExORmVWY1VvakxNclpjQTVwNXR0T2w2aUM2bDRoQzEyWGNubXhtNlpodU1WV3YwS3VNS3QzIiwibWFjIjoiNGEyNmNmMzUzNDgxZDhlYjk1YWViOWEzOTAzODJjOWY1NGRmMDliYmRjNWQwOTQxZDQ1YjVmNWJmYmEyN2U0NiIsInRhZyI6IiJ9&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;context&quot;:[{&quot;schemaComponent&quot;:&quot;content.app&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;name&quot;:&quot;setUpEmailAuthentication&quot;,&quot;arguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;context&quot;:[{&quot;schemaComponent&quot;:&quot;content.email_code&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;name&quot;:&quot;setUpEmailAuthentication&quot;,&quot;arguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;context&quot;:[{&quot;schemaComponent&quot;:&quot;content.email_code&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;defaultActionContext&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areSchemaStateUpdateHooksDisabledForTesting&quot;:false,&quot;discoveredSchemaNames&quot;:[[&quot;form&quot;,&quot;content&quot;],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;XtuZdMRdQW91I6mgSZz7&quot;,&quot;name&quot;:&quot;filament.auth.pages.edit-profile&quot;,&quot;path&quot;:&quot;admin\\/profile&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;167e7f6c3d1c7f3bdd6105b6fa9af132bf86214af03145d0c42dcf51f56eee10&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">mountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">setUpAppAuthentication</span>\"\n            <span class=sf-dump-index>1</span> => []\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>schemaComponent</span>\" => \"<span class=sf-dump-str title=\"11 characters\">content.app</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305691321\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1238198683 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1170 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlZBL0I4c3lZOXF4S3lHRzEzQ3pKTnc9PSIsInZhbHVlIjoiRUh3QzJFUzU5YitDSFNBSVE4SStuTXJ6RDcxZm1qKzlCVEdBMFRvdGZGWDJaZld6RFk3WVd1ZldGVGhQRnVob2ZxdHBJSC9ueDQ4WmZKdUR5dlNBYmlVSG5KbU1ldEZtd1VCaGhLYVluTnp1ZWV3NU5iQkFqVDM2Y20zVU1oRGkwUHRaRTAvZXpKcWZHQkZPWjVHeW56VXREWGlPT21aN25iZHorQ1BaWWdjPSIsIm1hYyI6ImRiNjI1ZDdjMjM4NGQ5YzMwMDhhZTUzMjY5OTlmNDU0YjdhYjk0OGNjNTVjNDhkZjNkNDQxYTViNDJkYzY1ZDciLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkxEU1NLYjRxdjJMcHNwdUdnY1VZRGc9PSIsInZhbHVlIjoiRWprbHFwWUg4cC9lZHRnN3FhdzRneVd5L2NYaGdxbGZjaGFreXVSckVQZko0Mk8rMmRLM0hYU0tOanJDU21Mb0xOd2c4amdFOUp4ZDNzbWc2RHc2RVRqeXFPR3VxNGRTL2RGL1ZaSG9zbzhObVNYUXBxaFoxUVBDbFJMbGVSdXkiLCJtYWMiOiIyNTEwNDBhNmJhODg5ZjE3YzFiNTY4NmMyMWNkNTgwOGJlN2QzM2IzMmFmZDVjMjY3YTFiNDdjOTlkNmExYzM4IiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6IjEvRjRock5tNHhZbUczcW9QdmluM1E9PSIsInZhbHVlIjoiUWUrOXB1WUZyeXE3TnhUc0Qyamo4WU1vUEg0U1duaU52cUp0eDN4UjFxK3FDQ3pOTGM0eFV0YUVQeFVCb29FdjhUalllZ3VzcVQvdVZSdnl6cEN3b0V4SEpMMTNnRlVTT3djNTd2OEZER2VJQjJpTmx4emRTck92OGZ1cndHUEMiLCJtYWMiOiJhNDg5YWY2ZTg0ZTg3MDdhZDliMWRhNWI1YzIxZTIxYTI5YjAyZDU2Njg3YmI0NWIwNzBkZGQ5ZmMwMjczYjQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://lfsl.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2773</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238198683\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2099093756 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"73 characters\">1|i7KPyT5afz|$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">En0ESbCdOv8mGck1kPu5UTU1yKyFhqCFYln5SFSX</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UHHHxcVErMSEx54Y0bWPgM0w7wDqwE1jtlIpN6pe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099093756\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-82490275 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 05:02:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82490275\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-836868425 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">En0ESbCdOv8mGck1kPu5UTU1yKyFhqCFYln5SFSX</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>filament_email_authentication_code</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$MwSJhC/4xRRmpasxu6d5u.MsPbUSzjVIgy3Oc4XJwMNH5kepOLCGK</span>\"\n  \"<span class=sf-dump-key>filament_email_authentication_code_expires_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\CarbonImmutable @1751346401\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CarbonImmutable @1751346401</span></span> {<a class=sf-dump-ref>#1272</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000004f80000000000000000</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\CarbonImmutable`\">clock</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Tuesday, July 1, 2025\n+ 00:03:45.565905 from now\nDST Off\">2025-07-01 05:06:41.378154 UTC (+00:00)</span>\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836868425\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}