{"__meta": {"id": "01JZ2TDPR7VZCH78R62PVX994V", "datetime": "2025-07-01 11:02:16", "utime": **********.144585, "method": "POST", "uri": "/admin/logout", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.903319, "end": **********.144596, "duration": 0.24127721786499023, "duration_str": "241ms", "measures": [{"label": "Booting", "start": **********.903319, "relative_start": 0, "end": **********.944705, "relative_end": **********.944705, "duration": 0.*****************, "duration_str": "41.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.944711, "relative_start": 0.*****************, "end": **********.144596, "relative_end": 0, "duration": 0.*****************, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.945388, "relative_start": 0.042069196701049805, "end": **********.945542, "relative_end": **********.945542, "duration": 0.00015401840209960938, "duration_str": "154μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.010764, "relative_start": 0.*****************, "end": **********.011002, "relative_end": **********.011002, "duration": 0.00023818016052246094, "duration_str": "238μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.071032, "relative_start": 0.*****************, "end": **********.071079, "relative_end": **********.071079, "duration": 4.696846008300781e-05, "duration_str": "47μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 2673872, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 9, "nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00259, "accumulated_duration_str": "2.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = 'cf22qcd3LuPSlGDUeLugkfooDUJm31Ahb6B2c0cH' limit 1", "type": "query", "params": [], "bindings": ["cf22qcd3LuPSlGDUeLugkfooDUJm31Ahb6B2c0cH"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.953036, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 5.019}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.959386, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 5.019, "width_percent": 3.861}, {"sql": "select exists(select * from \"users\" where \"slug\" = 'system-user' and \"id\" != 1) as \"exists\"", "type": "query", "params": [], "bindings": ["system-user", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-sluggable/src/HasSlug.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-sluggable/src/HasSlug.php", "line": 171}, {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-sluggable/src/HasSlug.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-sluggable/src/HasSlug.php", "line": 135}, {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-sluggable/src/HasSlug.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-sluggable/src/HasSlug.php", "line": 82}, {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-sluggable/src/HasSlug.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-sluggable/src/HasSlug.php", "line": 65}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-sluggable/src/HasSlug.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-sluggable/src/HasSlug.php", "line": 22}], "start": **********.966776, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasSlug.php:171", "source": {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-sluggable/src/HasSlug.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-sluggable/src/HasSlug.php", "line": 171}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Fspatie%2Flaravel-sluggable%2Fsrc%2FHasSlug.php&line=171", "ajax": false, "filename": "HasSlug.php", "line": "171"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 8.88, "width_percent": 16.602}, {"sql": "update \"users\" set \"remember_token\" = 'Bw1MCtSuwJvp5hm5Vwls3Q5yU08MR2P1tvkLZacWBy5hLBIUaF8OmqqjbIy1', \"slug\" = 'system-user', \"updated_by\" = 1 where \"id\" = 1", "type": "query", "params": [], "bindings": ["Bw1MCtSuwJvp5hm5Vwls3Q5yU08MR2P1tvkLZacWBy5hLBIUaF8OmqqjbIy1", "system-user", 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 100}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 700}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 630}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Auth/Http/Controllers/LogoutController.php", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Auth/Http/Controllers/LogoutController.php", "line": 12}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 46}], "start": **********.978571, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:100", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=100", "ajax": false, "filename": "EloquentUserProvider.php", "line": "100"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 25.483, "width_percent": 21.622}, {"sql": "select * from \"users\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/Traits/LogsActivity.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-activitylog/src/Traits/LogsActivity.php", "line": 276}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/Traits/LogsActivity.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-activitylog/src/Traits/LogsActivity.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 100}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 700}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 630}], "start": **********.985614, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "LogsActivity.php:276", "source": {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-activitylog/src/Traits/LogsActivity.php", "file": "/Users/<USER>/Herd/lfsl/vendor/spatie/laravel-activitylog/src/Traits/LogsActivity.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Fspatie%2Flaravel-activitylog%2Fsrc%2FTraits%2FLogsActivity.php&line=276", "ajax": false, "filename": "LogsActivity.php", "line": "276"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 47.104, "width_percent": 3.861}, {"sql": "delete from \"sessions\" where \"id\" = 'cf22qcd3LuPSlGDUeLugkfooDUJm31Ahb6B2c0cH'", "type": "query", "params": [], "bindings": ["cf22qcd3LuPSlGDUeLugkfooDUJm31Ahb6B2c0cH"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 268}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 608}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 583}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Auth/Http/Controllers/LogoutController.php", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Auth/Http/Controllers/LogoutController.php", "line": 14}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 46}], "start": **********.009458, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:268", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 268}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=268", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "268"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 50.965, "width_percent": 18.919}, {"sql": "select * from \"sessions\" where \"id\" = 'NrOurcPmneK1fGK1tox6KEz9Do21e5GOwDQpDGFq' limit 1", "type": "query", "params": [], "bindings": ["NrOurcPmneK1fGK1tox6KEz9Do21e5GOwDQpDGFq"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 135}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.0550382, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 69.884, "width_percent": 3.475}, {"sql": "insert into \"sessions\" (\"payload\", \"last_activity\", \"user_id\", \"ip_address\", \"user_agent\", \"id\") values ('YToyOntzOjY6Il90b2tlbiI7czo0MDoiNGU4M1pMa1E3Y0Z4a2dGckdjcVBuOEpmQzE4enk3c1c1SE9nbjBMZCI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', **********, null, '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'NrOurcPmneK1fGK1tox6KEz9Do21e5GOwDQpDGFq')", "type": "query", "params": [], "bindings": ["YToyOntzOjY6Il90b2tlbiI7czo0MDoiNGU4M1pMa1E3Y0Z4a2dGckdjcVBuOEpmQzE4enk3c1c1SE9nbjBMZCI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==", **********, null, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "NrOurcPmneK1fGK1tox6KEz9Do21e5GOwDQpDGFq"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 157}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 141}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.070114, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:157", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 157}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=157", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "157"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 73.359, "width_percent": 22.78}, {"sql": "select * from \"sessions\" where \"id\" = 'NrOurcPmneK1fGK1tox6KEz9Do21e5GOwDQpDGFq' limit 1", "type": "query", "params": [], "bindings": ["NrOurcPmneK1fGK1tox6KEz9Do21e5GOwDQpDGFq"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 21, "namespace": null, "name": "vendor/php-debugbar/php-debugbar/src/DebugBar/DebugBar.php", "file": "/Users/<USER>/Herd/lfsl/vendor/php-debugbar/php-debugbar/src/DebugBar/DebugBar.php", "line": 446}], "start": **********.1441882, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 96.139, "width_percent": 3.861}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://lfsl.test/admin/logout", "action_name": "filament.admin.auth.logout", "controller_action": "Filament\\Auth\\Http\\Controllers\\LogoutController", "uri": "POST admin/logout", "controller": "Filament\\Auth\\Http\\Controllers\\LogoutController", "prefix": "admin", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "233ms", "peak_memory": "4MB", "response": "Redirect to https://lfsl.test/admin/login", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FsmClCh9x07ImBLKx91SMqDGuymiGOhFsCiVH7yU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1170 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlZBL0I4c3lZOXF4S3lHRzEzQ3pKTnc9PSIsInZhbHVlIjoiRUh3QzJFUzU5YitDSFNBSVE4SStuTXJ6RDcxZm1qKzlCVEdBMFRvdGZGWDJaZld6RFk3WVd1ZldGVGhQRnVob2ZxdHBJSC9ueDQ4WmZKdUR5dlNBYmlVSG5KbU1ldEZtd1VCaGhLYVluTnp1ZWV3NU5iQkFqVDM2Y20zVU1oRGkwUHRaRTAvZXpKcWZHQkZPWjVHeW56VXREWGlPT21aN25iZHorQ1BaWWdjPSIsIm1hYyI6ImRiNjI1ZDdjMjM4NGQ5YzMwMDhhZTUzMjY5OTlmNDU0YjdhYjk0OGNjNTVjNDhkZjNkNDQxYTViNDJkYzY1ZDciLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkdHcUE3OTNXQXVyTG5mVTYrMUl2WXc9PSIsInZhbHVlIjoicC9MSlFkM3d1cWJMNEdLdlRjMEVNVGIxNGdEb2wwQWtDQXU3L2hWQTN6Y2prQlJsQzZvbEZyWFpESE1iTEdXRkMyZlpxV3kvOWdJZ29HTkF2eHk3NXducE5kcHorTEVOZWNPUzdCQTlRK0ZnSTdIRXgyQWlEenBsT1QrWWZlaTQiLCJtYWMiOiI2OTNlYmRlODFhMWEwMWQxNTc5Njk2ZjMxNzM4MGMyODcyYzExMTRmMWIwYmZmNWNlNGMwNzc4Y2UxMmNhNGY3IiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6Ik5scWZubEdsaVM1L3IrdW1Wb215YXc9PSIsInZhbHVlIjoid0ZGZENDTGExL3JOL3RyV1RVQk5nTnRmZ0hUQjEyM1RuVkFSaCtMaUJQdWc3Rjl1aHdLakJtTmpaS1MwZGlkbGU5K1MrM3J0aWNVbVNEMkxWd2RjMzVBNHNwRHNmZlJFT2RqWmJqZkZyZE1WTFdqOXVEU1M0MjBncURYZ3Y5SnkiLCJtYWMiOiJkNWNmMzIzMjZlNTI5MDk0YzI3NTE1MTBhZmRlMmRiZjhmZGVkOGI1M2VhZTc4ZDQxNzYxNTc0MjBjOTVlZjg5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://lfsl.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1690703272 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"73 characters\">1|i7KPyT5afz|$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FsmClCh9x07ImBLKx91SMqDGuymiGOhFsCiVH7yU</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cf22qcd3LuPSlGDUeLugkfooDUJm31Ahb6B2c0cH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1690703272\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1402021193 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 11:02:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">https://lfsl.test/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6Ik5aZmo3YmprbDAxb2xjUTFWK3Y0WXc9PSIsInZhbHVlIjoiYW94bnBpVkF2YkNmVlorK3RYRU00aE45MURDamNJd2ZYeWxiNlFoY2x2Y1B6dVg5aXMralcxNGdMb2ZCWEVWU3B0Rnc2ZktqU21DKzN1L2FlREIzSC8xQWFNeWh1YmhBSXN2clZkcENVVCtNQk9Gd3JpVGRsSEhzVTd5dnVwQkoiLCJtYWMiOiI0Y2FhNTRhYjgyOWRkOTE5ODgyMTI4Yjg1NmUwNDBjMzJlMDIyMzAyMzQ2MGU0YWU5NGQ5Y2IzYjg5NWRiMGVlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 13:02:16 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">lfsl_session=eyJpdiI6Imk5R3RaRTZtN3JYZGhtckNZMkR3NXc9PSIsInZhbHVlIjoiazI1dTh5V1VlbjhleWVoUFo1MGpkRlRzSFpOdCtiYzAzSmd4aTNKUGQ2RXhaQ0UzbHBrZ1BwRmZsczNtUVR5dDlka2dZamxiU3JtSjV2L21MdWZCMGg0VE1QNWx4RUluaDVJL2pvVXhhVVVkM0xFbEk1K0h3dC9Ma0dBNFFaTWgiLCJtYWMiOiIwNmUwMjIxNDE3NzQyYmE2YTAwNTQ2YjdhODk3MmY2NTEwYjg1ZGMxOTljM2E5MzYzODRmNjQ5NGFjY2E0MDhhIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 13:02:16 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"404 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJKSWlQUElkSHA0ZDR3VTBiTElad1E9PSIsInZhbHVlIjoiemVyb09uTHBLRXhYV2RoVjRKOEVrZ0d1WXZaN21OUjdpb1dkVW1zZFY2UXc4U1hoaXZPOU9CYzV5aVkxSXFCZCIsIm1hYyI6IjQxNmQ0YThkODA2YWZiM2EyNTFjOGVkYmExZWY2NGI4MjI5YWQ5ZDViYzRjMzFlZWEwNDU5NmVkMGJjNTNlMjMiLCJ0YWciOiIifQ%3D%3D; expires=Thu, 02 Jul 2020 11:02:15 GMT; Max-Age=0; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6Ik5aZmo3YmprbDAxb2xjUTFWK3Y0WXc9PSIsInZhbHVlIjoiYW94bnBpVkF2YkNmVlorK3RYRU00aE45MURDamNJd2ZYeWxiNlFoY2x2Y1B6dVg5aXMralcxNGdMb2ZCWEVWU3B0Rnc2ZktqU21DKzN1L2FlREIzSC8xQWFNeWh1YmhBSXN2clZkcENVVCtNQk9Gd3JpVGRsSEhzVTd5dnVwQkoiLCJtYWMiOiI0Y2FhNTRhYjgyOWRkOTE5ODgyMTI4Yjg1NmUwNDBjMzJlMDIyMzAyMzQ2MGU0YWU5NGQ5Y2IzYjg5NWRiMGVlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 13:02:16 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">lfsl_session=eyJpdiI6Imk5R3RaRTZtN3JYZGhtckNZMkR3NXc9PSIsInZhbHVlIjoiazI1dTh5V1VlbjhleWVoUFo1MGpkRlRzSFpOdCtiYzAzSmd4aTNKUGQ2RXhaQ0UzbHBrZ1BwRmZsczNtUVR5dDlka2dZamxiU3JtSjV2L21MdWZCMGg0VE1QNWx4RUluaDVJL2pvVXhhVVVkM0xFbEk1K0h3dC9Ma0dBNFFaTWgiLCJtYWMiOiIwNmUwMjIxNDE3NzQyYmE2YTAwNTQ2YjdhODk3MmY2NTEwYjg1ZGMxOTljM2E5MzYzODRmNjQ5NGFjY2E0MDhhIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 13:02:16 GMT; path=/; secure; httponly</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"379 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJKSWlQUElkSHA0ZDR3VTBiTElad1E9PSIsInZhbHVlIjoiemVyb09uTHBLRXhYV2RoVjRKOEVrZ0d1WXZaN21OUjdpb1dkVW1zZFY2UXc4U1hoaXZPOU9CYzV5aVkxSXFCZCIsIm1hYyI6IjQxNmQ0YThkODA2YWZiM2EyNTFjOGVkYmExZWY2NGI4MjI5YWQ5ZDViYzRjMzFlZWEwNDU5NmVkMGJjNTNlMjMiLCJ0YWciOiIifQ%3D%3D; expires=Thu, 02-Jul-2020 11:02:15 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402021193\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1399206644 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4e83ZLkQ7cFxkgFrGcqPn8JfC18zy7sW5HOgn0Ld</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399206644\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://lfsl.test/admin/logout", "action_name": "filament.admin.auth.logout", "controller_action": "Filament\\Auth\\Http\\Controllers\\LogoutController"}, "badge": "302 Found"}}