{"__meta": {"id": "01JZ2W0PY0BB6QNX5JZ8V2X5TQ", "datetime": "2025-07-01 11:30:07", "utime": **********.424272, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.904128, "end": **********.42428, "duration": 0.5201518535614014, "duration_str": "520ms", "measures": [{"label": "Booting", "start": **********.904128, "relative_start": 0, "end": **********.977972, "relative_end": **********.977972, "duration": 0.*****************, "duration_str": "73.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.977979, "relative_start": 0.*****************, "end": **********.424281, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "446ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.97928, "relative_start": 0.*****************, "end": **********.979747, "relative_end": **********.979747, "duration": 0.00046706199645996094, "duration_str": "467μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::30c1d878896da9becaa5c742f9c433ec", "start": **********.075058, "relative_start": 0.****************, "end": **********.075058, "relative_end": **********.075058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::884d3416ba71745f64da4c2f0e691b0f", "start": **********.420231, "relative_start": 0.****************, "end": **********.420231, "relative_end": **********.420231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.423984, "relative_start": 0.5198559761047363, "end": **********.424112, "relative_end": **********.424112, "duration": 0.00012803077697753906, "duration_str": "128μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 3504384, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "__components::30c1d878896da9becaa5c742f9c433ec", "param_count": null, "params": [], "start": **********.07505, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/30c1d878896da9becaa5c742f9c433ec.blade.php__components::30c1d878896da9becaa5c742f9c433ec", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F30c1d878896da9becaa5c742f9c433ec.blade.php&line=1", "ajax": false, "filename": "30c1d878896da9becaa5c742f9c433ec.blade.php", "line": "?"}}, {"name": "__components::884d3416ba71745f64da4c2f0e691b0f", "param_count": null, "params": [], "start": **********.420226, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/884d3416ba71745f64da4c2f0e691b0f.blade.php__components::884d3416ba71745f64da4c2f0e691b0f", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F884d3416ba71745f64da4c2f0e691b0f.blade.php&line=1", "ajax": false, "filename": "884d3416ba71745f64da4c2f0e691b0f.blade.php", "line": "?"}}]}, "queries": {"count": 14, "nb_statements": 12, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00513, "accumulated_duration_str": "5.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = '4ZNUdoRjoIz4k7SKHmBC7D5wAL2jgQyjeosHDPDb' limit 1", "type": "query", "params": [], "bindings": ["4ZNUdoRjoIz4k7SKHmBC7D5wAL2jgQyjeosHDPDb"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.055018, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 13.06}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.071214, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 13.06, "width_percent": 2.339}, {"sql": "select * from \"cache\" where \"key\" in ('lfsl_cache_filament_email_authentication.1')", "type": "query", "params": [], "bindings": ["lfsl_cache_filament_email_authentication.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 300}], "start": **********.08596, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 15.4, "width_percent": 6.433}, {"sql": "delete from \"cache\" where \"key\" in ('lfsl_cache_filament_email_authentication.1', 'lfsl_cache_illuminate:cache:flexible:created:filament_email_authentication.1') and \"expiration\" <= **********", "type": "query", "params": [], "bindings": ["lfsl_cache_filament_email_authentication.1", "lfsl_cache_illuminate:cache:flexible:created:filament_email_authentication.1", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 206}], "start": **********.091065, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 21.832, "width_percent": 13.255}, {"sql": "select * from \"cache\" where \"key\" in ('lfsl_cache_filament_email_authentication.1:timer')", "type": "query", "params": [], "bindings": ["lfsl_cache_filament_email_authentication.1:timer"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 164}], "start": **********.0971782, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 35.088, "width_percent": 1.949}, {"sql": "delete from \"cache\" where \"key\" in ('lfsl_cache_filament_email_authentication.1:timer', 'lfsl_cache_illuminate:cache:flexible:created:filament_email_authentication.1:timer') and \"expiration\" <= **********", "type": "query", "params": [], "bindings": ["lfsl_cache_filament_email_authentication.1:timer", "lfsl_cache_illuminate:cache:flexible:created:filament_email_authentication.1:timer", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 411}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 105}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 204}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 348}], "start": **********.102072, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:411", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=411", "ajax": false, "filename": "DatabaseStore.php", "line": "411"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 37.037, "width_percent": 16.569}, {"sql": "insert or ignore into \"cache\" (\"key\", \"value\", \"expiration\") values ('lfsl_cache_filament_email_authentication.1:timer', 'i:1751369467;', 1751369467)", "type": "query", "params": [], "bindings": ["lfsl_cache_filament_email_authentication.1:timer", "i:1751369467;", 1751369467], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 213}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 164}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 149}, {"index": 15, "namespace": null, "name": "vendor/filament/filament/src/Auth/MultiFactor/Email/EmailAuthentication.php", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Auth/MultiFactor/Email/EmailAuthentication.php", "line": 76}], "start": **********.10932, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:213", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=213", "ajax": false, "filename": "DatabaseStore.php", "line": "213"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 53.606, "width_percent": 10.526}, {"sql": "select * from \"cache\" where \"key\" in ('lfsl_cache_filament_email_authentication.1')", "type": "query", "params": [], "bindings": ["lfsl_cache_filament_email_authentication.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 204}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 169}], "start": **********.1150248, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 64.133, "width_percent": 1.559}, {"sql": "insert or ignore into \"cache\" (\"key\", \"value\", \"expiration\") values ('lfsl_cache_filament_email_authentication.1', 'i:0;', 1751369467)", "type": "query", "params": [], "bindings": ["lfsl_cache_filament_email_authentication.1", "i:0;", 1751369467], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 213}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 348}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 169}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 300}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 168}], "start": **********.120564, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:213", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=213", "ajax": false, "filename": "DatabaseStore.php", "line": "213"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 65.692, "width_percent": 10.721}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 263}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 234}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 373}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 149}], "start": **********.121598, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:263", "source": {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 263}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=263", "ajax": false, "filename": "DatabaseStore.php", "line": "263"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 76.413, "width_percent": 0}, {"sql": "select * from \"cache\" where \"key\" = 'lfsl_cache_filament_email_authentication.1' limit 1", "type": "query", "params": [], "bindings": ["lfsl_cache_filament_email_authentication.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 267}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 263}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 234}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 373}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 172}], "start": **********.1253262, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:267", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=267", "ajax": false, "filename": "DatabaseStore.php", "line": "267"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 76.413, "width_percent": 1.754}, {"sql": "update \"cache\" set \"value\" = 'i:1;' where \"key\" = 'lfsl_cache_filament_email_authentication.1'", "type": "query", "params": [], "bindings": ["i:1;", "lfsl_cache_filament_email_authentication.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 292}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 263}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 234}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 172}], "start": **********.136776, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:292", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 292}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=292", "ajax": false, "filename": "DatabaseStore.php", "line": "292"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 78.168, "width_percent": 3.119}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 263}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 234}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "line": 373}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 172}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php", "line": 149}], "start": **********.137895, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:263", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "line": 263}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=263", "ajax": false, "filename": "DatabaseStore.php", "line": "263"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 81.287, "width_percent": 0}, {"sql": "insert into \"jobs\" (\"queue\", \"attempts\", \"reserved_at\", \"available_at\", \"created_at\", \"payload\") values ('default', 0, null, **********, **********, '{\"uuid\":\"a77d47c6-83cb-47f2-a62c-5f86ae1279a6\",\"displayName\":\"Filament\\\\Auth\\\\MultiFactor\\\\Email\\\\Notifications\\\\VerifyEmailAuthentication\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:6:\\\"sqlite\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:71:\\\"Filament\\\\Auth\\\\MultiFactor\\\\Email\\\\Notifications\\\\VerifyEmailAuthentication\\\":3:{s:4:\\\"code\\\";s:6:\\\"677745\\\";s:17:\\\"codeExpiryMinutes\\\";i:4;s:2:\\\"id\\\";s:36:\\\"a899d7ef-1b7e-4cfd-bb1a-89dfd699f9c9\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"},\"createdAt\":**********,\"delay\":null}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"a77d47c6-83cb-47f2-a62c-5f86ae1279a6\",\"displayName\":\"Filament\\\\Auth\\\\MultiFactor\\\\Email\\\\Notifications\\\\VerifyEmailAuthentication\",\"job\":\"Illuminate\\\\Queue\\\\CallQueued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:6:\\\"sqlite\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:71:\\\"Filament\\\\Auth\\\\MultiFactor\\\\Email\\\\Notifications\\\\VerifyEmailAuthentication\\\":3:{s:4:\\\"code\\\";s:6:\\\"677745\\\";s:17:\\\"codeExpiryMinutes\\\";i:4;s:2:\\\"id\\\";s:36:\\\"a899d7ef-1b7e-4cfd-bb1a-89dfd699f9c9\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"},\"createdAt\":**********,\"delay\":null}"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "line": 247}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "line": 158}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "line": 361}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "line": 152}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "line": 250}], "start": **********.4174318, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:247", "source": {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "line": 247}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=247", "ajax": false, "filename": "DatabaseQueue.php", "line": "247"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 81.287, "width_percent": 18.713}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.auth.pages.edit-profile #x8unwLl4MxU5qXdDRYtb": "array:4 [\n  \"data\" => array:8 [\n    \"data\" => array:16 [\n      \"id\" => 1\n      \"name\" => \"System User\"\n      \"email\" => \"<EMAIL>\"\n      \"email_verified_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"created_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"updated_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"public_id\" => \"01JZ259ZK8T7A00R9ZC5X9DYEA\"\n      \"slug\" => \"system-user\"\n      \"deleted_at\" => null\n      \"created_by\" => null\n      \"updated_by\" => 1\n      \"deleted_by\" => null\n      \"has_email_authentication\" => false\n      \"password\" => null\n      \"passwordConfirmation\" => null\n      \"currentPassword\" => null\n    ]\n    \"mountedActions\" => array:1 [\n      0 => array:3 [\n        \"name\" => \"setUpEmailAuthentication\"\n        \"arguments\" => []\n        \"context\" => array:1 [\n          \"schemaComponent\" => \"content.email_code\"\n        ]\n      ]\n    ]\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:2 [\n      0 => \"form\"\n      1 => \"content\"\n    ]\n  ]\n  \"name\" => \"filament.auth.pages.edit-profile\"\n  \"component\" => \"Filament\\Auth\\Pages\\EditProfile\"\n  \"id\" => \"x8unwLl4MxU5qXdDRYtb\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Filament\\Auth\\Pages\\EditProfile@mountAction<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=89\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Factions%2Fsrc%2FConcerns%2FInteractsWithActions.php&line=89\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/actions/src/Concerns/InteractsWithActions.php:89-164</a>", "middleware": "web", "duration": "515ms", "peak_memory": "4MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-947469634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-947469634\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-625284295 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5VYB93hxfCGySdSdvkFlUzz9WyFz4ywTDNfrzFVr</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"984 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;System User&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;email_verified_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;created_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;public_id&quot;:&quot;01JZ259ZK8T7A00R9ZC5X9DYEA&quot;,&quot;slug&quot;:&quot;system-user&quot;,&quot;deleted_at&quot;:null,&quot;created_by&quot;:null,&quot;updated_by&quot;:1,&quot;deleted_by&quot;:null,&quot;has_email_authentication&quot;:false,&quot;password&quot;:null,&quot;passwordConfirmation&quot;:null,&quot;currentPassword&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;defaultActionContext&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areSchemaStateUpdateHooksDisabledForTesting&quot;:false,&quot;discoveredSchemaNames&quot;:[[&quot;form&quot;,&quot;content&quot;],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;x8unwLl4MxU5qXdDRYtb&quot;,&quot;name&quot;:&quot;filament.auth.pages.edit-profile&quot;,&quot;path&quot;:&quot;admin\\/profile&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c504d2e5b0d9e5a7345e15290d940044d5682eecef2d0efab8ae2897575fcec1&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"11 characters\">mountAction</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">setUpEmailAuthentication</span>\"\n            <span class=sf-dump-index>1</span> => []\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>schemaComponent</span>\" => \"<span class=sf-dump-str title=\"18 characters\">content.email_code</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-625284295\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1533960335 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1252 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjVjUlhpNDd3eWhDa0s3QjNxb21taFE9PSIsInZhbHVlIjoiREplYnhIaEpXSGkwRmZIWkJhY09vTXNJaDh1NHpuc0ozckd6bzJyT3J3UEtWeWx0WkdTTGtwMW5mNmFSaUlqNzR6RXNmQmQrMThYMTFWVVRaeFBGR2pJem94Si9EUFh0aW4vdnNHb0l0OWRGU20wRjB5TEhvOGI4TlpFSmFSeGFPSkVZdmdjc09SMk92MHdHUEc0ZVhqK2VuQ25maUtFMVBkS1l5MFRWb0l5OUpYSEI2WVZTWUJFdzludVVzRElLVWsvdy9DbjBvKzVZemFXSEVKMUZSRUIwMy84N0JMTnVTbjlYSzU0Mno4Yz0iLCJtYWMiOiIxNTE0N2MxYzhiZWQ5YWNmMDE5Y2U4Yjc2M2I3MjhhZTNhMDE1YzcyMzJiMjgwMTZiNmZlYmE4ZWIwN2M1NTA4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkU2NVlUNEMxc2dNQlhRaDAzV2pKd1E9PSIsInZhbHVlIjoiTkE4YkFmV1ZKRDRnSS9BUlVuNVE1cGJDdWRRVWpEUjgrcjNkNXM2TXl1UHZqWUZxZnp2anBxSGplNThlbEs4cW9QTmxtRFBIaTlMTmxYcDB2UCtiaXBvM01kSWp0Vkg2K21UaEZFV2hwaGp2MXYrczlobThIdjNtQ1c4Z2lydGciLCJtYWMiOiJjZjVhNjAxNTk0ZWRjYTU5N2MzYjU2MTRhYWU3YTkwYWZmNGIzMDY2MWNkNDE3NGE2MDBmMjBiN2ViZTkyYTVlIiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6Ik5NNFE0OENiZWYyVlZPVzQ2bDQ0N3c9PSIsInZhbHVlIjoiMjdZa0JSQzFFOG5XYzB2M1FoVTM1ak4vVDRuS293a0k4RWNLTENrKzJZQTYrTjVVaE9ZU3M1bEFpYit6T3UxL1luclJ5cXRvRTJMNVdlellKeGx5N2wwU1U2ZVNWdmFKTStud2pCTWYzRlVlQ0VKYW1yeWFKOGtrZjZ0Z25pT2YiLCJtYWMiOiI2N2VlYThlZmZhZDYxY2I0YTM5ZTFhZTFlY2U3ZTdhNTUzNDQ1M2E3ZDk3OTQ1ZTE5MWI1YjRlN2Q2YzVjZTI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://lfsl.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1327</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533960335\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-968497305 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|Bw1MCtSuwJvp5hm5Vwls3Q5yU08MR2P1tvkLZacWBy5hLBIUaF8OmqqjbIy1|$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5VYB93hxfCGySdSdvkFlUzz9WyFz4ywTDNfrzFVr</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4ZNUdoRjoIz4k7SKHmBC7D5wAL2jgQyjeosHDPDb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-968497305\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-457386279 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 11:30:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457386279\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1018689169 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5VYB93hxfCGySdSdvkFlUzz9WyFz4ywTDNfrzFVr</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>filament_email_authentication_code</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$DEjyVJhaEcl3RZTx4tMJiOMSXY9R9nyf0ha4nbIGTU7Gy7y8kjhSq</span>\"\n  \"<span class=sf-dump-key>filament_email_authentication_code_expires_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\CarbonImmutable @1751369647\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CarbonImmutable @1751369647</span></span> {<a class=sf-dump-ref>#1030</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000004060000000000000000</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\CarbonImmutable`\">clock</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Tuesday, July 1, 2025\n+ 00:03:59.980809 from now\nDST Off\">2025-07-01 11:34:07.406128 UTC (+00:00)</span>\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018689169\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}