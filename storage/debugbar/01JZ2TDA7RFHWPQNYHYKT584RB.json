{"__meta": {"id": "01JZ2TDA7RFHWPQNYHYKT584RB", "datetime": "2025-07-01 11:02:03", "utime": **********.256113, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.054881, "end": **********.256119, "duration": 0.20123791694641113, "duration_str": "201ms", "measures": [{"label": "Booting", "start": **********.054881, "relative_start": 0, "end": **********.215887, "relative_end": **********.215887, "duration": 0.*****************, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.215893, "relative_start": 0.****************, "end": **********.25612, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "40.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.216663, "relative_start": 0.*****************, "end": **********.21682, "relative_end": **********.21682, "duration": 0.0001571178436279297, "duration_str": "157μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::30c1d878896da9becaa5c742f9c433ec", "start": **********.242058, "relative_start": 0.*****************, "end": **********.242058, "relative_end": **********.242058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.246606, "relative_start": 0.****************, "end": **********.246606, "relative_end": **********.246606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.24788, "relative_start": 0.19299888610839844, "end": **********.24788, "relative_end": **********.24788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.249101, "relative_start": 0.19421982765197754, "end": **********.249101, "relative_end": **********.249101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.249823, "relative_start": 0.19494199752807617, "end": **********.249823, "relative_end": **********.249823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.250499, "relative_start": 0.1956179141998291, "end": **********.250499, "relative_end": **********.250499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6880515bef9fee3b6a53f72c8a43c470", "start": **********.254778, "relative_start": 0.19989681243896484, "end": **********.254778, "relative_end": **********.254778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.255837, "relative_start": 0.20095586776733398, "end": **********.255978, "relative_end": **********.255978, "duration": 0.000141143798828125, "duration_str": "141μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5956312, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "__components::30c1d878896da9becaa5c742f9c433ec", "param_count": null, "params": [], "start": **********.24205, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/30c1d878896da9becaa5c742f9c433ec.blade.php__components::30c1d878896da9becaa5c742f9c433ec", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F30c1d878896da9becaa5c742f9c433ec.blade.php&line=1", "ajax": false, "filename": "30c1d878896da9becaa5c742f9c433ec.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.246602, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.247875, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.249098, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.24982, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.250496, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::6880515bef9fee3b6a53f72c8a43c470", "param_count": null, "params": [], "start": **********.254775, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/6880515bef9fee3b6a53f72c8a43c470.blade.php__components::6880515bef9fee3b6a53f72c8a43c470", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F6880515bef9fee3b6a53f72c8a43c470.blade.php&line=1", "ajax": false, "filename": "6880515bef9fee3b6a53f72c8a43c470.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00020999999999999998, "accumulated_duration_str": "210μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = 'cf22qcd3LuPSlGDUeLugkfooDUJm31Ahb6B2c0cH' limit 1", "type": "query", "params": [], "bindings": ["cf22qcd3LuPSlGDUeLugkfooDUJm31Ahb6B2c0cH"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.2279742, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 57.143}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.238281, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 57.143, "width_percent": 42.857}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.auth.pages.edit-profile #e9GBxd3KIbkqV1ZvoTi1": "array:4 [\n  \"data\" => array:8 [\n    \"data\" => array:16 [\n      \"id\" => 1\n      \"name\" => \"System User\"\n      \"email\" => \"<EMAIL>\"\n      \"email_verified_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"created_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"updated_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"public_id\" => \"01JZ259ZK8T7A00R9ZC5X9DYEA\"\n      \"slug\" => \"adam-mason-2680\"\n      \"deleted_at\" => null\n      \"created_by\" => null\n      \"updated_by\" => null\n      \"deleted_by\" => null\n      \"has_email_authentication\" => false\n      \"password\" => \"password\"\n      \"passwordConfirmation\" => null\n      \"currentPassword\" => null\n    ]\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:2 [\n      0 => \"form\"\n      1 => \"content\"\n    ]\n  ]\n  \"name\" => \"filament.auth.pages.edit-profile\"\n  \"component\" => \"Filament\\Auth\\Pages\\EditProfile\"\n  \"id\" => \"e9GBxd3KIbkqV1ZvoTi1\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "197ms", "peak_memory": "8MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-266004965 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-266004965\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-662137988 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FsmClCh9x07ImBLKx91SMqDGuymiGOhFsCiVH7yU</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"991 characters\">{&quot;data&quot;:{&quot;data&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;System User&quot;,&quot;email&quot;:&quot;<EMAIL>&quot;,&quot;email_verified_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;created_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-01T04:53:13.000000Z&quot;,&quot;public_id&quot;:&quot;01JZ259ZK8T7A00R9ZC5X9DYEA&quot;,&quot;slug&quot;:&quot;adam-mason-2680&quot;,&quot;deleted_at&quot;:null,&quot;created_by&quot;:null,&quot;updated_by&quot;:null,&quot;deleted_by&quot;:null,&quot;has_email_authentication&quot;:false,&quot;password&quot;:null,&quot;passwordConfirmation&quot;:null,&quot;currentPassword&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;defaultActionContext&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;areSchemaStateUpdateHooksDisabledForTesting&quot;:false,&quot;discoveredSchemaNames&quot;:[[&quot;form&quot;,&quot;content&quot;],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;e9GBxd3KIbkqV1ZvoTi1&quot;,&quot;name&quot;:&quot;filament.auth.pages.edit-profile&quot;,&quot;path&quot;:&quot;admin\\/profile&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;5866563d956d3f187ae0ee4d25f577c8c34046b39da8db46a9fb564802ea1532&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data.password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-662137988\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1938760923 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1170 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlZBL0I4c3lZOXF4S3lHRzEzQ3pKTnc9PSIsInZhbHVlIjoiRUh3QzJFUzU5YitDSFNBSVE4SStuTXJ6RDcxZm1qKzlCVEdBMFRvdGZGWDJaZld6RFk3WVd1ZldGVGhQRnVob2ZxdHBJSC9ueDQ4WmZKdUR5dlNBYmlVSG5KbU1ldEZtd1VCaGhLYVluTnp1ZWV3NU5iQkFqVDM2Y20zVU1oRGkwUHRaRTAvZXpKcWZHQkZPWjVHeW56VXREWGlPT21aN25iZHorQ1BaWWdjPSIsIm1hYyI6ImRiNjI1ZDdjMjM4NGQ5YzMwMDhhZTUzMjY5OTlmNDU0YjdhYjk0OGNjNTVjNDhkZjNkNDQxYTViNDJkYzY1ZDciLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6Imt3ZWhWdWZNZkV2c0xZRGd0a050R2c9PSIsInZhbHVlIjoiZzRMWkJvbzkrUGh5RWJORUsvc2lDMEJMaEFTVFc2VFp3dTVNWVg5MUtVbVRqYm9Xb3dDMzhraEtMdXVUQ3Zkc2d4NkI1cGNkM1d0K255ZnNkZURvS21VK1B6SnZsVld2V1d5MkRYalFQUTZrTitMVUhKQ0YrdEUwRWY4SUg2TTUiLCJtYWMiOiIzNTljZmMyZmQyMDNjZjQzNjRlOGNlNjYyZDVlZjkwMWE1ODJiMDI5ZjgwNzk5OWZkNTcwZjYxZGUwNzQ4MTZjIiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6IjNlRTZHR2ZyNElNWjJUdmN5Vlg0L1E9PSIsInZhbHVlIjoiaE9JSG9ZbUtyYjJPU2IxSEZWdW13QnJ4bktzcUZSYUdQWmhneG9WVEllVGNiRXEySE9LQ01VdWFKOGNhQ2loOEVHTGJqNTlzOWtLejlxc0VRZEQzZHZ2UGYzMUlIQ05JUFBLNXEvWUVpUjhLT3kzVVdaQUZCTUZHanZGTU9hd3QiLCJtYWMiOiI4MTcwN2FlYWU1NzNmMDgyZmRlNTIwODQ3N2YyODYxOGM0Y2QyYTFkZDhlOTI3MWE5MDlhMGY5NjZlZmMyOWVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://lfsl.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1244</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938760923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-40945383 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"73 characters\">1|i7KPyT5afz|$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FsmClCh9x07ImBLKx91SMqDGuymiGOhFsCiVH7yU</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cf22qcd3LuPSlGDUeLugkfooDUJm31Ahb6B2c0cH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40945383\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1925541740 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 11:02:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925541740\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1175621907 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FsmClCh9x07ImBLKx91SMqDGuymiGOhFsCiVH7yU</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175621907\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}