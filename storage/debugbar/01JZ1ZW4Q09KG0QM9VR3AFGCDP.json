{"__meta": {"id": "01JZ1ZW4Q09KG0QM9VR3AFGCDP", "datetime": "2025-07-01 03:18:17", "utime": **********.568666, "method": "GET", "uri": "/admin/profile", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.461454, "end": **********.568674, "duration": 0.10722017288208008, "duration_str": "107ms", "measures": [{"label": "Booting", "start": **********.461454, "relative_start": 0, "end": **********.522009, "relative_end": **********.522009, "duration": 0.*****************, "duration_str": "60.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.522015, "relative_start": 0.060561180114746094, "end": **********.568674, "relative_end": 0, "duration": 0.046658992767333984, "duration_str": "46.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.522799, "relative_start": 0.*****************, "end": **********.522949, "relative_end": **********.522949, "duration": 0.0001499652862548828, "duration_str": "150μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.542231, "relative_start": 0.*****************, "end": **********.542231, "relative_end": **********.542231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.543506, "relative_start": 0.*****************, "end": **********.543506, "relative_end": **********.543506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.544876, "relative_start": 0.08342218399047852, "end": **********.544876, "relative_end": **********.544876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6880515bef9fee3b6a53f72c8a43c470", "start": **********.546605, "relative_start": 0.08515119552612305, "end": **********.546605, "relative_end": **********.546605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.548884, "relative_start": 0.08743000030517578, "end": **********.548884, "relative_end": **********.548884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.555748, "relative_start": 0.09429407119750977, "end": **********.555776, "relative_end": **********.555776, "duration": 2.7894973754882812e-05, "duration_str": "28μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.568397, "relative_start": 0.10694313049316406, "end": **********.56844, "relative_end": **********.56844, "duration": 4.291534423828125e-05, "duration_str": "43μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 4278720, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 5, "nb_templates": 5, "templates": [{"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.542225, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.543503, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.544871, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::6880515bef9fee3b6a53f72c8a43c470", "param_count": null, "params": [], "start": **********.546601, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/6880515bef9fee3b6a53f72c8a43c470.blade.php__components::6880515bef9fee3b6a53f72c8a43c470", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F6880515bef9fee3b6a53f72c8a43c470.blade.php&line=1", "ajax": false, "filename": "6880515bef9fee3b6a53f72c8a43c470.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.548879, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00092, "accumulated_duration_str": "920μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = 'g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L' limit 1", "type": "query", "params": [], "bindings": ["g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.528342, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 15.217}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.534664, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 15.217, "width_percent": 8.696}, {"sql": "update \"sessions\" set \"payload\" = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiTDVjamRySW9DWG9GTFhYWFJRZGhkTEtDZ3VaWllmaXE3bml1c2NTViI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMxOiJodHRwczovL2xmc2wudGVzdC9hZG1pbi9wcm9maWxlIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJGEzTmgySUxwZC5qTk42eUU3RURybi5QT1JzNklwSHFOL3dzMVpqNnd0eTUwRXhTVm5hM2RtIjt9', \"last_activity\" = **********, \"user_id\" = 1, \"ip_address\" = '127.0.0.1', \"user_agent\" = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where \"id\" = 'g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiTDVjamRySW9DWG9GTFhYWFJRZGhkTEtDZ3VaWllmaXE3bml1c2NTViI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMxOiJodHRwczovL2xmc2wudGVzdC9hZG1pbi9wcm9maWxlIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJGEzTmgySUxwZC5qTk42eUU3RURybi5QT1JzNklwSHFOL3dzMVpqNnd0eTUwRXhTVm5hM2RtIjt9", **********, 1, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.567345, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 23.913, "width_percent": 76.087}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.auth.pages.edit-profile #DLFIxznQtyy1x5gPUc3x": "array:4 [\n  \"data\" => array:8 [\n    \"data\" => array:15 [\n      \"id\" => 1\n      \"name\" => \"System User\"\n      \"email\" => \"<EMAIL>\"\n      \"email_verified_at\" => \"2025-07-01T03:16:54.000000Z\"\n      \"created_at\" => \"2025-07-01T03:16:54.000000Z\"\n      \"updated_at\" => \"2025-07-01T03:16:54.000000Z\"\n      \"public_id\" => \"01JZ1ZSM00YK4NMAEZ8YVAVBS3\"\n      \"slug\" => \"david-davis-5976\"\n      \"deleted_at\" => null\n      \"created_by\" => null\n      \"updated_by\" => null\n      \"deleted_by\" => null\n      \"password\" => null\n      \"passwordConfirmation\" => null\n      \"currentPassword\" => null\n    ]\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:1 [\n      0 => \"form\"\n    ]\n  ]\n  \"name\" => \"filament.auth.pages.edit-profile\"\n  \"component\" => \"Filament\\Auth\\Pages\\EditProfile\"\n  \"id\" => \"DLFIxznQtyy1x5gPUc3x\"\n]", "filament.livewire.simple-user-menu #ztrnUgm2gr2TjRIkQWoQ": "array:4 [\n  \"data\" => array:7 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.simple-user-menu\"\n  \"component\" => \"Filament\\Livewire\\SimpleUserMenu\"\n  \"id\" => \"ztrnUgm2gr2TjRIkQWoQ\"\n]", "filament.livewire.notifications #oLLr8ZiSbpL1rtU5wgDp": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#5208\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"oLLr8ZiSbpL1rtU5wgDp\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/admin/profile", "action_name": "filament.admin.auth.profile", "controller_action": "Filament\\Auth\\Pages\\EditProfile", "uri": "GET admin/profile", "controller": "Filament\\Auth\\Pages\\EditProfile@render<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:49-57</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, verified:filament.admin.auth.email-verification.prompt", "duration": "103ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1170 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ii9jN1JWZ3VlMllJd0dDdkQ5UmhIY2c9PSIsInZhbHVlIjoiQW1zSmV1NDhiODZNeUxXTlZ5MFFiaEUvZjEvcFFFUFNXWXFZRVFpN2l0bGFLWXlMZk9kb21aWUQrZWpKTFIxTG1JVHVPOE1McmE4aElXNWdHNWtoMmlXRlJTM2R4MWtha0pLUXRBT3lFdzRLMTd5V3VidGljdzhUOXh4Z0ptN0pRajI1cWtVSU5xODlqSDE3dDQwZlo0NlIwcS8yUlJDbUZrMjZFWFc2K3hVPSIsIm1hYyI6ImMzNmU3YTIzNThjYTUyZjRlYzE1YWM0OWQ2YWVmOWE5NTlkOTg1MWY2NDc1NjlmMGI2ZTc5NmIwMzJjOTFiZGQiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjhldkhkN2pRdmZxY1M1TVVVamFRNXc9PSIsInZhbHVlIjoiMjhDSVNtRURDb0dZMktlSENHRmY2VytDY3NhUUVrYTNxb0l0Nm9IbXFvV1NrVVRIdkh3bGlIeC9zQlNqMkE5eXprRzlIUVJXcDNaczM5YXp0MGEzMmc2eTYrekNiZ2hOaUJBd2R3RGxOQ2pVTjFVdFd1T1NVSys4N2ZlWU5SWGUiLCJtYWMiOiIzNDc0NjMyYjQ4ZmQ1NjdlMjdjOGJlNzBmOTljMjg5ZGNhNDc1MWUwM2IzNmE2MjQ4NjI4NmQ4ZTc4ZWYxOWFkIiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6ImZGWjRFRUt3QjQzcEJ0LzN2OHV1a2c9PSIsInZhbHVlIjoiTGJOeEpPdEtVVWpxc3BQK2IzZjJlQXFDa29jQzBKdnFnZnhMZmZMSUdBT0l6b3pVdzVlbzhOak5mZWRIMFltcEJSM0ZWYVRIamErSktOUmtwZHVkQkNtN21LWDYyTHlGSVgrMjdWYWEybENwZHhPU1Z3VnA4T1hzbkNHeFlLT3AiLCJtYWMiOiJmN2ZkMDM1ZDU3ZWYwZjAyOTYzMGNjOTUwYzU3YmFkMDc5ZWUyOTUwZTgwZTRiNTljOGIwNmVjNThkYWJlNTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://lfsl.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1409451044 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"73 characters\">1|S8YbluZk61|$2y$12$a3Nh2ILpd.jNN6yE7EDrn.PORs6IpHqN/ws1Zj6wty50ExSVna3dm</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">L5cjdrIoCXoFLXXXRQdhdLKCguZZYfiq7niuscSV</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409451044\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1670201862 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 03:18:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IlIzZU1HcytlU0tWajZRYk9pZFQxMlE9PSIsInZhbHVlIjoibkExTVRXTS9iTENXNWxoc0xUU3FxVzNEa0JudFVJTXZvV1gycGl1clVlQXREZFY0T2g0QndXeDJMQVp0RTdyczNLV1dKaFo0UGxtdXVZamRvdzVRWWRDUThrUjJLakRWUkdpMHBvRDlWZ2d0WVo2Y2Era2RSRkVTbmM1aGVkdVUiLCJtYWMiOiIwNmRlMzMxZTA5NTA3Njg5NWM0OTFhZjhkOTg1MWRlZWRmZTUzM2Q3YzUzNjc2ZjE0MDMyZDA2NWZkMWI3MjVkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 05:18:17 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">lfsl_session=eyJpdiI6IkplVzZFOGFxd0hHYm5lWmw1dEdZVmc9PSIsInZhbHVlIjoiaUIrc3d3b1JIMzh3TDdzckRJa0xCSmZmOUhJUzNjT0N1Yi93bnVnR2dMYWg0K0hSMnN3RFNHd3VlR3B0eW1VMHdwSkdhQzZ2Sm5pR3h2WHphSys5eGxPVmN1K2JWRUMveWdxTHlkOW80VXQ4YUNzNExoZWVPU0lYOG9CY0lkeWEiLCJtYWMiOiJmNjg1YWJiMTI4YTY4MGQzMDNjZGM1NmI3MjE4MTg4YzZjNDg4ZTdmZTRkNTVlNmY1ZjBjMjRhY2I1NjA1ZmJlIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 05:18:17 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IlIzZU1HcytlU0tWajZRYk9pZFQxMlE9PSIsInZhbHVlIjoibkExTVRXTS9iTENXNWxoc0xUU3FxVzNEa0JudFVJTXZvV1gycGl1clVlQXREZFY0T2g0QndXeDJMQVp0RTdyczNLV1dKaFo0UGxtdXVZamRvdzVRWWRDUThrUjJLakRWUkdpMHBvRDlWZ2d0WVo2Y2Era2RSRkVTbmM1aGVkdVUiLCJtYWMiOiIwNmRlMzMxZTA5NTA3Njg5NWM0OTFhZjhkOTg1MWRlZWRmZTUzM2Q3YzUzNjc2ZjE0MDMyZDA2NWZkMWI3MjVkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 05:18:17 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">lfsl_session=eyJpdiI6IkplVzZFOGFxd0hHYm5lWmw1dEdZVmc9PSIsInZhbHVlIjoiaUIrc3d3b1JIMzh3TDdzckRJa0xCSmZmOUhJUzNjT0N1Yi93bnVnR2dMYWg0K0hSMnN3RFNHd3VlR3B0eW1VMHdwSkdhQzZ2Sm5pR3h2WHphSys5eGxPVmN1K2JWRUMveWdxTHlkOW80VXQ4YUNzNExoZWVPU0lYOG9CY0lkeWEiLCJtYWMiOiJmNjg1YWJiMTI4YTY4MGQzMDNjZGM1NmI3MjE4MTg4YzZjNDg4ZTdmZTRkNTVlNmY1ZjBjMjRhY2I1NjA1ZmJlIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 05:18:17 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670201862\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1376260986 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">L5cjdrIoCXoFLXXXRQdhdLKCguZZYfiq7niuscSV</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$a3Nh2ILpd.jNN6yE7EDrn.PORs6IpHqN/ws1Zj6wty50ExSVna3dm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376260986\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/admin/profile", "action_name": "filament.admin.auth.profile", "controller_action": "Filament\\Auth\\Pages\\EditProfile"}, "badge": null}}