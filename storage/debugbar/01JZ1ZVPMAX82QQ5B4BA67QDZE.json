{"__meta": {"id": "01JZ1ZVPMAX82QQ5B4BA67QDZE", "datetime": "2025-07-01 03:18:03", "utime": **********.146196, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751339882.973227, "end": **********.146204, "duration": 0.17297697067260742, "duration_str": "173ms", "measures": [{"label": "Booting", "start": 1751339882.973227, "relative_start": 0, "end": **********.011605, "relative_end": **********.011605, "duration": 0.038378000259399414, "duration_str": "38.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.011609, "relative_start": 0.*****************, "end": **********.146205, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.012077, "relative_start": 0.*****************, "end": **********.012157, "relative_end": **********.012157, "duration": 7.987022399902344e-05, "duration_str": "80μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.067625, "relative_start": 0.*****************, "end": **********.067625, "relative_end": **********.067625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.108137, "relative_start": 0.*****************, "end": **********.108157, "relative_end": **********.108157, "duration": 2.002716064453125e-05, "duration_str": "20μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.145911, "relative_start": 0.****************, "end": **********.145954, "relative_end": **********.145954, "duration": 4.291534423828125e-05, "duration_str": "43μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 4732112, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.067619, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0008000000000000001, "accumulated_duration_str": "800μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = 'g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L' limit 1", "type": "query", "params": [], "bindings": ["g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.018434, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 16.25}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.023931, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 16.25, "width_percent": 13.75}, {"sql": "update \"sessions\" set \"payload\" = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiTDVjamRySW9DWG9GTFhYWFJRZGhkTEtDZ3VaWllmaXE3bml1c2NTViI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjIzOiJodHRwczovL2xmc2wudGVzdC9hZG1pbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRhM05oMklMcGQuak5ONnlFN0VEcm4uUE9SczZJcEhxTi93czFaajZ3dHk1MEV4U1ZuYTNkbSI7fQ==', \"last_activity\" = **********, \"user_id\" = 1, \"ip_address\" = '127.0.0.1', \"user_agent\" = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where \"id\" = 'g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiTDVjamRySW9DWG9GTFhYWFJRZGhkTEtDZ3VaWllmaXE3bml1c2NTViI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjIzOiJodHRwczovL2xmc2wudGVzdC9hZG1pbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRhM05oMklMcGQuak5ONnlFN0VEcm4uUE9SczZJcEhxTi93czFaajZ3dHk1MEV4U1ZuYTNkbSI7fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.14494, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 30, "width_percent": 70}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.pages.dashboard #nUEHD1bfJaeULqeZdS08": "array:4 [\n  \"data\" => array:7 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.pages.dashboard\"\n  \"component\" => \"Filament\\Pages\\Dashboard\"\n  \"id\" => \"nUEHD1bfJaeULqeZdS08\"\n]", "filament.widgets.account-widget #Qc2rkYDHicZxfIyT5Cxc": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament.widgets.account-widget\"\n  \"component\" => \"Filament\\Widgets\\AccountWidget\"\n  \"id\" => \"Qc2rkYDHicZxfIyT5Cxc\"\n]", "filament.widgets.filament-info-widget #KIWmTPT0KhvEFJfJXUbW": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament.widgets.filament-info-widget\"\n  \"component\" => \"Filament\\Widgets\\FilamentInfoWidget\"\n  \"id\" => \"KIWmTPT0KhvEFJfJXUbW\"\n]", "filament.livewire.topbar #03PJ90ACXNIt3ldmyIBV": "array:4 [\n  \"data\" => array:7 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.topbar\"\n  \"component\" => \"Filament\\Livewire\\Topbar\"\n  \"id\" => \"03PJ90ACXNIt3ldmyIBV\"\n]", "filament.livewire.sidebar #oQxxdIPDy1olZ9z9MLsr": "array:4 [\n  \"data\" => array:7 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.sidebar\"\n  \"component\" => \"Filament\\Livewire\\Sidebar\"\n  \"id\" => \"oQxxdIPDy1olZ9z9MLsr\"\n]", "filament.livewire.notifications #Lv6qNIRXP3nicBHvwyww": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#4236\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"Lv6qNIRXP3nicBHvwyww\"\n]"}, "count": 6}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "Filament\\Pages\\Dashboard", "uri": "GET admin", "controller": "Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:49-57</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, verified:filament.admin.auth.email-verification.prompt", "duration": "171ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1170 characters\">XSRF-TOKEN=eyJpdiI6IjZld2p6bDI0a21KQ1Y1aGQxR1lZYlE9PSIsInZhbHVlIjoialQ0SGdXTWNabkU4cEFzQUljdXV5K2ljdFRNMXdVelpzcHN2VkJVUWtsaUFhL0JCWmZKWjk5RHh6R0taemRXd0xRQldqeTY2N3VONkF3c3JiNk51Ti9CLzZXOFAzZXRPQldQbGdFYzE3RzI5bGRibC9YSVlWR2JBWmFWZzlQczYiLCJtYWMiOiJkMTBkN2FlOWJkMzAyNmU2YThiODkxZTkwNTQ3NjA5NGFjN2VjZThlYTQ2NTMzOWYwOGQ2ZTEwMzdmOGU1NDMyIiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6IjMrU05NL1dyN0lXY3pEZWNzYk5aSVE9PSIsInZhbHVlIjoiS2tsenZiY2FwRHVVSllncVlnaElwWENqOEU5cCtRVFlpQWxFd3lYWFVucjAvYjFKUzIyQkN1WHBCYmpnczFBd3VLNUQ2ZlVRMzJZQzFZOERQcGNLU3hJS0FZLzFVeTQ1Zi96SW1BNUNRYzJqZFdnNmZTdVQ3OVl6ZFBOVHMyWU4iLCJtYWMiOiIyZmE4NWQ3NGRmYTE5NDY3Yjg1NmQ2OTViYmExNGI3NzY5OGZjZWY4NTQyZjU0NzhkY2U1ODg0NDAzYzVmMjMxIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ii9jN1JWZ3VlMllJd0dDdkQ5UmhIY2c9PSIsInZhbHVlIjoiQW1zSmV1NDhiODZNeUxXTlZ5MFFiaEUvZjEvcFFFUFNXWXFZRVFpN2l0bGFLWXlMZk9kb21aWUQrZWpKTFIxTG1JVHVPOE1McmE4aElXNWdHNWtoMmlXRlJTM2R4MWtha0pLUXRBT3lFdzRLMTd5V3VidGljdzhUOXh4Z0ptN0pRajI1cWtVSU5xODlqSDE3dDQwZlo0NlIwcS8yUlJDbUZrMjZFWFc2K3hVPSIsIm1hYyI6ImMzNmU3YTIzNThjYTUyZjRlYzE1YWM0OWQ2YWVmOWE5NTlkOTg1MWY2NDc1NjlmMGI2ZTc5NmIwMzJjOTFiZGQiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">https://lfsl.test/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1905177524 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">L5cjdrIoCXoFLXXXRQdhdLKCguZZYfiq7niuscSV</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g5oyMRygIOlwjhY3I2zMf9znlXH6ct99AIgZkL7L</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"73 characters\">1|S8YbluZk61|$2y$12$a3Nh2ILpd.jNN6yE7EDrn.PORs6IpHqN/ws1Zj6wty50ExSVna3dm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905177524\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1537084567 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 03:18:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IjhldkhkN2pRdmZxY1M1TVVVamFRNXc9PSIsInZhbHVlIjoiMjhDSVNtRURDb0dZMktlSENHRmY2VytDY3NhUUVrYTNxb0l0Nm9IbXFvV1NrVVRIdkh3bGlIeC9zQlNqMkE5eXprRzlIUVJXcDNaczM5YXp0MGEzMmc2eTYrekNiZ2hOaUJBd2R3RGxOQ2pVTjFVdFd1T1NVSys4N2ZlWU5SWGUiLCJtYWMiOiIzNDc0NjMyYjQ4ZmQ1NjdlMjdjOGJlNzBmOTljMjg5ZGNhNDc1MWUwM2IzNmE2MjQ4NjI4NmQ4ZTc4ZWYxOWFkIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 05:18:03 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">lfsl_session=eyJpdiI6ImZGWjRFRUt3QjQzcEJ0LzN2OHV1a2c9PSIsInZhbHVlIjoiTGJOeEpPdEtVVWpxc3BQK2IzZjJlQXFDa29jQzBKdnFnZnhMZmZMSUdBT0l6b3pVdzVlbzhOak5mZWRIMFltcEJSM0ZWYVRIamErSktOUmtwZHVkQkNtN21LWDYyTHlGSVgrMjdWYWEybENwZHhPU1Z3VnA4T1hzbkNHeFlLT3AiLCJtYWMiOiJmN2ZkMDM1ZDU3ZWYwZjAyOTYzMGNjOTUwYzU3YmFkMDc5ZWUyOTUwZTgwZTRiNTljOGIwNmVjNThkYWJlNTc0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 05:18:03 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IjhldkhkN2pRdmZxY1M1TVVVamFRNXc9PSIsInZhbHVlIjoiMjhDSVNtRURDb0dZMktlSENHRmY2VytDY3NhUUVrYTNxb0l0Nm9IbXFvV1NrVVRIdkh3bGlIeC9zQlNqMkE5eXprRzlIUVJXcDNaczM5YXp0MGEzMmc2eTYrekNiZ2hOaUJBd2R3RGxOQ2pVTjFVdFd1T1NVSys4N2ZlWU5SWGUiLCJtYWMiOiIzNDc0NjMyYjQ4ZmQ1NjdlMjdjOGJlNzBmOTljMjg5ZGNhNDc1MWUwM2IzNmE2MjQ4NjI4NmQ4ZTc4ZWYxOWFkIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 05:18:03 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">lfsl_session=eyJpdiI6ImZGWjRFRUt3QjQzcEJ0LzN2OHV1a2c9PSIsInZhbHVlIjoiTGJOeEpPdEtVVWpxc3BQK2IzZjJlQXFDa29jQzBKdnFnZnhMZmZMSUdBT0l6b3pVdzVlbzhOak5mZWRIMFltcEJSM0ZWYVRIamErSktOUmtwZHVkQkNtN21LWDYyTHlGSVgrMjdWYWEybENwZHhPU1Z3VnA4T1hzbkNHeFlLT3AiLCJtYWMiOiJmN2ZkMDM1ZDU3ZWYwZjAyOTYzMGNjOTUwYzU3YmFkMDc5ZWUyOTUwZTgwZTRiNTljOGIwNmVjNThkYWJlNTc0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 05:18:03 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537084567\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-242307175 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">L5cjdrIoCXoFLXXXRQdhdLKCguZZYfiq7niuscSV</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">https://lfsl.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$a3Nh2ILpd.jNN6yE7EDrn.PORs6IpHqN/ws1Zj6wty50ExSVna3dm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242307175\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "Filament\\Pages\\Dashboard"}, "badge": null}}