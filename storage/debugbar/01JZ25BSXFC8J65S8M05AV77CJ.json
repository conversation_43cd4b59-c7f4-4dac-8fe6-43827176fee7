{"__meta": {"id": "01JZ25BSXFC8J65S8M05AV77CJ", "datetime": "2025-07-01 04:54:13", "utime": **********.679122, "method": "GET", "uri": "/admin/profile", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.430269, "end": **********.679128, "duration": 0.24885892868041992, "duration_str": "249ms", "measures": [{"label": "Booting", "start": **********.430269, "relative_start": 0, "end": **********.582852, "relative_end": **********.582852, "duration": 0.*****************, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.582858, "relative_start": 0.****************, "end": **********.679129, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "96.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.583646, "relative_start": 0.*****************, "end": **********.583807, "relative_end": **********.583807, "duration": 0.0001609325408935547, "duration_str": "161μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::30c1d878896da9becaa5c742f9c433ec", "start": **********.630099, "relative_start": 0.****************, "end": **********.630099, "relative_end": **********.630099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.63624, "relative_start": 0.*****************, "end": **********.63624, "relative_end": **********.63624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.637871, "relative_start": 0.20760202407836914, "end": **********.637871, "relative_end": **********.637871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.639019, "relative_start": 0.20875000953674316, "end": **********.639019, "relative_end": **********.639019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6880515bef9fee3b6a53f72c8a43c470", "start": **********.65005, "relative_start": 0.21978092193603516, "end": **********.65005, "relative_end": **********.65005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.653321, "relative_start": 0.2230520248413086, "end": **********.653321, "relative_end": **********.653321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.660059, "relative_start": 0.22978997230529785, "end": **********.660085, "relative_end": **********.660085, "duration": 2.5987625122070312e-05, "duration_str": "26μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.678876, "relative_start": 0.24860692024230957, "end": **********.678913, "relative_end": **********.678913, "duration": 3.719329833984375e-05, "duration_str": "37μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5208664, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "__components::30c1d878896da9becaa5c742f9c433ec", "param_count": null, "params": [], "start": **********.630092, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/30c1d878896da9becaa5c742f9c433ec.blade.php__components::30c1d878896da9becaa5c742f9c433ec", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F30c1d878896da9becaa5c742f9c433ec.blade.php&line=1", "ajax": false, "filename": "30c1d878896da9becaa5c742f9c433ec.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.636235, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.637867, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.639015, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::6880515bef9fee3b6a53f72c8a43c470", "param_count": null, "params": [], "start": **********.650044, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/6880515bef9fee3b6a53f72c8a43c470.blade.php__components::6880515bef9fee3b6a53f72c8a43c470", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F6880515bef9fee3b6a53f72c8a43c470.blade.php&line=1", "ajax": false, "filename": "6880515bef9fee3b6a53f72c8a43c470.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.653316, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00076, "accumulated_duration_str": "760μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = 'UHHHxcVErMSEx54Y0bWPgM0w7wDqwE1jtlIpN6pe' limit 1", "type": "query", "params": [], "bindings": ["UHHHxcVErMSEx54Y0bWPgM0w7wDqwE1jtlIpN6pe"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.5922241, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 17.105}, {"sql": "select * from \"users\" where \"id\" = 1 and \"users\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/filament/filament/src/Http/Middleware/Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 61}], "start": **********.619128, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 17.105, "width_percent": 10.526}, {"sql": "update \"sessions\" set \"payload\" = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiRW4wRVNiQ2RPdjhtR2NrMWtQdTVVVFUxeUt5RmhxQ0ZZbG41U0ZTWCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMxOiJodHRwczovL2xmc2wudGVzdC9hZG1pbi9wcm9maWxlIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEpZZUpEcW0yaG5YZnlTZWs0SmYvMWVXZ1FPRkxHMHQuNlFBcWZHT2loNkVLTVBGaVd4Z2hxIjt9', \"last_activity\" = **********, \"user_id\" = 1, \"ip_address\" = '127.0.0.1', \"user_agent\" = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where \"id\" = 'UHHHxcVErMSEx54Y0bWPgM0w7wDqwE1jtlIpN6pe'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiRW4wRVNiQ2RPdjhtR2NrMWtQdTVVVFUxeUt5RmhxQ0ZZbG41U0ZTWCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMxOiJodHRwczovL2xmc2wudGVzdC9hZG1pbi9wcm9maWxlIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJEpZZUpEcW0yaG5YZnlTZWs0SmYvMWVXZ1FPRkxHMHQuNlFBcWZHT2loNkVLTVBGaVd4Z2hxIjt9", **********, 1, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "UHHHxcVErMSEx54Y0bWPgM0w7wDqwE1jtlIpN6pe"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.67802, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 27.632, "width_percent": 72.368}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.auth.pages.edit-profile #RQgpjNA5qADnU4bwBdP1": "array:4 [\n  \"data\" => array:8 [\n    \"data\" => array:16 [\n      \"id\" => 1\n      \"name\" => \"System User\"\n      \"email\" => \"<EMAIL>\"\n      \"email_verified_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"created_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"updated_at\" => \"2025-07-01T04:53:13.000000Z\"\n      \"public_id\" => \"01JZ259ZK8T7A00R9ZC5X9DYEA\"\n      \"slug\" => \"adam-mason-2680\"\n      \"deleted_at\" => null\n      \"created_by\" => null\n      \"updated_by\" => null\n      \"deleted_by\" => null\n      \"has_email_authentication\" => false\n      \"password\" => null\n      \"passwordConfirmation\" => null\n      \"currentPassword\" => null\n    ]\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:1 [\n      0 => \"form\"\n    ]\n  ]\n  \"name\" => \"filament.auth.pages.edit-profile\"\n  \"component\" => \"Filament\\Auth\\Pages\\EditProfile\"\n  \"id\" => \"RQgpjNA5qADnU4bwBdP1\"\n]", "filament.livewire.simple-user-menu #SqjAsWIC12iIC9fUt63m": "array:4 [\n  \"data\" => array:7 [\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => []\n  ]\n  \"name\" => \"filament.livewire.simple-user-menu\"\n  \"component\" => \"Filament\\Livewire\\SimpleUserMenu\"\n  \"id\" => \"SqjAsWIC12iIC9fUt63m\"\n]", "filament.livewire.notifications #8nIPYj99IhSeWW52G4Lp": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#6624\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"8nIPYj99IhSeWW52G4Lp\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/admin/profile", "action_name": "filament.admin.auth.profile", "controller_action": "Filament\\Auth\\Pages\\EditProfile", "uri": "GET admin/profile", "controller": "Filament\\Auth\\Pages\\EditProfile@render<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:49-57</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate, verified:filament.admin.auth.email-verification.prompt", "duration": "243ms", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1170 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlZBL0I4c3lZOXF4S3lHRzEzQ3pKTnc9PSIsInZhbHVlIjoiRUh3QzJFUzU5YitDSFNBSVE4SStuTXJ6RDcxZm1qKzlCVEdBMFRvdGZGWDJaZld6RFk3WVd1ZldGVGhQRnVob2ZxdHBJSC9ueDQ4WmZKdUR5dlNBYmlVSG5KbU1ldEZtd1VCaGhLYVluTnp1ZWV3NU5iQkFqVDM2Y20zVU1oRGkwUHRaRTAvZXpKcWZHQkZPWjVHeW56VXREWGlPT21aN25iZHorQ1BaWWdjPSIsIm1hYyI6ImRiNjI1ZDdjMjM4NGQ5YzMwMDhhZTUzMjY5OTlmNDU0YjdhYjk0OGNjNTVjNDhkZjNkNDQxYTViNDJkYzY1ZDciLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjdtN0Y1bThhMmlHUnI3bDVwZTZFb2c9PSIsInZhbHVlIjoiOG9uUnE5N3VldVFNQ3huUGZTU29CaUdpUG02cTZyK1FHK2EvdXFtYjVQRTI1RjNPUWlFZUdnOUtaL2pDOUFaMXZxZENsbXhJNFJrdzBsQXR3L3RGT25GRzlXK25RRzVKMEwwa0RXb0xEamRjeFZlZElKYmxrYk8rMVRpYkxLRk0iLCJtYWMiOiI0YjJkN2Q4NTYxODYyYzcxYzBlNDhkNjhiM2ZhOGNjZTc5MmFhNTgxMGVhNDBiN2MyYWRiMDU2NzEwNzliOWE1IiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6IkRpRnhQZjc1Rm1HanA3SkZhWGVlZVE9PSIsInZhbHVlIjoiUktKelhCRklmaE1Ba1JrZUV6VWtWRXluRWlhRVlzN2dGVHFZYmh5SERDZmZyd0dQeUlsMngrMjI2Tzk3aFlmVkdreWk4Si9uSEVnWUcxT0x1SlRPMEEyMnNtaEdmY0pCR3FJYlozOStDb0ZpUktIWWNWbFM3eU5iaE1EZ0JlVGUiLCJtYWMiOiJkNmYzMjA4YWQwMDlmMjZmYjg2YWZlM2U1ODlhNTY5MzViMWQ1YThhYjU0YTA3OGFmNmY4MDRkNjc5MTAxY2UzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://lfsl.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire-navigate</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-578784192 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"73 characters\">1|i7KPyT5afz|$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">En0ESbCdOv8mGck1kPu5UTU1yKyFhqCFYln5SFSX</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UHHHxcVErMSEx54Y0bWPgM0w7wDqwE1jtlIpN6pe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578784192\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1014694916 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 04:54:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6ImZmVmhQcjlVV04wYTZlaGZqeXR6VUE9PSIsInZhbHVlIjoiQk4zS0ZRcGhxdTVuYzltd281aGxJbmN4T05FOEhDalpQbFV5UTFKNWJoQ2hJdHhidXJ6MGV6anpSeDN2ZG10NkhRL2VWdFdOOGZ4MVpNTzJmR0ExZ1g2akZBc0toTE5FZDYzNEh5WTl4RWJkQkRlTHZlNXVoOGJ2QUV0ckJVSG8iLCJtYWMiOiI4YjRhNDFjZjlhMWMxYmEyOTJkMDIyMjliZDZkMjA1NDYzY2JjZTRmYmJiZjllZjNlYTU0NGExZWU5NDc1NWY0IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 06:54:13 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">lfsl_session=eyJpdiI6Imtpb0lJcGNrWGNWeVM2U1EvODVkRkE9PSIsInZhbHVlIjoiUDhOY3JQRVdmYVFkNU50blVYZncyMFI5L2t4dktsbVQyU3pJR2FER1hGNllrUlZqK3dlbkZ5RW1GYzdWdE5tbEo2akd5aVVVeGZNUWNDcy9tK0tuTXZBNWxsWDRabVVSZDNIUVJXbWM5dWp0YUNtTjVFdjNhWGp3N29nb2lCdEQiLCJtYWMiOiI3ZWNhNzE2ZDM1MTE3YWNiZGM2M2I0OWFkNmIyYzQyZjEyZmViZGNjNWExYzBkYTZkZTFjMzNlZjc1NjJiZDQ3IiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 06:54:13 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6ImZmVmhQcjlVV04wYTZlaGZqeXR6VUE9PSIsInZhbHVlIjoiQk4zS0ZRcGhxdTVuYzltd281aGxJbmN4T05FOEhDalpQbFV5UTFKNWJoQ2hJdHhidXJ6MGV6anpSeDN2ZG10NkhRL2VWdFdOOGZ4MVpNTzJmR0ExZ1g2akZBc0toTE5FZDYzNEh5WTl4RWJkQkRlTHZlNXVoOGJ2QUV0ckJVSG8iLCJtYWMiOiI4YjRhNDFjZjlhMWMxYmEyOTJkMDIyMjliZDZkMjA1NDYzY2JjZTRmYmJiZjllZjNlYTU0NGExZWU5NDc1NWY0IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 06:54:13 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">lfsl_session=eyJpdiI6Imtpb0lJcGNrWGNWeVM2U1EvODVkRkE9PSIsInZhbHVlIjoiUDhOY3JQRVdmYVFkNU50blVYZncyMFI5L2t4dktsbVQyU3pJR2FER1hGNllrUlZqK3dlbkZ5RW1GYzdWdE5tbEo2akd5aVVVeGZNUWNDcy9tK0tuTXZBNWxsWDRabVVSZDNIUVJXbWM5dWp0YUNtTjVFdjNhWGp3N29nb2lCdEQiLCJtYWMiOiI3ZWNhNzE2ZDM1MTE3YWNiZGM2M2I0OWFkNmIyYzQyZjEyZmViZGNjNWExYzBkYTZkZTFjMzNlZjc1NjJiZDQ3IiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 06:54:13 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014694916\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1581242237 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">En0ESbCdOv8mGck1kPu5UTU1yKyFhqCFYln5SFSX</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">https://lfsl.test/admin/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$JYeJDqm2hnXfySek4Jf/1eWgQOFLG0t.6QAqfGOih6EKMPFiWxghq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581242237\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/admin/profile", "action_name": "filament.admin.auth.profile", "controller_action": "Filament\\Auth\\Pages\\EditProfile"}, "badge": null}}