{"__meta": {"id": "01JZ1ZTZHSZT9894RWKW8RGXKG", "datetime": "2025-07-01 03:17:39", "utime": **********.51322, "method": "GET", "uri": "/livewire/livewire.js?id=df3a17f2", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.460652, "end": **********.513226, "duration": 0.05257391929626465, "duration_str": "52.57ms", "measures": [{"label": "Booting", "start": **********.460652, "relative_start": 0, "end": **********.51063, "relative_end": **********.51063, "duration": 0.049977779388427734, "duration_str": "49.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.510636, "relative_start": 0.049983978271484375, "end": **********.513227, "relative_end": 9.5367431640625e-07, "duration": 0.0025908946990966797, "duration_str": "2.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.511152, "relative_start": 0.050499916076660156, "end": **********.511241, "relative_end": **********.511241, "duration": 8.893013000488281e-05, "duration_str": "89μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.513073, "relative_start": 0.052420854568481445, "end": **********.513105, "relative_end": **********.513105, "duration": 3.1948089599609375e-05, "duration_str": "32μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.513112, "relative_start": 0.****************, "end": **********.513122, "relative_end": **********.513122, "duration": 1.0013580322265625e-05, "duration_str": "10μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 2321120, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/livewire.js?id=df3a17f2", "action_name": "generated::cGYzcNaFRKdTpPg9", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>", "duration": "49.46ms", "peak_memory": "4MB", "response": "application/javascript; charset=utf-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1009736636 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">df3a17f2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009736636\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-466865136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-466865136\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1252 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImEwTzdTYzZkM3dDT1VVc0NldGUvOUE9PSIsInZhbHVlIjoiVGJIZkk2TDlSZi9URm5mR21lTUZlVGFqMXV5RDBnLzJXdW5qSjA3aUFPV0MzZDBTRHgwbGYzblNJM0dIak0vYTRqa3lOWmZwQW9Qc3VaNXlBb1htNm10R29pVmQ1NGFzWGVQVk1XbVMyM01lWUhqVGt0dXdubG9aVWxjT1llN0NQUWF1cnUrTUpxZVFFTjIycjlZMzZBMXFFcFZXVGV5c0NtZ09KSGFiMHJHWHlKUEF5bU5pbXU0azRyK1FzVnN1NjlDcUJkWUorM2NEaUZocGRWZDUvZERnYlAyN0xqdHF1UGo1M3NyRW1sYz0iLCJtYWMiOiJkMzJkZWE1MTZjNDMxNGE1NjRhZjdlZmYzMjIzMzg3YjcwZjNmMzgzZjI3NmQzYWIzMzAyNDhhM2FlYTJiZjVlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjYxaldybzVWUmxGTFpYSURNQWNhdlE9PSIsInZhbHVlIjoiVFl6Z3Z3OGUvanhHa0JTdFRMR0ZwK1NzdjJ4V3F2Z3Z5M04rZzNKZFNxdGpUZVl4T1BEWStXcmpJbFZuNm5BWG95alBQNGxZR3lzVnNKRDV5bEd0Qm5YYXZaeHFDMFNUZUovRW1VKzI2Zzh0cE5vRkc4M0t2WWMxYWk1ZkxCNnUiLCJtYWMiOiI1NGVlYjZlN2ZiYjZlMzMxNzE5YjMzYzkxYWE4MTYzYTIxYjQ4MGFkNDQ0NzBjZDdkYzZkNmY4ZDc1MzE1YWMyIiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6Inl4V1NDZ2xWMHlqVXhNQ1JtcmRHL3c9PSIsInZhbHVlIjoiV0hFbG5BYjFvU3RrL1F5S3RiSm1qa3JBZlM1SjZJaFF6dWFEVi9CRUl1dnBPdkZveGFQMGViY1BVT3IxT2NpcWkxWDRMU3NFcDcwWFpPRDduaTFLYzZKTmVQbjRNa3E4SlZPZ3hYVmJ6ZzBhM21IQlRVTXVIUUgxSU54elNSSmUiLCJtYWMiOiJiOTcyOGE5OTExZGE3ZGE4YmI4MWY0OGQ3OWY1OWIzYjAwNjJkYjA1OGM4N2MyMmM4ZjE2ZWE1MTYyY2JmODFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">https://lfsl.test/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1923640527 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6ImEwTzdTYzZkM3dDT1VVc0NldGUvOUE9PSIsInZhbHVlIjoiVGJIZkk2TDlSZi9URm5mR21lTUZlVGFqMXV5RDBnLzJXdW5qSjA3aUFPV0MzZDBTRHgwbGYzblNJM0dIak0vYTRqa3lOWmZwQW9Qc3VaNXlBb1htNm10R29pVmQ1NGFzWGVQVk1XbVMyM01lWUhqVGt0dXdubG9aVWxjT1llN0NQUWF1cnUrTUpxZVFFTjIycjlZMzZBMXFFcFZXVGV5c0NtZ09KSGFiMHJHWHlKUEF5bU5pbXU0azRyK1FzVnN1NjlDcUJkWUorM2NEaUZocGRWZDUvZERnYlAyN0xqdHF1UGo1M3NyRW1sYz0iLCJtYWMiOiJkMzJkZWE1MTZjNDMxNGE1NjRhZjdlZmYzMjIzMzg3YjcwZjNmMzgzZjI3NmQzYWIzMzAyNDhhM2FlYTJiZjVlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjYxaldybzVWUmxGTFpYSURNQWNhdlE9PSIsInZhbHVlIjoiVFl6Z3Z3OGUvanhHa0JTdFRMR0ZwK1NzdjJ4V3F2Z3Z5M04rZzNKZFNxdGpUZVl4T1BEWStXcmpJbFZuNm5BWG95alBQNGxZR3lzVnNKRDV5bEd0Qm5YYXZaeHFDMFNUZUovRW1VKzI2Zzh0cE5vRkc4M0t2WWMxYWk1ZkxCNnUiLCJtYWMiOiI1NGVlYjZlN2ZiYjZlMzMxNzE5YjMzYzkxYWE4MTYzYTIxYjQ4MGFkNDQ0NzBjZDdkYzZkNmY4ZDc1MzE1YWMyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Inl4V1NDZ2xWMHlqVXhNQ1JtcmRHL3c9PSIsInZhbHVlIjoiV0hFbG5BYjFvU3RrL1F5S3RiSm1qa3JBZlM1SjZJaFF6dWFEVi9CRUl1dnBPdkZveGFQMGViY1BVT3IxT2NpcWkxWDRMU3NFcDcwWFpPRDduaTFLYzZKTmVQbjRNa3E4SlZPZ3hYVmJ6ZzBhM21IQlRVTXVIUUgxSU54elNSSmUiLCJtYWMiOiJiOTcyOGE5OTExZGE3ZGE4YmI4MWY0OGQ3OWY1OWIzYjAwNjJkYjA1OGM4N2MyMmM4ZjE2ZWE1MTYyY2JmODFiIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923640527\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1321733676 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 01 Jul 2026 03:17:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Apr 2025 22:26:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 03:17:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">347518</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321733676\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1137698894 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1137698894\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/livewire/livewire.js?id=df3a17f2", "action_name": "generated::cGYzcNaFRKdTpPg9", "controller_action": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile"}, "badge": null}}