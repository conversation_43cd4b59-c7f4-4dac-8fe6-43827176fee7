{"__meta": {"id": "01JZ1ZTZ89YY4RG0QN514N542M", "datetime": "2025-07-01 03:17:39", "utime": **********.209636, "method": "GET", "uri": "/admin/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.033171, "end": **********.209646, "duration": 0.17647504806518555, "duration_str": "176ms", "measures": [{"label": "Booting", "start": **********.033171, "relative_start": 0, "end": **********.058615, "relative_end": **********.058615, "duration": 0.*****************, "duration_str": "25.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.058622, "relative_start": 0.025450944900512695, "end": **********.209647, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.059237, "relative_start": 0.026066064834594727, "end": **********.059367, "relative_end": **********.059367, "duration": 0.00012993812561035156, "duration_str": "130μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::035f275254fb9861f9d8b48e028f4e98", "start": **********.088728, "relative_start": 0.****************, "end": **********.088728, "relative_end": **********.088728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.106038, "relative_start": 0.*****************, "end": **********.106038, "relative_end": **********.106038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da4024d4660ac6be041ce7ecc15ee307", "start": **********.110174, "relative_start": 0.07700300216674805, "end": **********.110174, "relative_end": **********.110174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": **********.113746, "relative_start": 0.08057498931884766, "end": **********.113746, "relative_end": **********.113746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.126679, "relative_start": 0.09350800514221191, "end": **********.126679, "relative_end": **********.126679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.134454, "relative_start": 0.10128307342529297, "end": **********.134474, "relative_end": **********.134474, "duration": 2.002716064453125e-05, "duration_str": "20μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.20933, "relative_start": 0.17615914344787598, "end": **********.209385, "relative_end": **********.209385, "duration": 5.4836273193359375e-05, "duration_str": "55μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 5099296, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.4.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "lfsl.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 5, "nb_templates": 5, "templates": [{"name": "__components::035f275254fb9861f9d8b48e028f4e98", "param_count": null, "params": [], "start": **********.088722, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/035f275254fb9861f9d8b48e028f4e98.blade.php__components::035f275254fb9861f9d8b48e028f4e98", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F035f275254fb9861f9d8b48e028f4e98.blade.php&line=1", "ajax": false, "filename": "035f275254fb9861f9d8b48e028f4e98.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.106033, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::da4024d4660ac6be041ce7ecc15ee307", "param_count": null, "params": [], "start": **********.11017, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/da4024d4660ac6be041ce7ecc15ee307.blade.php__components::da4024d4660ac6be041ce7ecc15ee307", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2Fda4024d4660ac6be041ce7ecc15ee307.blade.php&line=1", "ajax": false, "filename": "da4024d4660ac6be041ce7ecc15ee307.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.113742, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.126675, "type": "blade", "hash": "blade/Users/<USER>/Herd/lfsl/storage/framework/views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00076, "accumulated_duration_str": "760μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from \"sessions\" where \"id\" = 'KCjBUU7400GwCrPtf5tnO9H450Ml1suIFn6qsTMq' limit 1", "type": "query", "params": [], "bindings": ["KCjBUU7400GwCrPtf5tnO9H450Ml1suIFn6qsTMq"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 146}], "start": **********.0678341, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 0, "width_percent": 17.105}, {"sql": "update \"sessions\" set \"payload\" = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiUjQ4Um5IaDZOTjFwampwWlNqU2pPM3BPMGNqZmI4aU1GMEtBUFZ1UyI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czoyMzoiaHR0cHM6Ly9sZnNsLnRlc3QvYWRtaW4iO31zOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czoyOToiaHR0cHM6Ly9sZnNsLnRlc3QvYWRtaW4vbG9naW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', \"last_activity\" = **********, \"user_id\" = null, \"ip_address\" = '127.0.0.1', \"user_agent\" = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where \"id\" = 'KCjBUU7400GwCrPtf5tnO9H450Ml1suIFn6qsTMq'", "type": "query", "params": [], "bindings": ["YTo0OntzOjY6Il90b2tlbiI7czo0MDoiUjQ4Um5IaDZOTjFwampwWlNqU2pPM3BPMGNqZmI4aU1GMEtBUFZ1UyI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czoyMzoiaHR0cHM6Ly9sZnNsLnRlc3QvYWRtaW4iO31zOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czoyOToiaHR0cHM6Ly9sZnNsLnRlc3QvYWRtaW4vbG9naW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19", **********, null, "127.0.0.1", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "KCjBUU7400GwCrPtf5tnO9H450Ml1suIFn6qsTMq"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "line": 129}], "start": **********.208266, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "/Users/<USER>/Herd/lfsl/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "database/database.sqlite", "explain": null, "start_percent": 17.105, "width_percent": 82.895}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": {"filament.auth.pages.login #n0kLtMEoqc12E23qy16T": "array:4 [\n  \"data\" => array:9 [\n    \"data\" => array:3 [\n      \"email\" => null\n      \"password\" => null\n      \"remember\" => false\n    ]\n    \"userUndertakingMultiFactorAuthentication\" => null\n    \"mountedActions\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"defaultActionContext\" => null\n    \"componentFileAttachments\" => []\n    \"areSchemaStateUpdateHooksDisabledForTesting\" => false\n    \"discoveredSchemaNames\" => array:1 [\n      0 => \"form\"\n    ]\n  ]\n  \"name\" => \"filament.auth.pages.login\"\n  \"component\" => \"Filament\\Auth\\Pages\\Login\"\n  \"id\" => \"n0kLtMEoqc12E23qy16T\"\n]", "filament.livewire.notifications #NOgyR8YiBl1EmvZLPuW8": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#4328\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"NOgyR8YiBl1EmvZLPuW8\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://lfsl.test/admin/login", "action_name": "filament.admin.auth.login", "controller_action": "Filament\\Auth\\Pages\\Login", "uri": "GET admin/login", "controller": "Filament\\Auth\\Pages\\Login@render<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=%2FUsers%2Fs-a-c%2FHerd%2Flfsl%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=49\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:49-57</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent", "duration": "172ms", "peak_memory": "20MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"897 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImEwTzdTYzZkM3dDT1VVc0NldGUvOUE9PSIsInZhbHVlIjoiVGJIZkk2TDlSZi9URm5mR21lTUZlVGFqMXV5RDBnLzJXdW5qSjA3aUFPV0MzZDBTRHgwbGYzblNJM0dIak0vYTRqa3lOWmZwQW9Qc3VaNXlBb1htNm10R29pVmQ1NGFzWGVQVk1XbVMyM01lWUhqVGt0dXdubG9aVWxjT1llN0NQUWF1cnUrTUpxZVFFTjIycjlZMzZBMXFFcFZXVGV5c0NtZ09KSGFiMHJHWHlKUEF5bU5pbXU0azRyK1FzVnN1NjlDcUJkWUorM2NEaUZocGRWZDUvZERnYlAyN0xqdHF1UGo1M3NyRW1sYz0iLCJtYWMiOiJkMzJkZWE1MTZjNDMxNGE1NjRhZjdlZmYzMjIzMzg3YjcwZjNmMzgzZjI3NmQzYWIzMzAyNDhhM2FlYTJiZjVlIiwidGFnIjoiIn0%3D; lfsl_session=eyJpdiI6Iit5aHlnTnhIclR2UldwT2pEcnJOYlE9PSIsInZhbHVlIjoiMFJMYlkwa1F4bWlUcXl2YlR0QkVua1E3MFh1RFRQem80M2tNMkQ1aDVQY1Y4ajBKcWNQT0xiTDQwcExMRUdsOElVanJNNi9MclFTblJqOVRUSEQzdEZsNmx0K3J1ekUwc1JiczRHUXBINmsrVDF3Rms1aWtaWTBreTRITFZxS1UiLCJtYWMiOiIwNDgxMjlkNjUxNjgwMzFiN2Y5MGU3MzJjNmM0Yjg3YzI4M2NhOTg3MTM2NDcwODM2ZGM2OGY5ZjExZmI3NjVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">en-GB,en;q=0.9,en-US;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">&quot;macOS&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">&quot;Wavebox&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">lfsl.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-244922119 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>lfsl_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KCjBUU7400GwCrPtf5tnO9H450Ml1suIFn6qsTMq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244922119\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2133962245 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 01 Jul 2025 03:17:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IjYxaldybzVWUmxGTFpYSURNQWNhdlE9PSIsInZhbHVlIjoiVFl6Z3Z3OGUvanhHa0JTdFRMR0ZwK1NzdjJ4V3F2Z3Z5M04rZzNKZFNxdGpUZVl4T1BEWStXcmpJbFZuNm5BWG95alBQNGxZR3lzVnNKRDV5bEd0Qm5YYXZaeHFDMFNUZUovRW1VKzI2Zzh0cE5vRkc4M0t2WWMxYWk1ZkxCNnUiLCJtYWMiOiI1NGVlYjZlN2ZiYjZlMzMxNzE5YjMzYzkxYWE4MTYzYTIxYjQ4MGFkNDQ0NzBjZDdkYzZkNmY4ZDc1MzE1YWMyIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 05:17:39 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">lfsl_session=eyJpdiI6Inl4V1NDZ2xWMHlqVXhNQ1JtcmRHL3c9PSIsInZhbHVlIjoiV0hFbG5BYjFvU3RrL1F5S3RiSm1qa3JBZlM1SjZJaFF6dWFEVi9CRUl1dnBPdkZveGFQMGViY1BVT3IxT2NpcWkxWDRMU3NFcDcwWFpPRDduaTFLYzZKTmVQbjRNa3E4SlZPZ3hYVmJ6ZzBhM21IQlRVTXVIUUgxSU54elNSSmUiLCJtYWMiOiJiOTcyOGE5OTExZGE3ZGE4YmI4MWY0OGQ3OWY1OWIzYjAwNjJkYjA1OGM4N2MyMmM4ZjE2ZWE1MTYyY2JmODFiIiwidGFnIjoiIn0%3D; expires=Tue, 01 Jul 2025 05:17:39 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IjYxaldybzVWUmxGTFpYSURNQWNhdlE9PSIsInZhbHVlIjoiVFl6Z3Z3OGUvanhHa0JTdFRMR0ZwK1NzdjJ4V3F2Z3Z5M04rZzNKZFNxdGpUZVl4T1BEWStXcmpJbFZuNm5BWG95alBQNGxZR3lzVnNKRDV5bEd0Qm5YYXZaeHFDMFNUZUovRW1VKzI2Zzh0cE5vRkc4M0t2WWMxYWk1ZkxCNnUiLCJtYWMiOiI1NGVlYjZlN2ZiYjZlMzMxNzE5YjMzYzkxYWE4MTYzYTIxYjQ4MGFkNDQ0NzBjZDdkYzZkNmY4ZDc1MzE1YWMyIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 05:17:39 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">lfsl_session=eyJpdiI6Inl4V1NDZ2xWMHlqVXhNQ1JtcmRHL3c9PSIsInZhbHVlIjoiV0hFbG5BYjFvU3RrL1F5S3RiSm1qa3JBZlM1SjZJaFF6dWFEVi9CRUl1dnBPdkZveGFQMGViY1BVT3IxT2NpcWkxWDRMU3NFcDcwWFpPRDduaTFLYzZKTmVQbjRNa3E4SlZPZ3hYVmJ6ZzBhM21IQlRVTXVIUUgxSU54elNSSmUiLCJtYWMiOiJiOTcyOGE5OTExZGE3ZGE4YmI4MWY0OGQ3OWY1OWIzYjAwNjJkYjA1OGM4N2MyMmM4ZjE2ZWE1MTYyY2JmODFiIiwidGFnIjoiIn0%3D; expires=Tue, 01-Jul-2025 05:17:39 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133962245\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-556188599 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R48RnHh6NN1pjjpZSjSjO3pO0cjfb8iMF0KAPVuS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"23 characters\">https://lfsl.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">https://lfsl.test/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556188599\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://lfsl.test/admin/login", "action_name": "filament.admin.auth.login", "controller_action": "Filament\\Auth\\Pages\\Login"}, "badge": null}}