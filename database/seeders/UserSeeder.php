<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'System User',
            'email' => '<EMAIL>',
        ]);

        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        User::factory(10)->create();
    }
}
