<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Field definitions for the migration - aligned with User model
     */
    private function getFieldDefinitions(): array
    {
        return [
            'public_id' => [
                'type' => 'string',
                'length' => 26,
                'nullable' => true,
                'unique' => true,
                'after' => 'id',
                'index' => true,
            ],
            'slug' => [
                'type' => 'string',
                'length' => 100,
                'nullable' => true,
                'unique' => true,
                'after' => 'name',
                'index' => true,
            ],
            'deleted_at' => [
                'type' => 'softDeletes',
                'nullable' => true,
            ],
            'created_by' => [
                'type' => 'unsignedBigInteger',
                'nullable' => true,
                'after' => 'created_at',
                'index' => true,
                'foreign' => ['references' => 'id', 'on' => 'users', 'onDelete' => 'set null'],
            ],
            'updated_by' => [
                'type' => 'unsignedBigInteger',
                'nullable' => true,
                'after' => 'updated_at',
                'index' => true,
                'foreign' => ['references' => 'id', 'on' => 'users', 'onDelete' => 'set null'],
            ],
            'deleted_by' => [
                'type' => 'unsignedBigInteger',
                'nullable' => true,
                'after' => 'deleted_at',
                'index' => true,
                'foreign' => ['references' => 'id', 'on' => 'users', 'onDelete' => 'set null'],
            ],
            'app_authentication_secret' => [
                'type' => 'text',
                'nullable' => true,
                'after' => 'remember_token',
            ],
            'app_authentication_recovery_codes' => [
                'type' => 'text',
                'nullable' => true,
                'after' => 'app_authentication_secret',
            ],
            'has_email_authentication' => [
                'type' => 'boolean',
                'nullable' => false,
                'default' => false,
                'after' => 'app_authentication_recovery_codes',
            ],
        ];
    }

    /**
     * Add a column based on field definition with enhanced type support
     */
    private function addColumn(Blueprint $table, string $fieldName, array $definition): void
    {
        $column = match ($definition['type']) {
            'string' => $table->string($fieldName, $definition['length'] ?? 255),
            'text' => $table->text($fieldName),
            'boolean' => $table->boolean($fieldName),
            'unsignedBigInteger' => $table->unsignedBigInteger($fieldName),
            'softDeletes' => $table->softDeletes(),
            default => throw new \InvalidArgumentException("Unsupported column type: {$definition['type']}")
        };

        // Apply modifiers only if column was created (not for softDeletes)
        if ($definition['type'] !== 'softDeletes') {
            $this->applyColumnModifiers($column, $definition);
        }
    }

    /**
     * Apply column modifiers using match expressions
     */
    private function applyColumnModifiers($column, array $definition): void
    {
        // Apply modifiers based on definition
        collect($definition)->each(function ($value, $key) use ($column) {
            match ($key) {
                'nullable' => $value ? $column->nullable() : null,
                'unique' => $value ? $column->unique() : null,
                'default' => $column->default($value),
                'after' => $column->after($value),
                default => null // Ignore other keys
            };
        });
    }

    /**
     * Run the migrations with enhanced resilience and Eloquent features
     */
    public function up(): void
    {
        $fields = $this->getFieldDefinitions();

        // Add columns for fields that don't exist
        $this->addMissingColumns($fields);

        // Add indexes and foreign keys with built-in Laravel resilience
        $this->addConstraints($fields);
    }

    /**
     * Add missing columns using Eloquent collection methods
     */
    private function addMissingColumns(array $fields): void
    {
        $missingFields = collect($fields)
            ->filter(fn($definition, $fieldName) => !Schema::hasColumn('users', $fieldName));

        if ($missingFields->isEmpty()) {
            return;
        }

        Schema::table('users', function (Blueprint $table) use ($missingFields) {
            $missingFields->each(fn($definition, $fieldName) =>
                $this->addColumn($table, $fieldName, $definition)
            );
        });
    }

    /**
     * Add constraints (indexes and foreign keys) with minimal try/catch
     */
    private function addConstraints(array $fields): void
    {
        $fieldsWithConstraints = collect($fields)
            ->filter(function ($definition, $fieldName) {
                if (!Schema::hasColumn('users', $fieldName)) {
                    return false;
                }
                return ($definition['index'] ?? false) || isset($definition['foreign']);
            });

        if ($fieldsWithConstraints->isEmpty()) {
            return;
        }

        Schema::table('users', function (Blueprint $table) use ($fieldsWithConstraints) {
            $fieldsWithConstraints->each(function ($definition, $fieldName) use ($table) {
                $this->addConstraintSafely($table, $fieldName, $definition);
            });
        });
    }

    /**
     * Add constraint safely using match expression for constraint types
     */
    private function addConstraintSafely(Blueprint $table, string $fieldName, array $definition): void
    {
        // Use Laravel's built-in resilience - it handles existing constraints gracefully
        match (true) {
            ($definition['index'] ?? false) => $this->safelyExecute(
                fn() => $table->index($fieldName)
            ),
            isset($definition['foreign']) => $this->safelyExecute(
                fn() => $this->addForeignKey($table, $fieldName, $definition['foreign'])
            ),
            default => null
        };
    }

    /**
     * Add foreign key constraint
     */
    private function addForeignKey(Blueprint $table, string $fieldName, array $foreign): void
    {
        $table->foreign($fieldName)
            ->references($foreign['references'])
            ->on($foreign['on'])
            ->onDelete($foreign['onDelete']);
    }

    /**
     * Execute operation safely with minimal try/catch
     */
    private function safelyExecute(callable $operation): bool
    {
        try {
            $operation();
            return true;
        } catch (\Throwable) {
            // Laravel handles most constraint conflicts gracefully
            // Silent failure maintains resilience without verbose error handling
            return false;
        }
    }


    /**
     * Reverse the migrations with enhanced Eloquent features
     */
    public function down(): void
    {
        $fields = $this->getFieldDefinitions();

        // Drop constraints first (foreign keys and indexes)
        $this->dropConstraints($fields);

        // Drop columns using optimized bulk operations
        $this->dropColumnsOptimized($fields);
    }

    /**
     * Drop constraints using Eloquent collections and match expressions
     */
    private function dropConstraints(array $fields): void
    {
        $fieldsWithConstraints = collect($fields)
            ->filter(fn($definition, $fieldName) =>
                Schema::hasColumn('users', $fieldName) &&
                (isset($definition['foreign']) || ($definition['index'] ?? false))
            );

        if ($fieldsWithConstraints->isEmpty()) {
            return;
        }

        Schema::table('users', function (Blueprint $table) use ($fieldsWithConstraints) {
            $fieldsWithConstraints->each(function ($definition, $fieldName) use ($table) {
                $this->dropConstraintSafely($table, $fieldName, $definition);
            });
        });
    }

    /**
     * Drop constraint safely using match expression
     */
    private function dropConstraintSafely(Blueprint $table, string $fieldName, array $definition): void
    {
        match (true) {
            isset($definition['foreign']) => $this->safelyExecute(
                fn() => $table->dropForeign([$fieldName])
            ),
            ($definition['index'] ?? false) => $this->safelyExecute(
                fn() => $this->dropIndexWithFallback($table, $fieldName, $definition['unique'] ?? false)
            ),
            default => null
        };
    }

    /**
     * Drop index with multiple naming convention fallbacks
     */
    private function dropIndexWithFallback(Blueprint $table, string $fieldName, bool $isUnique = false): void
    {
        $indexNames = collect([
            $fieldName,
            "users_{$fieldName}_index",
            $isUnique ? "users_{$fieldName}_unique" : null,
        ])->filter();

        $indexNames->each(function ($indexName) use ($table) {
            try {
                $table->dropIndex([$indexName]);
                return false; // Stop iteration on success
            } catch (\Throwable) {
                return true; // Continue to next naming convention
            }
        });
    }

    /**
     * Drop columns using optimized bulk operations with Eloquent collections
     */
    private function dropColumnsOptimized(array $fields): void
    {
        $columnsToDrop = collect($fields)
            ->keys()
            ->filter(fn($fieldName) => Schema::hasColumn('users', $fieldName));

        if ($columnsToDrop->isEmpty()) {
            return;
        }

        // Try bulk operation first, fallback to individual drops if it fails
        $bulkSuccess = $this->safelyExecute(function () use ($columnsToDrop) {
            Schema::table('users', function (Blueprint $table) use ($columnsToDrop) {
                $table->dropColumn($columnsToDrop->toArray());
            });
        });

        if (!$bulkSuccess) {
            $this->dropColumnsIndividually($columnsToDrop);
        }
    }

    /**
     * Fallback to individual column drops using Eloquent collections
     */
    private function dropColumnsIndividually($columns): void
    {
        $columns->each(function ($column) {
            if (Schema::hasColumn('users', $column)) {
                $this->safelyExecute(function () use ($column) {
                    Schema::table('users', function (Blueprint $table) use ($column) {
                        $table->dropColumn($column);
                    });
                });
            }
        });
    }
};
