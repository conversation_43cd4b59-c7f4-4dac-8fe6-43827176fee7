var tt=Math.min,F=Math.max,et=Math.round;var E=i=>({x:i,y:i}),Gt={left:"right",right:"left",bottom:"top",top:"bottom"},Xt={start:"end",end:"start"};function pt(i,t,e){return F(i,tt(t,e))}function it(i,t){return typeof i=="function"?i(t):i}function H(i){return i.split("-")[0]}function st(i){return i.split("-")[1]}function ut(i){return i==="x"?"y":"x"}function mt(i){return i==="y"?"height":"width"}var Qt=new Set(["top","bottom"]);function k(i){return Qt.has(H(i))?"y":"x"}function gt(i){return ut(k(i))}function Ot(i,t,e){e===void 0&&(e=!1);let s=st(i),n=gt(i),o=mt(n),r=n==="x"?s===(e?"end":"start")?"right":"left":s==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(r=Z(r)),[r,Z(r)]}function At(i){let t=Z(i);return[lt(i),t,lt(t)]}function lt(i){return i.replace(/start|end/g,t=>Xt[t])}var vt=["left","right"],Lt=["right","left"],Zt=["top","bottom"],te=["bottom","top"];function ee(i,t,e){switch(i){case"top":case"bottom":return e?t?Lt:vt:t?vt:Lt;case"left":case"right":return t?Zt:te;default:return[]}}function St(i,t,e,s){let n=st(i),o=ee(H(i),e==="start",s);return n&&(o=o.map(r=>r+"-"+n),t&&(o=o.concat(o.map(lt)))),o}function Z(i){return i.replace(/left|right|bottom|top/g,t=>Gt[t])}function ie(i){return{top:0,right:0,bottom:0,left:0,...i}}function Ct(i){return typeof i!="number"?ie(i):{top:i,right:i,bottom:i,left:i}}function U(i){let{x:t,y:e,width:s,height:n}=i;return{width:s,height:n,top:e,left:t,right:t+s,bottom:e+n,x:t,y:e}}function Dt(i,t,e){let{reference:s,floating:n}=i,o=k(t),r=gt(t),l=mt(r),a=H(t),c=o==="y",h=s.x+s.width/2-n.width/2,d=s.y+s.height/2-n.height/2,p=s[l]/2-n[l]/2,f;switch(a){case"top":f={x:h,y:s.y-n.height};break;case"bottom":f={x:h,y:s.y+s.height};break;case"right":f={x:s.x+s.width,y:d};break;case"left":f={x:s.x-n.width,y:d};break;default:f={x:s.x,y:s.y}}switch(st(t)){case"start":f[r]-=p*(e&&c?-1:1);break;case"end":f[r]+=p*(e&&c?-1:1);break}return f}var Et=async(i,t,e)=>{let{placement:s="bottom",strategy:n="absolute",middleware:o=[],platform:r}=e,l=o.filter(Boolean),a=await(r.isRTL==null?void 0:r.isRTL(t)),c=await r.getElementRects({reference:i,floating:t,strategy:n}),{x:h,y:d}=Dt(c,s,a),p=s,f={},u=0;for(let m=0;m<l.length;m++){let{name:g,fn:y}=l[m],{x:b,y:v,data:L,reset:x}=await y({x:h,y:d,initialPlacement:s,placement:p,strategy:n,middlewareData:f,rects:c,platform:r,elements:{reference:i,floating:t}});h=b??h,d=v??d,f={...f,[g]:{...f[g],...L}},x&&u<=50&&(u++,typeof x=="object"&&(x.placement&&(p=x.placement),x.rects&&(c=x.rects===!0?await r.getElementRects({reference:i,floating:t,strategy:n}):x.rects),{x:h,y:d}=Dt(c,p,a)),m=-1)}return{x:h,y:d,placement:p,strategy:n,middlewareData:f}};async function bt(i,t){var e;t===void 0&&(t={});let{x:s,y:n,platform:o,rects:r,elements:l,strategy:a}=i,{boundary:c="clippingAncestors",rootBoundary:h="viewport",elementContext:d="floating",altBoundary:p=!1,padding:f=0}=it(t,i),u=Ct(f),g=l[p?d==="floating"?"reference":"floating":d],y=U(await o.getClippingRect({element:(e=await(o.isElement==null?void 0:o.isElement(g)))==null||e?g:g.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(l.floating)),boundary:c,rootBoundary:h,strategy:a})),b=d==="floating"?{x:s,y:n,width:r.floating.width,height:r.floating.height}:r.reference,v=await(o.getOffsetParent==null?void 0:o.getOffsetParent(l.floating)),L=await(o.isElement==null?void 0:o.isElement(v))?await(o.getScale==null?void 0:o.getScale(v))||{x:1,y:1}:{x:1,y:1},x=U(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:b,offsetParent:v,strategy:a}):b);return{top:(y.top-x.top+u.top)/L.y,bottom:(x.bottom-y.bottom+u.bottom)/L.y,left:(y.left-x.left+u.left)/L.x,right:(x.right-y.right+u.right)/L.x}}var Rt=function(i){return i===void 0&&(i={}),{name:"flip",options:i,async fn(t){var e,s;let{placement:n,middlewareData:o,rects:r,initialPlacement:l,platform:a,elements:c}=t,{mainAxis:h=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:u="none",flipAlignment:m=!0,...g}=it(i,t);if((e=o.arrow)!=null&&e.alignmentOffset)return{};let y=H(n),b=k(l),v=H(l)===l,L=await(a.isRTL==null?void 0:a.isRTL(c.floating)),x=p||(v||!m?[Z(l)]:At(l)),q=u!=="none";!p&&q&&x.push(...St(l,m,u,L));let X=[l,...x],V=await bt(t,g),B=[],I=((s=o.flip)==null?void 0:s.overflows)||[];if(h&&B.push(V[y]),d){let O=Ot(n,r,L);B.push(V[O[0]],V[O[1]])}if(I=[...I,{placement:n,overflows:B}],!B.every(O=>O<=0)){var J,j;let O=(((J=o.flip)==null?void 0:J.index)||0)+1,Q=X[O];if(Q&&(!(d==="alignment"?b!==k(Q):!1)||I.every(D=>D.overflows[0]>0&&k(D.placement)===b)))return{data:{index:O,overflows:I},reset:{placement:Q}};let $=(j=I.filter(W=>W.overflows[0]<=0).sort((W,D)=>W.overflows[1]-D.overflows[1])[0])==null?void 0:j.placement;if(!$)switch(f){case"bestFit":{var z;let W=(z=I.filter(D=>{if(q){let N=k(D.placement);return N===b||N==="y"}return!0}).map(D=>[D.placement,D.overflows.filter(N=>N>0).reduce((N,Yt)=>N+Yt,0)]).sort((D,N)=>D[1]-N[1])[0])==null?void 0:z[0];W&&($=W);break}case"initialPlacement":$=l;break}if(n!==$)return{reset:{placement:$}}}return{}}}};var se=new Set(["left","top"]);async function ne(i,t){let{placement:e,platform:s,elements:n}=i,o=await(s.isRTL==null?void 0:s.isRTL(n.floating)),r=H(e),l=st(e),a=k(e)==="y",c=se.has(r)?-1:1,h=o&&a?-1:1,d=it(t,i),{mainAxis:p,crossAxis:f,alignmentAxis:u}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&typeof u=="number"&&(f=l==="end"?u*-1:u),a?{x:f*h,y:p*c}:{x:p*c,y:f*h}}var It=function(i){return i===void 0&&(i=0),{name:"offset",options:i,async fn(t){var e,s;let{x:n,y:o,placement:r,middlewareData:l}=t,a=await ne(t,i);return r===((e=l.offset)==null?void 0:e.placement)&&(s=l.arrow)!=null&&s.alignmentOffset?{}:{x:n+a.x,y:o+a.y,data:{...a,placement:r}}}}},Tt=function(i){return i===void 0&&(i={}),{name:"shift",options:i,async fn(t){let{x:e,y:s,placement:n}=t,{mainAxis:o=!0,crossAxis:r=!1,limiter:l={fn:g=>{let{x:y,y:b}=g;return{x:y,y:b}}},...a}=it(i,t),c={x:e,y:s},h=await bt(t,a),d=k(H(n)),p=ut(d),f=c[p],u=c[d];if(o){let g=p==="y"?"top":"left",y=p==="y"?"bottom":"right",b=f+h[g],v=f-h[y];f=pt(b,f,v)}if(r){let g=d==="y"?"top":"left",y=d==="y"?"bottom":"right",b=u+h[g],v=u-h[y];u=pt(b,u,v)}let m=l.fn({...t,[p]:f,[d]:u});return{...m,data:{x:m.x-e,y:m.y-s,enabled:{[p]:o,[d]:r}}}}}};function ct(){return typeof window<"u"}function K(i){return Mt(i)?(i.nodeName||"").toLowerCase():"#document"}function A(i){var t;return(i==null||(t=i.ownerDocument)==null?void 0:t.defaultView)||window}function T(i){var t;return(t=(Mt(i)?i.ownerDocument:i.document)||window.document)==null?void 0:t.documentElement}function Mt(i){return ct()?i instanceof Node||i instanceof A(i).Node:!1}function S(i){return ct()?i instanceof Element||i instanceof A(i).Element:!1}function R(i){return ct()?i instanceof HTMLElement||i instanceof A(i).HTMLElement:!1}function kt(i){return!ct()||typeof ShadowRoot>"u"?!1:i instanceof ShadowRoot||i instanceof A(i).ShadowRoot}var oe=new Set(["inline","contents"]);function Y(i){let{overflow:t,overflowX:e,overflowY:s,display:n}=C(i);return/auto|scroll|overlay|hidden|clip/.test(t+s+e)&&!oe.has(n)}var re=new Set(["table","td","th"]);function Pt(i){return re.has(K(i))}var le=[":popover-open",":modal"];function nt(i){return le.some(t=>{try{return i.matches(t)}catch{return!1}})}var ae=["transform","translate","scale","rotate","perspective"],ce=["transform","translate","scale","rotate","perspective","filter"],de=["paint","layout","strict","content"];function dt(i){let t=ht(),e=S(i)?C(i):i;return ae.some(s=>e[s]?e[s]!=="none":!1)||(e.containerType?e.containerType!=="normal":!1)||!t&&(e.backdropFilter?e.backdropFilter!=="none":!1)||!t&&(e.filter?e.filter!=="none":!1)||ce.some(s=>(e.willChange||"").includes(s))||de.some(s=>(e.contain||"").includes(s))}function Bt(i){let t=M(i);for(;R(t)&&!_(t);){if(dt(t))return t;if(nt(t))return null;t=M(t)}return null}function ht(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}var he=new Set(["html","body","#document"]);function _(i){return he.has(K(i))}function C(i){return A(i).getComputedStyle(i)}function ot(i){return S(i)?{scrollLeft:i.scrollLeft,scrollTop:i.scrollTop}:{scrollLeft:i.scrollX,scrollTop:i.scrollY}}function M(i){if(K(i)==="html")return i;let t=i.assignedSlot||i.parentNode||kt(i)&&i.host||T(i);return kt(t)?t.host:t}function Nt(i){let t=M(i);return _(t)?i.ownerDocument?i.ownerDocument.body:i.body:R(t)&&Y(t)?t:Nt(t)}function at(i,t,e){var s;t===void 0&&(t=[]),e===void 0&&(e=!0);let n=Nt(i),o=n===((s=i.ownerDocument)==null?void 0:s.body),r=A(n);if(o){let l=ft(r);return t.concat(r,r.visualViewport||[],Y(n)?n:[],l&&e?at(l):[])}return t.concat(n,at(n,[],e))}function ft(i){return i.parent&&Object.getPrototypeOf(i.parent)?i.frameElement:null}function Vt(i){let t=C(i),e=parseFloat(t.width)||0,s=parseFloat(t.height)||0,n=R(i),o=n?i.offsetWidth:e,r=n?i.offsetHeight:s,l=et(e)!==o||et(s)!==r;return l&&(e=o,s=r),{width:e,height:s,$:l}}function zt(i){return S(i)?i:i.contextElement}function G(i){let t=zt(i);if(!R(t))return E(1);let e=t.getBoundingClientRect(),{width:s,height:n,$:o}=Vt(t),r=(o?et(e.width):e.width)/s,l=(o?et(e.height):e.height)/n;return(!r||!Number.isFinite(r))&&(r=1),(!l||!Number.isFinite(l))&&(l=1),{x:r,y:l}}var fe=E(0);function $t(i){let t=A(i);return!ht()||!t.visualViewport?fe:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function pe(i,t,e){return t===void 0&&(t=!1),!e||t&&e!==A(i)?!1:t}function rt(i,t,e,s){t===void 0&&(t=!1),e===void 0&&(e=!1);let n=i.getBoundingClientRect(),o=zt(i),r=E(1);t&&(s?S(s)&&(r=G(s)):r=G(i));let l=pe(o,e,s)?$t(o):E(0),a=(n.left+l.x)/r.x,c=(n.top+l.y)/r.y,h=n.width/r.x,d=n.height/r.y;if(o){let p=A(o),f=s&&S(s)?A(s):s,u=p,m=ft(u);for(;m&&s&&f!==u;){let g=G(m),y=m.getBoundingClientRect(),b=C(m),v=y.left+(m.clientLeft+parseFloat(b.paddingLeft))*g.x,L=y.top+(m.clientTop+parseFloat(b.paddingTop))*g.y;a*=g.x,c*=g.y,h*=g.x,d*=g.y,a+=v,c+=L,u=A(m),m=ft(u)}}return U({width:h,height:d,x:a,y:c})}function yt(i,t){let e=ot(i).scrollLeft;return t?t.left+e:rt(T(i)).left+e}function Wt(i,t,e){e===void 0&&(e=!1);let s=i.getBoundingClientRect(),n=s.left+t.scrollLeft-(e?0:yt(i,s)),o=s.top+t.scrollTop;return{x:n,y:o}}function ue(i){let{elements:t,rect:e,offsetParent:s,strategy:n}=i,o=n==="fixed",r=T(s),l=t?nt(t.floating):!1;if(s===r||l&&o)return e;let a={scrollLeft:0,scrollTop:0},c=E(1),h=E(0),d=R(s);if((d||!d&&!o)&&((K(s)!=="body"||Y(r))&&(a=ot(s)),R(s))){let f=rt(s);c=G(s),h.x=f.x+s.clientLeft,h.y=f.y+s.clientTop}let p=r&&!d&&!o?Wt(r,a,!0):E(0);return{width:e.width*c.x,height:e.height*c.y,x:e.x*c.x-a.scrollLeft*c.x+h.x+p.x,y:e.y*c.y-a.scrollTop*c.y+h.y+p.y}}function me(i){return Array.from(i.getClientRects())}function ge(i){let t=T(i),e=ot(i),s=i.ownerDocument.body,n=F(t.scrollWidth,t.clientWidth,s.scrollWidth,s.clientWidth),o=F(t.scrollHeight,t.clientHeight,s.scrollHeight,s.clientHeight),r=-e.scrollLeft+yt(i),l=-e.scrollTop;return C(s).direction==="rtl"&&(r+=F(t.clientWidth,s.clientWidth)-n),{width:n,height:o,x:r,y:l}}function be(i,t){let e=A(i),s=T(i),n=e.visualViewport,o=s.clientWidth,r=s.clientHeight,l=0,a=0;if(n){o=n.width,r=n.height;let c=ht();(!c||c&&t==="fixed")&&(l=n.offsetLeft,a=n.offsetTop)}return{width:o,height:r,x:l,y:a}}var we=new Set(["absolute","fixed"]);function ye(i,t){let e=rt(i,!0,t==="fixed"),s=e.top+i.clientTop,n=e.left+i.clientLeft,o=R(i)?G(i):E(1),r=i.clientWidth*o.x,l=i.clientHeight*o.y,a=n*o.x,c=s*o.y;return{width:r,height:l,x:a,y:c}}function Ft(i,t,e){let s;if(t==="viewport")s=be(i,e);else if(t==="document")s=ge(T(i));else if(S(t))s=ye(t,e);else{let n=$t(i);s={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return U(s)}function Ut(i,t){let e=M(i);return e===t||!S(e)||_(e)?!1:C(e).position==="fixed"||Ut(e,t)}function xe(i,t){let e=t.get(i);if(e)return e;let s=at(i,[],!1).filter(l=>S(l)&&K(l)!=="body"),n=null,o=C(i).position==="fixed",r=o?M(i):i;for(;S(r)&&!_(r);){let l=C(r),a=dt(r);!a&&l.position==="fixed"&&(n=null),(o?!a&&!n:!a&&l.position==="static"&&!!n&&we.has(n.position)||Y(r)&&!a&&Ut(i,r))?s=s.filter(h=>h!==r):n=l,r=M(r)}return t.set(i,s),s}function ve(i){let{element:t,boundary:e,rootBoundary:s,strategy:n}=i,r=[...e==="clippingAncestors"?nt(t)?[]:xe(t,this._c):[].concat(e),s],l=r[0],a=r.reduce((c,h)=>{let d=Ft(t,h,n);return c.top=F(d.top,c.top),c.right=tt(d.right,c.right),c.bottom=tt(d.bottom,c.bottom),c.left=F(d.left,c.left),c},Ft(t,l,n));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Le(i){let{width:t,height:e}=Vt(i);return{width:t,height:e}}function Oe(i,t,e){let s=R(t),n=T(t),o=e==="fixed",r=rt(i,!0,o,t),l={scrollLeft:0,scrollTop:0},a=E(0);function c(){a.x=yt(n)}if(s||!s&&!o)if((K(t)!=="body"||Y(n))&&(l=ot(t)),s){let f=rt(t,!0,o,t);a.x=f.x+t.clientLeft,a.y=f.y+t.clientTop}else n&&c();o&&!s&&n&&c();let h=n&&!s&&!o?Wt(n,l):E(0),d=r.left+l.scrollLeft-a.x-h.x,p=r.top+l.scrollTop-a.y-h.y;return{x:d,y:p,width:r.width,height:r.height}}function wt(i){return C(i).position==="static"}function Ht(i,t){if(!R(i)||C(i).position==="fixed")return null;if(t)return t(i);let e=i.offsetParent;return T(i)===e&&(e=e.ownerDocument.body),e}function Kt(i,t){let e=A(i);if(nt(i))return e;if(!R(i)){let n=M(i);for(;n&&!_(n);){if(S(n)&&!wt(n))return n;n=M(n)}return e}let s=Ht(i,t);for(;s&&Pt(s)&&wt(s);)s=Ht(s,t);return s&&_(s)&&wt(s)&&!dt(s)?e:s||Bt(i)||e}var Ae=async function(i){let t=this.getOffsetParent||Kt,e=this.getDimensions,s=await e(i.floating);return{reference:Oe(i.reference,await t(i.floating),i.strategy),floating:{x:0,y:0,width:s.width,height:s.height}}};function Se(i){return C(i).direction==="rtl"}var Ce={convertOffsetParentRelativeRectToViewportRelativeRect:ue,getDocumentElement:T,getClippingRect:ve,getOffsetParent:Kt,getElementRects:Ae,getClientRects:me,getDimensions:Le,getScale:G,isElement:S,isRTL:Se};var _t=It;var qt=Tt,Jt=Rt;var jt=(i,t,e)=>{let s=new Map,n={platform:Ce,...e},o={...n.platform,_c:s};return Et(i,t,{...n,platform:o})};function P(i){return i==null||i===""||typeof i=="string"&&i.trim()===""}function w(i){return!P(i)}function De({canSelectPlaceholder:i,isHtmlAllowed:t,getOptionLabelUsing:e,getOptionLabelsUsing:s,getOptionsUsing:n,getSearchResultsUsing:o,initialOptionLabel:r,initialOptionLabels:l,initialState:a,isAutofocused:c,isDisabled:h,isMultiple:d,isSearchable:p,hasDynamicOptions:f,hasDynamicSearchResults:u,livewireId:m,loadingMessage:g,maxItems:y,maxItemsMessage:b,noSearchResultsMessage:v,options:L,optionsLimit:x,placeholder:q,position:X,searchDebounce:V,searchingMessage:B,searchPrompt:I,searchableOptionFields:J,state:j,statePath:z}){return{state:j,select:null,init(){this.select=new xt({element:this.$refs.select,options:L,placeholder:q,state:this.state,canSelectPlaceholder:i,initialOptionLabel:r,initialOptionLabels:l,initialState:a,isHtmlAllowed:t,isAutofocused:c,isDisabled:h,isMultiple:d,isSearchable:p,getOptionLabelUsing:e,getOptionLabelsUsing:s,getOptionsUsing:n,getSearchResultsUsing:o,hasDynamicOptions:f,hasDynamicSearchResults:u,searchPrompt:I,searchDebounce:V,loadingMessage:g,searchingMessage:B,noSearchResultsMessage:v,maxItems:y,maxItemsMessage:b,optionsLimit:x,position:X,searchableOptionFields:J,livewireId:m,statePath:z,onStateChange:O=>{this.state=O}}),this.$watch("state",O=>{this.select&&this.select.state!==O&&(this.select.state=O,this.select.updateSelectedDisplay(),this.select.renderOptions())})},destroy(){this.select&&(this.select.destroy(),this.select=null)}}}var xt=class{constructor({element:t,options:e,placeholder:s,state:n,canSelectPlaceholder:o=!0,initialOptionLabel:r=null,initialOptionLabels:l=null,initialState:a=null,isHtmlAllowed:c=!1,isAutofocused:h=!1,isDisabled:d=!1,isMultiple:p=!1,isSearchable:f=!1,getOptionLabelUsing:u=null,getOptionLabelsUsing:m=null,getOptionsUsing:g=null,getSearchResultsUsing:y=null,hasDynamicOptions:b=!1,hasDynamicSearchResults:v=!0,searchPrompt:L="Search...",searchDebounce:x=1e3,loadingMessage:q="Loading...",searchingMessage:X="Searching...",noSearchResultsMessage:V="No results found",maxItems:B=null,maxItemsMessage:I="Maximum number of items selected",optionsLimit:J=null,position:j=null,searchableOptionFields:z=["label"],livewireId:O=null,statePath:Q=null,onStateChange:$=()=>{}}){this.element=t,this.options=e,this.originalOptions=JSON.parse(JSON.stringify(e)),this.placeholder=s,this.state=n,this.canSelectPlaceholder=o,this.initialOptionLabel=r,this.initialOptionLabels=l,this.initialState=a,this.isHtmlAllowed=c,this.isAutofocused=h,this.isDisabled=d,this.isMultiple=p,this.isSearchable=f,this.getOptionLabelUsing=u,this.getOptionLabelsUsing=m,this.getOptionsUsing=g,this.getSearchResultsUsing=y,this.hasDynamicOptions=b,this.hasDynamicSearchResults=v,this.searchPrompt=L,this.searchDebounce=x,this.loadingMessage=q,this.searchingMessage=X,this.noSearchResultsMessage=V,this.maxItems=B,this.maxItemsMessage=I,this.optionsLimit=J,this.position=j,this.searchableOptionFields=Array.isArray(z)?z:["label"],this.livewireId=O,this.statePath=Q,this.onStateChange=$,this.labelRepository={},this.isOpen=!1,this.selectedIndex=-1,this.searchQuery="",this.searchTimeout=null,this.render(),this.setUpEventListeners(),this.isAutofocused&&this.selectButton.focus()}populateLabelRepositoryFromOptions(t){if(!(!t||!Array.isArray(t)))for(let e of t)e.options&&Array.isArray(e.options)?this.populateLabelRepositoryFromOptions(e.options):e.value!==void 0&&e.label!==void 0&&(this.labelRepository[e.value]=e.label)}render(){this.populateLabelRepositoryFromOptions(this.options),this.container=document.createElement("div"),this.container.className="fi-fo-select-ctn",this.container.setAttribute("aria-haspopup","listbox"),this.selectButton=document.createElement("button"),this.selectButton.className="fi-fo-select-btn",this.selectButton.type="button",this.selectButton.setAttribute("aria-expanded","false"),this.selectedDisplay=document.createElement("div"),this.selectedDisplay.className="fi-fo-select-value-ctn",this.updateSelectedDisplay(),this.selectButton.appendChild(this.selectedDisplay),this.dropdown=document.createElement("div"),this.dropdown.className="fi-dropdown-panel fi-scrollable",this.dropdown.setAttribute("role","listbox"),this.dropdown.setAttribute("tabindex","-1"),this.dropdown.style.display="none",this.dropdownId=`fi-fo-select-dropdown-${Math.random().toString(36).substring(2,11)}`,this.dropdown.id=this.dropdownId,this.isMultiple&&this.dropdown.setAttribute("aria-multiselectable","true"),this.isSearchable&&(this.searchContainer=document.createElement("div"),this.searchContainer.className="fi-fo-select-search-ctn",this.searchInput=document.createElement("input"),this.searchInput.className="fi-input",this.searchInput.type="text",this.searchInput.placeholder=this.searchPrompt,this.searchInput.setAttribute("aria-label","Search"),this.searchContainer.appendChild(this.searchInput),this.dropdown.appendChild(this.searchContainer),this.searchInput.addEventListener("input",t=>{this.isDisabled||this.handleSearch(t)}),this.searchInput.addEventListener("keydown",t=>{if(!this.isDisabled){if(t.key==="Tab"){t.preventDefault();let e=this.getVisibleOptions();if(e.length===0)return;t.shiftKey?this.selectedIndex=e.length-1:this.selectedIndex=0,e.forEach(s=>{s.classList.remove("fi-selected")}),e[this.selectedIndex].classList.add("fi-selected"),e[this.selectedIndex].focus()}else if(t.key==="ArrowDown"){if(t.preventDefault(),t.stopPropagation(),this.getVisibleOptions().length===0)return;this.selectedIndex=-1,this.searchInput.blur(),this.focusNextOption()}else if(t.key==="ArrowUp"){t.preventDefault(),t.stopPropagation();let e=this.getVisibleOptions();if(e.length===0)return;this.selectedIndex=e.length-1,this.searchInput.blur(),e[this.selectedIndex].classList.add("fi-selected"),e[this.selectedIndex].focus(),e[this.selectedIndex].id&&this.dropdown.setAttribute("aria-activedescendant",e[this.selectedIndex].id),this.scrollOptionIntoView(e[this.selectedIndex])}}})),this.optionsList=document.createElement("ul"),this.renderOptions(),this.container.appendChild(this.selectButton),this.container.appendChild(this.dropdown),this.element.appendChild(this.container),this.applyDisabledState()}renderOptions(){this.optionsList.innerHTML="";let t=0,e=this.options,s=0,n=!1;this.options.forEach(l=>{l.options&&Array.isArray(l.options)?(s+=l.options.length,n=!0):s++}),n?this.optionsList.className="fi-fo-select-options-ctn":s>0&&(this.optionsList.className="fi-dropdown-list");let o=n?null:this.optionsList,r=0;for(let l of e){if(this.optionsLimit&&r>=this.optionsLimit)break;if(l.options&&Array.isArray(l.options)){let a=l.options;if(this.isMultiple&&Array.isArray(this.state)&&this.state.length>0&&(a=l.options.filter(c=>!this.state.includes(c.value))),a.length>0){if(this.optionsLimit){let c=this.optionsLimit-r;c<a.length&&(a=a.slice(0,c))}this.renderOptionGroup(l.label,a),r+=a.length,t+=a.length}}else{if(this.isMultiple&&Array.isArray(this.state)&&this.state.includes(l.value))continue;!o&&n&&(o=document.createElement("ul"),o.className="fi-dropdown-list",this.optionsList.appendChild(o));let a=this.createOptionElement(l.value,l);o.appendChild(a),r++,t++}}t===0?(this.searchQuery?this.showNoResultsMessage():this.isMultiple&&this.isOpen&&!this.isSearchable&&this.closeDropdown(),this.optionsList.parentNode===this.dropdown&&this.dropdown.removeChild(this.optionsList)):(this.hideLoadingState(),this.optionsList.parentNode!==this.dropdown&&this.dropdown.appendChild(this.optionsList))}renderOptionGroup(t,e){if(e.length===0)return;let s=document.createElement("li");s.className="fi-fo-select-option-group";let n=document.createElement("div");n.className="fi-dropdown-header",n.textContent=t;let o=document.createElement("ul");o.className="fi-dropdown-list",e.forEach(r=>{let l=this.createOptionElement(r.value,r);o.appendChild(l)}),s.appendChild(n),s.appendChild(o),this.optionsList.appendChild(s)}createOptionElement(t,e){let s=t,n=e,o=!1;typeof e=="object"&&e!==null&&"label"in e&&"value"in e&&(s=e.value,n=e.label,o=e.isDisabled||!1);let r=document.createElement("li");r.className="fi-dropdown-list-item fi-fo-select-option",o&&r.classList.add("fi-disabled");let l=`fi-fo-select-option-${Math.random().toString(36).substring(2,11)}`;if(r.id=l,r.setAttribute("role","option"),r.setAttribute("data-value",s),r.setAttribute("tabindex","0"),o&&r.setAttribute("aria-disabled","true"),this.isHtmlAllowed&&typeof n=="string"){let c=document.createElement("div");c.innerHTML=n;let h=c.textContent||c.innerText||n;r.setAttribute("aria-label",h)}let a=this.isMultiple?Array.isArray(this.state)&&this.state.includes(s):this.state===s;if(r.setAttribute("aria-selected",a?"true":"false"),a&&r.classList.add("fi-selected"),this.isHtmlAllowed){let c=document.createElement("span");c.innerHTML=n,r.appendChild(c)}else r.appendChild(document.createTextNode(n));return o||r.addEventListener("click",c=>{c.preventDefault(),c.stopPropagation(),this.selectOption(s),this.isMultiple&&(this.isSearchable&&this.searchInput?setTimeout(()=>{this.searchInput.focus()},0):setTimeout(()=>{r.focus()},0))}),r}async updateSelectedDisplay(){if(this.selectedDisplay.innerHTML="",this.isMultiple){if(!Array.isArray(this.state)||this.state.length===0){this.selectedDisplay.textContent=this.placeholder||"Select options";return}let e=await this.getLabelsForMultipleSelection();this.addBadgesForSelectedOptions(e),this.isOpen&&this.positionDropdown();return}if(this.state===null||this.state===""){this.selectedDisplay.textContent=this.placeholder||"Select an option";return}let t=await this.getLabelForSingleSelection();this.addSingleSelectionDisplay(t)}async getLabelsForMultipleSelection(){let t=this.getSelectedOptionLabels(),e=[];if(Array.isArray(this.state)){for(let n of this.state)if(!w(this.labelRepository[n])){if(w(t[n])){this.labelRepository[n]=t[n];continue}e.push(n)}}if(e.length>0&&w(this.initialOptionLabels)&&JSON.stringify(this.state)===JSON.stringify(this.initialState)){if(Array.isArray(this.initialOptionLabels))for(let n of this.initialOptionLabels)w(n)&&n.value!==void 0&&n.label!==void 0&&e.includes(n.value)&&(this.labelRepository[n.value]=n.label)}else if(e.length>0&&this.getOptionLabelsUsing)try{let n=await this.getOptionLabelsUsing();for(let o of n)w(o)&&o.value!==void 0&&o.label!==void 0&&(this.labelRepository[o.value]=o.label)}catch(n){console.error("Error fetching option labels:",n)}let s=[];if(Array.isArray(this.state))for(let n of this.state)w(this.labelRepository[n])?s.push(this.labelRepository[n]):w(t[n])?s.push(t[n]):s.push(n);return s}createBadgeElement(t,e){let s=document.createElement("span");s.className="fi-badge fi-size-md fi-color fi-color-primary fi-text-color-600 dark:fi-text-color-200",w(t)&&s.setAttribute("data-value",t);let n=document.createElement("span");n.className="fi-badge-label-ctn";let o=document.createElement("span");o.className="fi-badge-label",this.isHtmlAllowed?o.innerHTML=e:o.textContent=e,n.appendChild(o),s.appendChild(n);let r=this.createRemoveButton(t,e);return s.appendChild(r),s}createRemoveButton(t,e){let s=document.createElement("button");return s.type="button",s.className="fi-badge-delete-btn",s.innerHTML='<svg class="fi-icon fi-size-xs" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon"><path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z"></path></svg>',s.setAttribute("aria-label","Remove "+(this.isHtmlAllowed?e.replace(/<[^>]*>/g,""):e)),s.addEventListener("click",n=>{n.stopPropagation(),w(t)&&this.selectOption(t)}),s.addEventListener("keydown",n=>{(n.key===" "||n.key==="Enter")&&(n.preventDefault(),n.stopPropagation(),w(t)&&this.selectOption(t))}),s}addBadgesForSelectedOptions(t){let e=document.createElement("div");e.className="fi-fo-select-value-badges-ctn",t.forEach((s,n)=>{let o=Array.isArray(this.state)?this.state[n]:null,r=this.createBadgeElement(o,s);e.appendChild(r)}),this.selectedDisplay.appendChild(e)}async getLabelForSingleSelection(){let t=this.labelRepository[this.state];if(P(t)&&(t=this.getSelectedOptionLabel(this.state)),P(t)&&w(this.initialOptionLabel)&&this.state===this.initialState)t=this.initialOptionLabel,w(this.state)&&(this.labelRepository[this.state]=t);else if(P(t)&&this.getOptionLabelUsing)try{t=await this.getOptionLabelUsing(),w(t)&&w(this.state)&&(this.labelRepository[this.state]=t)}catch(e){console.error("Error fetching option label:",e),t=this.state}else P(t)&&(t=this.state);return t}addSingleSelectionDisplay(t){let e=document.createElement("span");if(e.className="fi-fo-select-value-label",this.isHtmlAllowed?e.innerHTML=t:e.textContent=t,this.selectedDisplay.appendChild(e),!this.canSelectPlaceholder)return;let s=document.createElement("button");s.type="button",s.className="fi-fo-select-value-remove-btn",s.innerHTML='<svg class="fi-icon fi-size-sm" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>',s.setAttribute("aria-label","Clear selection"),s.addEventListener("click",n=>{n.stopPropagation(),this.selectOption("")}),s.addEventListener("keydown",n=>{(n.key===" "||n.key==="Enter")&&(n.preventDefault(),n.stopPropagation(),this.selectOption(""))}),this.selectedDisplay.appendChild(s)}getSelectedOptionLabel(t){if(w(this.labelRepository[t]))return this.labelRepository[t];let e="";for(let s of this.options)if(s.options&&Array.isArray(s.options)){for(let n of s.options)if(n.value===t){e=n.label,this.labelRepository[t]=e;break}}else if(s.value===t){e=s.label,this.labelRepository[t]=e;break}return e}setUpEventListeners(){this.buttonClickListener=()=>{this.toggleDropdown()},this.documentClickListener=t=>{!this.container.contains(t.target)&&this.isOpen&&this.closeDropdown()},this.buttonKeydownListener=t=>{this.isDisabled||this.handleSelectButtonKeydown(t)},this.dropdownKeydownListener=t=>{this.isDisabled||this.isSearchable&&document.activeElement===this.searchInput&&!["Tab","Escape"].includes(t.key)||this.handleDropdownKeydown(t)},this.selectButton.addEventListener("click",this.buttonClickListener),document.addEventListener("click",this.documentClickListener),this.selectButton.addEventListener("keydown",this.buttonKeydownListener),this.dropdown.addEventListener("keydown",this.dropdownKeydownListener),!this.isMultiple&&this.livewireId&&this.statePath&&this.getOptionLabelUsing&&(this.refreshOptionLabelListener=async t=>{if(t.detail.livewireId===this.livewireId&&t.detail.statePath===this.statePath&&w(this.state))try{delete this.labelRepository[this.state];let e=await this.getOptionLabelUsing();w(e)&&(this.labelRepository[this.state]=e);let s=this.selectedDisplay.querySelector(".fi-fo-select-value-label");w(s)&&(this.isHtmlAllowed?s.innerHTML=e:s.textContent=e),this.updateOptionLabelInList(this.state,e)}catch(e){console.error("Error refreshing option label:",e)}},window.addEventListener("filament-forms::select.refreshSelectedOptionLabel",this.refreshOptionLabelListener))}updateOptionLabelInList(t,e){this.labelRepository[t]=e;let s=this.getVisibleOptions();for(let n of s)if(n.getAttribute("data-value")===String(t)){if(n.innerHTML="",this.isHtmlAllowed){let o=document.createElement("span");o.innerHTML=e,n.appendChild(o)}else n.appendChild(document.createTextNode(e));break}for(let n of this.options)if(n.options&&Array.isArray(n.options)){for(let o of n.options)if(o.value===t){o.label=e;break}}else if(n.value===t){n.label=e;break}for(let n of this.originalOptions)if(n.options&&Array.isArray(n.options)){for(let o of n.options)if(o.value===t){o.label=e;break}}else if(n.value===t){n.label=e;break}}handleSelectButtonKeydown(t){switch(t.key){case"ArrowDown":t.preventDefault(),t.stopPropagation(),this.isOpen?this.focusNextOption():this.openDropdown();break;case"ArrowUp":t.preventDefault(),t.stopPropagation(),this.isOpen?this.focusPreviousOption():this.openDropdown();break;case" ":if(t.preventDefault(),this.isOpen){if(this.selectedIndex>=0){let e=this.getVisibleOptions()[this.selectedIndex];e&&e.click()}}else this.openDropdown();break;case"Enter":break;case"Escape":this.isOpen&&(t.preventDefault(),this.closeDropdown());break;case"Tab":this.isOpen&&this.closeDropdown();break}}handleDropdownKeydown(t){switch(t.key){case"ArrowDown":t.preventDefault(),t.stopPropagation(),this.focusNextOption();break;case"ArrowUp":t.preventDefault(),t.stopPropagation(),this.focusPreviousOption();break;case" ":if(t.preventDefault(),this.selectedIndex>=0){let e=this.getVisibleOptions()[this.selectedIndex];e&&e.click()}break;case"Enter":if(t.preventDefault(),this.selectedIndex>=0){let e=this.getVisibleOptions()[this.selectedIndex];e&&e.click()}else{let e=this.element.closest("form");e&&e.submit()}break;case"Escape":t.preventDefault(),this.closeDropdown(),this.selectButton.focus();break;case"Tab":this.closeDropdown();break}}toggleDropdown(){if(!this.isDisabled){if(this.isOpen){this.closeDropdown();return}this.isMultiple&&!this.isSearchable&&!this.hasAvailableOptions()||this.openDropdown()}}hasAvailableOptions(){for(let t of this.options)if(t.options&&Array.isArray(t.options)){for(let e of t.options)if(!Array.isArray(this.state)||!this.state.includes(e.value))return!0}else if(!Array.isArray(this.state)||!this.state.includes(t.value))return!0;return!1}async openDropdown(){this.dropdown.style.display="block",this.dropdown.style.opacity="0";let t=this.selectButton.closest(".fi-modal")!==null;if(this.dropdown.style.position=t?"absolute":"fixed",this.dropdown.style.width=`${this.selectButton.offsetWidth}px`,this.selectButton.setAttribute("aria-expanded","true"),this.isOpen=!0,this.positionDropdown(),this.resizeListener||(this.resizeListener=()=>{this.dropdown.style.width=`${this.selectButton.offsetWidth}px`,this.positionDropdown()},window.addEventListener("resize",this.resizeListener)),this.scrollListener||(this.scrollListener=()=>this.positionDropdown(),window.addEventListener("scroll",this.scrollListener,!0)),this.dropdown.style.opacity="1",this.hasDynamicOptions&&this.getOptionsUsing){this.showLoadingState(!1);try{let e=await this.getOptionsUsing();this.options=e,this.originalOptions=JSON.parse(JSON.stringify(e)),this.populateLabelRepositoryFromOptions(e),this.renderOptions()}catch(e){console.error("Error fetching options:",e),this.hideLoadingState()}}if(this.hideLoadingState(),this.isSearchable&&this.searchInput)this.searchInput.value="",this.searchInput.focus(),this.searchQuery="",this.options=JSON.parse(JSON.stringify(this.originalOptions)),this.renderOptions();else{this.selectedIndex=-1;let e=this.getVisibleOptions();if(this.isMultiple){if(Array.isArray(this.state)&&this.state.length>0){for(let s=0;s<e.length;s++)if(this.state.includes(e[s].getAttribute("data-value"))){this.selectedIndex=s;break}}}else for(let s=0;s<e.length;s++)if(e[s].getAttribute("data-value")===this.state){this.selectedIndex=s;break}this.selectedIndex===-1&&e.length>0&&(this.selectedIndex=0),this.selectedIndex>=0&&(e[this.selectedIndex].classList.add("fi-selected"),e[this.selectedIndex].focus())}}positionDropdown(){let t=this.position==="top"?"top-start":"bottom-start",e=[_t(4),qt({padding:5})];this.position!=="top"&&this.position!=="bottom"&&e.push(Jt());let s=this.selectButton.closest(".fi-modal")!==null;jt(this.selectButton,this.dropdown,{placement:t,middleware:e,strategy:s?"absolute":"fixed"}).then(({x:n,y:o})=>{Object.assign(this.dropdown.style,{left:`${n}px`,top:`${o}px`})})}closeDropdown(){this.dropdown.style.display="none",this.selectButton.setAttribute("aria-expanded","false"),this.isOpen=!1,this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null),this.scrollListener&&(window.removeEventListener("scroll",this.scrollListener,!0),this.scrollListener=null),this.getVisibleOptions().forEach(e=>{e.classList.remove("fi-selected")})}focusNextOption(){let t=this.getVisibleOptions();if(t.length!==0){if(this.selectedIndex>=0&&this.selectedIndex<t.length&&t[this.selectedIndex].classList.remove("fi-selected"),this.selectedIndex===t.length-1&&this.isSearchable&&this.searchInput){this.selectedIndex=-1,this.searchInput.focus(),this.dropdown.removeAttribute("aria-activedescendant");return}this.selectedIndex=(this.selectedIndex+1)%t.length,t[this.selectedIndex].classList.add("fi-selected"),t[this.selectedIndex].focus(),t[this.selectedIndex].id&&this.dropdown.setAttribute("aria-activedescendant",t[this.selectedIndex].id),this.scrollOptionIntoView(t[this.selectedIndex])}}focusPreviousOption(){let t=this.getVisibleOptions();if(t.length!==0){if(this.selectedIndex>=0&&this.selectedIndex<t.length&&t[this.selectedIndex].classList.remove("fi-selected"),(this.selectedIndex===0||this.selectedIndex===-1)&&this.isSearchable&&this.searchInput){this.selectedIndex=-1,this.searchInput.focus(),this.dropdown.removeAttribute("aria-activedescendant");return}this.selectedIndex=(this.selectedIndex-1+t.length)%t.length,t[this.selectedIndex].classList.add("fi-selected"),t[this.selectedIndex].focus(),t[this.selectedIndex].id&&this.dropdown.setAttribute("aria-activedescendant",t[this.selectedIndex].id),this.scrollOptionIntoView(t[this.selectedIndex])}}scrollOptionIntoView(t){if(!t)return;let e=this.dropdown.getBoundingClientRect(),s=t.getBoundingClientRect();s.bottom>e.bottom?this.dropdown.scrollTop+=s.bottom-e.bottom:s.top<e.top&&(this.dropdown.scrollTop-=e.top-s.top)}getVisibleOptions(){let t=[];this.optionsList.classList.contains("fi-dropdown-list")?t=Array.from(this.optionsList.querySelectorAll(':scope > li[role="option"]')):t=Array.from(this.optionsList.querySelectorAll(':scope > ul.fi-dropdown-list > li[role="option"]'));let e=Array.from(this.optionsList.querySelectorAll('li.fi-fo-select-option-group > ul > li[role="option"]'));return[...t,...e]}getSelectedOptionLabels(){if(!Array.isArray(this.state)||this.state.length===0)return{};let t={};for(let e of this.state){let s=!1;for(let n of this.options)if(n.options&&Array.isArray(n.options)){for(let o of n.options)if(o.value===e){t[e]=o.label,s=!0;break}if(s)break}else if(n.value===e){t[e]=n.label,s=!0;break}}return t}handleSearch(t){let e=t.target.value.trim().toLowerCase();if(this.searchQuery=e,this.searchTimeout&&clearTimeout(this.searchTimeout),e===""){this.options=JSON.parse(JSON.stringify(this.originalOptions)),this.renderOptions();return}if(!this.getSearchResultsUsing||typeof this.getSearchResultsUsing!="function"||!this.hasDynamicSearchResults){this.filterOptions(e);return}this.searchTimeout=setTimeout(async()=>{try{this.showLoadingState(!0);let s=await this.getSearchResultsUsing(e);this.options=s,this.populateLabelRepositoryFromOptions(s),this.hideLoadingState(),this.renderOptions(),this.isOpen&&this.positionDropdown(),this.options.length===0&&this.showNoResultsMessage()}catch(s){console.error("Error fetching search results:",s),this.hideLoadingState(),this.options=JSON.parse(JSON.stringify(this.originalOptions)),this.renderOptions()}},this.searchDebounce)}showLoadingState(t=!1){this.optionsList.parentNode===this.dropdown&&(this.optionsList.innerHTML=""),this.hideLoadingState();let e=document.createElement("div");e.className="fi-fo-select-message",e.textContent=t?this.searchingMessage:this.loadingMessage,this.dropdown.appendChild(e)}hideLoadingState(){let t=this.dropdown.querySelector(".fi-fo-select-message");t&&t.remove()}showNoResultsMessage(){this.optionsList.parentNode===this.dropdown&&this.optionsList.children.length>0&&(this.optionsList.innerHTML=""),this.hideLoadingState();let t=document.createElement("div");t.className="fi-fo-select-message",t.textContent=this.noSearchResultsMessage,this.dropdown.appendChild(t)}filterOptions(t){let e=this.searchableOptionFields.includes("label"),s=this.searchableOptionFields.includes("value"),n=[];for(let o of this.originalOptions)if(o.options&&Array.isArray(o.options)){let r=o.options.filter(l=>e&&l.label.toLowerCase().includes(t)||s&&String(l.value).toLowerCase().includes(t));r.length>0&&n.push({label:o.label,options:r})}else(e&&o.label.toLowerCase().includes(t)||s&&String(o.value).toLowerCase().includes(t))&&n.push(o);this.options=n,this.renderOptions(),this.options.length===0&&this.showNoResultsMessage(),this.isOpen&&this.positionDropdown()}selectOption(t){if(this.isDisabled)return;if(!this.isMultiple){this.state=t,this.updateSelectedDisplay(),this.renderOptions(),this.closeDropdown(),this.selectButton.focus(),this.onStateChange(this.state);return}let e=Array.isArray(this.state)?[...this.state]:[];if(e.includes(t)){let n=this.selectedDisplay.querySelector(`[data-value="${t}"]`);if(w(n)){let o=n.parentElement;w(o)&&o.children.length===1?(e=e.filter(r=>r!==t),this.state=e,this.updateSelectedDisplay()):(n.remove(),e=e.filter(r=>r!==t),this.state=e)}else e=e.filter(o=>o!==t),this.state=e,this.updateSelectedDisplay();this.renderOptions(),this.isOpen&&this.positionDropdown(),this.maintainFocusInMultipleMode(),this.onStateChange(this.state);return}if(this.maxItems&&e.length>=this.maxItems){this.maxItemsMessage&&alert(this.maxItemsMessage);return}e.push(t),this.state=e;let s=this.selectedDisplay.querySelector(".fi-fo-select-value-badges-ctn");P(s)?this.updateSelectedDisplay():this.addSingleBadge(t,s),this.renderOptions(),this.isOpen&&this.positionDropdown(),this.maintainFocusInMultipleMode(),this.onStateChange(this.state)}async addSingleBadge(t,e){let s=this.labelRepository[t];if(P(s)&&(s=this.getSelectedOptionLabel(t),w(s)&&(this.labelRepository[t]=s)),P(s)&&this.getOptionLabelsUsing)try{let o=await this.getOptionLabelsUsing();for(let r of o)if(w(r)&&r.value===t&&r.label!==void 0){s=r.label,this.labelRepository[t]=s;break}}catch(o){console.error("Error fetching option label:",o)}P(s)&&(s=t);let n=this.createBadgeElement(t,s);e.appendChild(n)}maintainFocusInMultipleMode(){if(this.isSearchable&&this.searchInput){this.searchInput.focus();return}let t=this.getVisibleOptions();if(t.length!==0){if(this.selectedIndex=-1,Array.isArray(this.state)&&this.state.length>0){for(let e=0;e<t.length;e++)if(this.state.includes(t[e].getAttribute("data-value"))){this.selectedIndex=e;break}}this.selectedIndex===-1&&(this.selectedIndex=0),t[this.selectedIndex].classList.add("fi-selected"),t[this.selectedIndex].focus()}}disable(){this.isDisabled||(this.isDisabled=!0,this.applyDisabledState(),this.isOpen&&this.closeDropdown())}enable(){this.isDisabled&&(this.isDisabled=!1,this.applyDisabledState())}applyDisabledState(){if(this.isDisabled){if(this.selectButton.setAttribute("disabled","disabled"),this.selectButton.setAttribute("aria-disabled","true"),this.selectButton.classList.add("fi-disabled"),this.isMultiple&&this.container.querySelectorAll(".fi-fo-select-badge-remove").forEach(e=>{e.setAttribute("disabled","disabled"),e.classList.add("fi-disabled")}),!this.isMultiple&&this.canSelectPlaceholder){let t=this.container.querySelector(".fi-fo-select-value-remove-btn");t&&(t.setAttribute("disabled","disabled"),t.classList.add("fi-disabled"))}this.isSearchable&&this.searchInput&&(this.searchInput.setAttribute("disabled","disabled"),this.searchInput.classList.add("fi-disabled"))}else{if(this.selectButton.removeAttribute("disabled"),this.selectButton.removeAttribute("aria-disabled"),this.selectButton.classList.remove("fi-disabled"),this.isMultiple&&this.container.querySelectorAll(".fi-fo-select-badge-remove").forEach(e=>{e.removeAttribute("disabled"),e.classList.remove("fi-disabled")}),!this.isMultiple&&this.canSelectPlaceholder){let t=this.container.querySelector(".fi-fo-select-value-remove-btn");t&&(t.removeAttribute("disabled"),t.classList.add("fi-disabled"))}this.isSearchable&&this.searchInput&&(this.searchInput.removeAttribute("disabled"),this.searchInput.classList.remove("fi-disabled"))}}destroy(){this.selectButton&&this.buttonClickListener&&this.selectButton.removeEventListener("click",this.buttonClickListener),this.documentClickListener&&document.removeEventListener("click",this.documentClickListener),this.selectButton&&this.buttonKeydownListener&&this.selectButton.removeEventListener("keydown",this.buttonKeydownListener),this.dropdown&&this.dropdownKeydownListener&&this.dropdown.removeEventListener("keydown",this.dropdownKeydownListener),this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null),this.scrollListener&&(window.removeEventListener("scroll",this.scrollListener,!0),this.scrollListener=null),this.refreshOptionLabelListener&&window.removeEventListener("filament-forms::select.refreshSelectedOptionLabel",this.refreshOptionLabelListener),this.isOpen&&this.closeDropdown(),this.searchTimeout&&(clearTimeout(this.searchTimeout),this.searchTimeout=null),this.container&&this.container.remove()}};export{De as default};
