# Testing Guidelines

This directory contains comprehensive testing guidelines and resources for the project.

## Overview

The testing guidelines are organized to provide clear, actionable guidance for developers at all levels, with special attention to clarity for junior developers.

## Testing Documentation Structure

1. [Comprehensive Testing Guide](010-comprehensive-testing-guide.md) - Complete overview of testing practices
2. [Test Categories](020-test-categories.md) - Different types of tests and when to use them
3. [Test Coverage](030-test-coverage.md) - Coverage requirements and measurement
4. [Test Data Requirements](040-test-data-requirements.md) - Guidelines for test data management
5. [Test Examples](050-test-examples.md) - Practical examples and patterns
6. [Test Helpers & Utilities](060-test-helpers-utilities.md) - Reusable testing components
7. [Test Linting Rules](070-test-linting-rules.md) - Code quality standards for tests
8. [Testing Standards](080-testing-standards.md) - Additional testing standards and practices

## Templates

The `templates/` directory contains standardized templates for creating tests:

- [Templates Index](templates/000-index.md) - Complete overview of available templates and usage guidelines
- [Feature Test Template](templates/020-feature-test-template.php) - Template for feature tests
- [Integration Test Template](templates/030-integration-test-template.php) - Template for integration tests
- [Unit Test Template](templates/010-unit-test-template.php) - Template for unit tests
- [PHP Attributes Standards](templates/040-php-attributes-standards.md) - Guidelines for PHP attributes in tests

## Purpose

These testing guidelines ensure:
- Consistent test quality across the project
- Clear testing standards for all team members
- Comprehensive coverage of testing scenarios
- Maintainable and reliable test suites

## Maintenance

Keep these guidelines updated when:
- Testing frameworks or tools change
- New testing patterns are adopted
- Project testing requirements evolve
- Best practices are updated

---

## Navigation

**← Previous:** [Testing Standards](../050-testing-standards.md) | **Next →** [Security Standards](../090-security-standards.md)
