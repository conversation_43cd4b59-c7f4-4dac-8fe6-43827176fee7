<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Laravel Application - Diagram Index</title>
    <!-- Chart.js for statistics visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- D3.js for relationship visualization -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        /* Modal/Lightbox styles */
        .modal {
            padding: 0;
            border: none;
            background-color: rgba(0, 0, 0, 0.8);
            max-width: 100%;
            max-height: 100%;
            width: 100%;
            height: 100%;
            transition: opacity 0.3s ease;
        }

        .modal::backdrop {
            background-color: rgba(0, 0, 0, 0.8);
        }

        .modal-content {
            position: relative;
            margin: auto;
            padding: 0;
            width: 90%;
            max-width: 1200px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .modal-image-container {
            position: relative;
            width: 100%;
            height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: auto;
        }

        .modal-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            background-color: var(--body-bg);
            padding: 20px;
            border-radius: 5px;
        }

        .modal-caption {
            margin: 15px 0;
            color: var(--body-text);
            background-color: var(--body-bg);
            padding: 10px 20px;
            border-radius: 5px;
            max-width: 80%;
            text-align: center;
            font-size: 1.5em;
            font-weight: bold;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            color: var(--body-text);
            font-size: 30px;
            font-weight: bold;
            cursor: pointer;
            background-color: var(--body-bg);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-prev,
        .modal-next {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: var(--body-text);
            font-size: 30px;
            font-weight: bold;
            cursor: pointer;
            background-color: var(--body-bg);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-prev {
            left: 15px;
        }

        .modal-next {
            right: 15px;
        }

        .thumbnail-container {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }

        .thumbnail-container img {
            transition: transform 0.3s ease;
            border: 2px solid transparent;
        }

        .thumbnail-container:hover img {
            transform: scale(1.05);
            border-color: var(--link-color);
        }

        .thumbnail-container::after {
            content: '&#128269;';
            position: absolute;
            bottom: 5px;
            right: 5px;
            background-color: var(--body-bg);
            color: var(--body-text);
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .thumbnail-container:hover::after {
            opacity: 1;
        }

        /* Clickable tags styles */
        .clickable-tag {
            display: inline-block;
            background-color: var(--search-bg);
            color: var(--search-text);
            border: 1px solid var(--search-border);
            border-radius: 4px;
            padding: 2px 6px;
            margin: 2px;
            font-size: 0.85em;
            cursor: pointer;
            transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
        }

        .clickable-tag:hover {
            background-color: var(--search-btn-primary);
            color: var(--search-btn-primary-text);
            border-color: var(--search-btn-primary);
        }

        .clickable-tag.active {
            background-color: var(--search-btn-primary);
            color: var(--search-btn-primary-text);
            border-color: var(--search-btn-primary);
        }

        /* Statistics dashboard styles */
        .stats-dashboard {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            flex: 1;
            min-width: 300px;
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stats-card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
            border-bottom: 1px solid var(--card-border);
            padding-bottom: 10px;
        }

        .chart-container {
            position: relative;
            height: 250px;
            width: 100%;
        }

        /* Diagram relationship visualization styles */
        #relationship-visualization {
            width: 100%;
            height: 400px;
            border: 1px solid var(--card-border);
            border-radius: 5px;
            background-color: var(--card-bg);
            margin-bottom: 20px;
        }

        .node {
            fill: var(--search-btn-primary);
            stroke: var(--search-border);
            stroke-width: 1.5px;
        }

        .link {
            stroke: var(--search-border);
            stroke-opacity: 0.6;
        }

        .node-label {
            font-size: 12px;
            fill: var(--body-text);
            text-anchor: middle;
            pointer-events: none;
        }

        /* Keyboard shortcuts guide styles */
        .floating-btn {
            position: fixed;
            background-color: var(--search-btn-primary);
            color: var(--search-btn-primary-text);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 100;
            transition: background-color 0.3s ease;
        }

        .floating-btn:hover {
            background-color: var(--link-hover);
        }

        /* Custom tooltips */
        .tooltip {
            position: fixed;
            background-color: var(--tooltip-bg, rgba(0, 0, 0, 0.8));
            color: var(--tooltip-text, white);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1001;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            bottom: 60px;
            left: 50%;
            transform: translateX(-50%);
        }

        .tooltip::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid var(--tooltip-bg, rgba(0, 0, 0, 0.8));
        }

        .floating-btn:hover .tooltip {
            opacity: 1;
        }

        #keyboard-shortcuts-btn {
            bottom: 20px;
            right: 20px;
        }

        #print-view-btn {
            bottom: 20px;
            right: 80px;
        }

        /* Diagram comparison tool styles */
        #comparison-container {
            padding: 20px;
            border: none;
            background-color: var(--body-bg);
            max-width: 100%;
            max-height: 100%;
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        #comparison-container::backdrop {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .comparison-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--card-border);
            padding-bottom: 10px;
        }

        .comparison-close {
            font-size: 24px;
            cursor: pointer;
            color: var(--body-text);
        }

        .comparison-close:hover {
            color: var(--link-hover);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .comparison-item {
            border: 1px solid var(--card-border);
            border-radius: 5px;
            padding: 15px;
            background-color: var(--card-bg);
        }

        .comparison-item h3 {
            margin-top: 0;
            border-bottom: 1px solid var(--card-border);
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .comparison-image-container {
            width: 100%;
            height: 300px;
            overflow: auto;
            margin-bottom: 15px;
            background-color: var(--body-bg);
            border: 1px solid var(--card-border);
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .comparison-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .comparison-details {
            margin-top: 15px;
            font-size: 0.9em;
        }

        .comparison-details p {
            margin: 5px 0;
        }

        .comparison-actions {
            margin-top: 15px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .comparison-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .comparison-primary {
            background-color: var(--search-btn-primary);
            color: var(--search-btn-primary-text);
        }

        .comparison-secondary {
            background-color: var(--search-btn-secondary);
            color: var(--search-btn-secondary-text);
        }

        .comparison-select {
            padding: 5px;
            border: 1px solid var(--card-border);
            border-radius: 3px;
            background-color: var(--body-bg);
            color: var(--body-text);
            width: 100%;
            margin-bottom: 10px;
        }

        /* Feedback form styles */
        #feedback-container {
            padding: 0;
            border: none;
            background-color: transparent;
            max-width: 100%;
            max-height: 100%;
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        #feedback-container::backdrop {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .feedback-content {
            background-color: var(--body-bg);
            color: var(--body-text);
            margin: 10% auto;
            padding: 20px;
            border: 1px solid var(--card-border);
            border-radius: 5px;
            width: 80%;
            max-width: 500px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .feedback-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--card-border);
            padding-bottom: 10px;
        }

        .feedback-close {
            font-size: 24px;
            cursor: pointer;
            color: var(--body-text);
        }

        .feedback-close:hover {
            color: var(--link-hover);
        }

        .feedback-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .feedback-form label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }

        .feedback-form input,
        .feedback-form select,
        .feedback-form textarea {
            padding: 8px;
            border: 1px solid var(--card-border);
            border-radius: 3px;
            background-color: var(--body-bg);
            color: var(--body-text);
            width: 100%;
        }

        .feedback-form textarea {
            min-height: 100px;
            resize: vertical;
        }

        .feedback-form button {
            padding: 10px;
            background-color: var(--search-btn-primary);
            color: var(--search-btn-primary-text);
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            align-self: flex-end;
        }

        .feedback-form button:hover {
            background-color: var(--link-hover);
        }

        .feedback-success {
            display: none;
            padding: 15px;
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 3px;
            margin-bottom: 15px;
        }

        #feedback-btn {
            right: 310px;
            bottom: 20px;
        }

        #compare-btn {
            right: 250px;
            bottom: 20px;
        }

        #print-view-btn {
            right: 190px;
            bottom: 20px;
        }

        #theme-toggle-btn {
            right: 130px;
            bottom: 20px;
        }

        #high-contrast-btn {
            right: 70px;
            bottom: 20px;
        }

        #keyboard-shortcuts-btn {
            right: 10px;
            bottom: 20px;
        }

        /* Mobile-specific styles */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            h1 {
                font-size: 1.8em;
            }

            h2 {
                font-size: 1.5em;
            }

            h3 {
                font-size: 1.3em;
            }

            #diagram-search-container {
                flex-direction: column;
            }

            #diagram-search-container > div {
                flex-direction: column;
                align-items: flex-start;
            }

            #diagram-search {
                width: 100%;
                margin-bottom: 10px;
            }

            .search-input-container {
                flex-direction: column;
                width: 100%;
            }

            .search-buttons {
                display: flex;
                gap: 10px;
                width: 100%;
            }

            .search-buttons button {
                flex: 1;
            }

            .filter-options {
                flex-wrap: wrap;
                justify-content: flex-start;
            }

            .filter-option {
                margin-right: 10px;
                margin-bottom: 5px;
            }

            table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .stats-dashboard {
                flex-direction: column;
            }

            .stats-card {
                min-width: 100%;
                margin-bottom: 15px;
            }

            .comparison-grid {
                grid-template-columns: 1fr;
            }

            .floating-btn {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }

            #feedback-btn {
                right: 260px;
            }

            #compare-btn {
                right: 210px;
            }

            #print-view-btn {
                right: 160px;
            }

            #theme-toggle-btn {
                right: 110px;
            }

            #high-contrast-btn {
                right: 60px;
            }

            #keyboard-shortcuts-btn {
                right: 10px;
            }

            /* For very small screens, stack buttons vertically */
            @media (max-width: 480px) {
                .floating-btn {
                    width: 35px;
                    height: 35px;
                    font-size: 16px;
                }

                #feedback-btn {
                    right: 10px;
                    bottom: 310px;
                }

                #compare-btn {
                    right: 10px;
                    bottom: 255px;
                }

                #print-view-btn {
                    right: 10px;
                    bottom: 200px;
                }

                #theme-toggle-btn {
                    right: 10px;
                    bottom: 145px;
                }

                #high-contrast-btn {
                    right: 10px;
                    bottom: 90px;
                }

                #keyboard-shortcuts-btn {
                    right: 10px;
                    bottom: 35px;
                }
            }

            .feedback-content,
            .keyboard-shortcuts-content,
            .modal-content {
                width: 95%;
                margin: 5% auto;
            }

            .recently-updated-diagrams {
                flex-direction: column;
            }

            .recently-updated-item {
                max-width: 100%;
                margin-bottom: 15px;
            }
        }

        /* Print-specific styles */
        @media print {
            .floating-btn,
            #keyboard-shortcuts-modal,
            #diagram-search-container,
            .modal {
                display: none !important;
            }

            details {
                display: block !important;
            }

            details > summary {
                display: none;
            }

            body {
                padding: 0;
                margin: 0;
            }

            .container {
                max-width: 100%;
                padding: 0;
                margin: 0;
            }

            a {
                text-decoration: none;
                color: var(--body-text);
            }

            a::after {
                content: ' (' attr(href) ')';
                font-size: 0.8em;
                font-style: italic;
            }

            table {
                page-break-inside: avoid;
            }

            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid;
            }

            img {
                max-width: 100% !important;
            }
        }

        #keyboard-shortcuts-modal {
            padding: 0;
            border: none;
            background-color: transparent;
            max-width: 100%;
            max-height: 100%;
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        #keyboard-shortcuts-modal::backdrop {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .keyboard-shortcuts-content {
            background-color: var(--body-bg);
            color: var(--body-text);
            margin: 10% auto;
            padding: 20px;
            border: 1px solid var(--card-border);
            border-radius: 5px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .keyboard-shortcuts-content h2 {
            margin-top: 0;
            border-bottom: 1px solid var(--card-border);
            padding-bottom: 10px;
        }

        .keyboard-shortcuts-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .keyboard-shortcuts-content th,
        .keyboard-shortcuts-content td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid var(--card-border);
        }

        .keyboard-shortcuts-content th {
            background-color: var(--card-highlight);
        }

        .keyboard-shortcuts-content kbd {
            background-color: var(--code-bg);
            border: 1px solid var(--card-border);
            border-radius: 3px;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
            color: var(--code-text);
            display: inline-block;
            font-size: 0.85em;
            font-family: monospace;
            line-height: 1;
            padding: 2px 4px;
            margin: 0 2px;
        }

        .keyboard-shortcuts-close {
            color: var(--body-text);
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .keyboard-shortcuts-close:hover {
            color: var(--link-hover);
        }
        /* Base styles for both light and dark modes */
        :root {
            /* Base theme colors */
            --search-bg: #f8f9fa;
            --search-border: #ddd;
            --search-text: #333;
            --search-input-bg: #fff;
            --search-input-border: #ccc;
            --search-btn-primary: #0077cc;
            --search-btn-primary-text: #fff;
            --search-btn-secondary: #6c757d;
            --search-btn-secondary-text: #fff;
            --search-results-border: #eee;

            /* Additional UI elements */
            --tip-bg: #ffffcc;
            --tip-border: #e6e600;
            --tip-text: #333;
            --tooltip-bg: rgba(0, 0, 0, 0.8);
            --tooltip-text: white;

            /* Diagram colors - light mode */
            --diagram-flowchart: #0077cc;
            --diagram-erd: #087f5b;
            --diagram-sequence: #e67700;
            --diagram-class: #9c36b5;
            --diagram-state: #c92a2a;
            --diagram-gantt: #5f3dc4;
            --diagram-line: #0077cc;
            --diagram-text: #333;
            --diagram-highlight: #ffffcc;

            /* Card and container backgrounds */
            --card-bg: #f8f9fa;
            --card-border: #ddd;
            --card-text: #333;
            --card-highlight: #e9ecef;

            /* Main document colors */
            --body-bg: #ffffff;
            --body-text: #333333;
            --heading-color: #222222;
            --link-color: #0077cc;
            --link-hover: #005fa3;
            --table-header-bg: #f2f2f2;
            --table-border: #ddd;
            --table-row-alt: #f9f9f9;
            --code-bg: #f5f5f5;
            --code-text: #333;
            --details-bg: #f8f9fa;
            --details-border: #ddd;
        }

        [data-theme="dark"] {
            /* Base theme colors - dark mode */
            --search-bg: #2d2d2d;
            --search-border: #444;
            --search-text: #e0e0e0;
            --search-input-bg: #3d3d3d;
            --search-input-border: #555;
            --search-btn-primary: #4dabf7;
            --search-btn-primary-text: #000;
            --search-btn-secondary: #909090;
            --search-btn-secondary-text: #000;
            --search-results-border: #444;

            /* Additional UI elements - dark mode */
            --tip-bg: #3d3d1a;
            --tip-border: #666600;
            --tip-text: #e0e0e0;
            --tooltip-bg: rgba(60, 60, 60, 0.9);
            --tooltip-text: #e0e0e0;

            /* Diagram colors - dark mode */
            --diagram-flowchart: #4dabf7;
            --diagram-erd: #63e6be;
            --diagram-sequence: #ffc078;
            --diagram-class: #da77f2;
            --diagram-state: #ff8787;
            --diagram-gantt: #b197fc;
            --diagram-line: #4dabf7;
            --diagram-text: #e0e0e0;
            --diagram-highlight: #3d3d1a;

            /* Card and container backgrounds - dark mode */
            --card-bg: #2d2d2d;
            --card-border: #444;
            --card-text: #e0e0e0;
            --card-highlight: #3d3d3d;

            /* Main document colors - dark mode */
            --body-bg: #1a1a1a;
            --body-text: #e0e0e0;
            --heading-color: #ffffff;
            --link-color: #4dabf7;
            --link-hover: #78c6ff;
            --table-header-bg: #2d2d2d;
            --table-border: #444;
            --table-row-alt: #2a2a2a;
            --code-bg: #2d2d2d;
            --code-text: #e0e0e0;
            --details-bg: #2d2d2d;
            --details-border: #444;
        }

        /* High contrast light mode */
        [data-theme="high-contrast-light"] {
            /* Base theme colors - high contrast light */
            --search-bg: #ffffff;
            --search-border: #000000;
            --search-text: #000000;
            --search-input-bg: #ffffff;
            --search-input-border: #000000;
            --search-btn-primary: #000000;
            --search-btn-primary-text: #ffffff;
            --search-btn-secondary: #333333;
            --search-btn-secondary-text: #ffffff;
            --search-results-border: #000000;

            /* Additional UI elements - high contrast light */
            --tip-bg: #ffffcc;
            --tip-border: #000000;
            --tip-text: #000000;

            /* Diagram colors - high contrast light */
            --diagram-flowchart: #000000;
            --diagram-erd: #000000;
            --diagram-sequence: #000000;
            --diagram-class: #000000;
            --diagram-state: #000000;
            --diagram-gantt: #000000;
            --diagram-line: #000000;
            --diagram-text: #000000;
            --diagram-highlight: #ffffcc;

            /* Card and container backgrounds - high contrast light */
            --card-bg: #ffffff;
            --card-border: #000000;
            --card-text: #000000;
            --card-highlight: #f0f0f0;

            /* Main document colors - high contrast light */
            --body-bg: #ffffff;
            --body-text: #000000;
            --heading-color: #000000;
            --link-color: #0000EE;
            --link-hover: #551A8B;
            --table-header-bg: #ffffff;
            --table-border: #000000;
            --table-row-alt: #f0f0f0;
            --code-bg: #ffffff;
            --code-text: #000000;
            --details-bg: #ffffff;
            --details-border: #000000;
        }

        /* High contrast dark mode */
        [data-theme="high-contrast-dark"] {
            /* Base theme colors - high contrast dark */
            --search-bg: #000000;
            --search-border: #ffffff;
            --search-text: #ffffff;
            --search-input-bg: #000000;
            --search-input-border: #ffffff;
            --search-btn-primary: #ffffff;
            --search-btn-primary-text: #000000;
            --search-btn-secondary: #cccccc;
            --search-btn-secondary-text: #000000;
            --search-results-border: #ffffff;

            /* Additional UI elements - high contrast dark */
            --tip-bg: #000000;
            --tip-border: #ffff00;
            --tip-text: #ffff00;

            /* Diagram colors - high contrast dark */
            --diagram-flowchart: #ffffff;
            --diagram-erd: #ffffff;
            --diagram-sequence: #ffffff;
            --diagram-class: #ffffff;
            --diagram-state: #ffffff;
            --diagram-gantt: #ffffff;
            --diagram-line: #ffffff;
            --diagram-text: #ffffff;
            --diagram-highlight: #333333;

            /* Card and container backgrounds - high contrast dark */
            --card-bg: #000000;
            --card-border: #ffffff;
            --card-text: #ffffff;
            --card-highlight: #333333;

            /* Main document colors - high contrast dark */
            --body-bg: #000000;
            --body-text: #ffffff;
            --heading-color: #ffffff;
            --link-color: #ffff00;
            --link-hover: #ffcc00;
            --table-header-bg: #000000;
            --table-border: #ffffff;
            --table-row-alt: #333333;
            --code-bg: #000000;
            --code-text: #ffffff;
            --details-bg: #000000;
            --details-border: #ffffff;
        }

        /* Global styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--body-bg);
            color: var(--body-text);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        h1, h2, h3, h4, h5, h6 {
            color: var(--heading-color);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        h1 {
            font-size: 2.2em;
            border-bottom: 2px solid var(--table-border);
            padding-bottom: 0.3em;
        }

        h2 {
            font-size: 1.8em;
            border-bottom: 1px solid var(--table-border);
            padding-bottom: 0.2em;
        }

        h3 {
            font-size: 1.5em;
        }

        a {
            color: var(--link-color);
            text-decoration: none;
        }

        a:hover {
            color: var(--link-hover);
            text-decoration: underline;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.9em;
        }

        th, td {
            padding: 12px 15px;
            border: 1px solid var(--table-border);
        }

        th {
            background-color: var(--table-header-bg);
            font-weight: bold;
            text-align: left;
        }

        tr:nth-child(even) {
            background-color: var(--table-row-alt);
        }

        code {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            background-color: var(--code-bg);
            color: var(--code-text);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }

        blockquote {
            border-left: 4px solid var(--search-btn-primary);
            margin: 1.5em 0;
            padding: 0.5em 1em;
            background-color: var(--card-bg);
            border-radius: 0 4px 4px 0;
        }

        details {
            background-color: var(--details-bg);
            border: 1px solid var(--details-border);
            border-radius: 4px;
            padding: 0.5em 1em;
            margin: 1em 0;
        }

        summary {
            font-weight: bold;
            cursor: pointer;
            padding: 0.5em 0;
        }

        /* Search container styles */
        #diagram-search-container {
            margin-bottom: 20px;
            padding: 15px;
            background-color: var(--search-bg);
            border-radius: 5px;
            border: 1px solid var(--search-border);
            color: var(--search-text);
        }

        #diagram-search-container input[type="text"] {
            flex-grow: 1;
            padding: 8px;
            border: 1px solid var(--search-input-border);
            border-radius: 4px;
            background-color: var(--search-input-bg);
            color: var(--search-text);
        }

        #search-button {
            padding: 8px 16px;
            background-color: var(--search-btn-primary);
            color: var(--search-btn-primary-text);
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        #reset-button {
            padding: 8px 16px;
            background-color: var(--search-btn-secondary);
            color: var(--search-btn-secondary-text);
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        #theme-toggle {
            padding: 8px 16px;
            background-color: var(--search-btn-secondary);
            color: var(--search-btn-secondary-text);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: auto;
        }

        #search-results table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        #search-results th, #search-results td {
            padding: 8px;
            border: 1px solid var(--search-results-border);
        }

        /* Container for the 010-ddl content */
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            table {
                display: block;
                overflow-x: auto;
            }

            th, td {
                padding: 8px 10px;
            }
        }

        /* Print styles */
        @media print {
            body {
                padding: 0;
                background-color: white;
                color: black;
            }

            #diagram-search-container,
            #theme-toggle,
            details summary {
                display: none;
            }

            details {
                display: block;
            }

            a {
                color: black;
                text-decoration: underline;
            }

            table {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- Modal/Lightbox for diagram previews -->
    <dialog id="diagramModal" class="modal" aria-labelledby="modalCaption">
        <div class="modal-content">
            <span class="modal-close" aria-label="Close diagram preview">&times;</span>
            <div class="modal-image-container">
                <img class="modal-image" id="modalImage" src="" alt="Diagram preview">
            </div>
            <h2 class="modal-caption" id="modalCaption">Diagram Preview</h2>
            <span class="modal-prev" aria-label="Previous diagram">&#10094;</span>
            <span class="modal-next" aria-label="Next diagram">&#10095;</span>
        </div>
    </dialog>

    <!-- Floating action buttons -->
    <button id="theme-toggle-btn" class="floating-btn" aria-label="Toggle Dark/Light Mode">
        <span class="tooltip">Toggle Dark/Light Mode</span>
        &#127763;
    </button>
    <button id="high-contrast-btn" class="floating-btn" aria-label="Toggle High Contrast Mode">
        <span class="tooltip">Toggle High Contrast Mode</span>
        &#128065;&#65039;
    </button>
    <button id="feedback-btn" class="floating-btn" aria-label="Provide Feedback">
        <span class="tooltip">Provide Feedback</span>
        &#128172;
    </button>
    <button id="compare-btn" class="floating-btn" aria-label="Compare Diagrams">
        <span class="tooltip">Compare Diagrams</span>
        &#128269;
    </button>
    <button id="print-view-btn" class="floating-btn" aria-label="Print View">
        <span class="tooltip">Print View</span>
        &#128433;&#65039;
    </button>
    <button id="keyboard-shortcuts-btn" class="floating-btn" aria-label="Keyboard Shortcuts">
        <span class="tooltip">Keyboard Shortcuts</span>
        &#9000;&#65039;
    </button>

    <!-- Feedback form -->
    <dialog id="feedback-container" aria-labelledby="feedback-title">
        <div class="feedback-content">
            <div class="feedback-header">
                <h2 id="feedback-title">Provide Feedback</h2>
                <span class="feedback-close" aria-label="Close feedback form">&times;</span>
            </div>
            <div class="feedback-success" role="alert" aria-live="assertive">
                Thank you for your feedback! We appreciate your input and will use it to improve our diagrams.
            </div>
            <form class="feedback-form" id="feedback-form" aria-labelledby="feedback-title">
                <div>
                    <label for="feedback-diagram">Select Diagram:</label>
                    <select id="feedback-diagram" required>
                        <option value="">Select a diagram...</option>
                    </select>
                </div>
                <div>
                    <label for="feedback-type">Feedback Type:</label>
                    <select id="feedback-type" required>
                        <option value="">Select a feedback type...</option>
                        <option value="error">Error or Inaccuracy</option>
                        <option value="improvement">Suggestion for Improvement</option>
                        <option value="clarity">Clarity Issue</option>
                        <option value="missing">Missing Information</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div>
                    <label for="feedback-message">Your Feedback:</label>
                    <textarea id="feedback-message" placeholder="Please provide details about your feedback..." required></textarea>
                </div>
                <div>
                    <label for="feedback-email">Your Email (optional):</label>
                    <input type="email" id="feedback-email" placeholder="<EMAIL>">
                </div>
                <button type="submit">Submit Feedback</button>
            </form>
        </div>
    </dialog>

    <!-- Diagram comparison tool -->
    <dialog id="comparison-container" aria-labelledby="comparison-title">
        <div class="comparison-header">
            <h2 id="comparison-title">Compare Diagrams</h2>
            <span class="comparison-close" aria-label="Close comparison tool">&times;</span>
        </div>
        <div class="comparison-grid">
            <div class="comparison-item">
                <h3>Diagram 1</h3>
                <label for="diagram1-select">Select Diagram 1:</label>
                <select id="diagram1-select" class="comparison-select">
                    <option value="">Select a diagram...</option>
                </select>
                <div class="comparison-image-container">
                    <img id="diagram1-image" class="comparison-image" src="" alt="Diagram 1">
                </div>
                <div class="comparison-details" id="diagram1-details">
                    <p><strong>Type:</strong> <span id="diagram1-type"></span></p>
                    <p><strong>Description:</strong> <span id="diagram1-desc"></span></p>
                </div>
            </div>
            <div class="comparison-item">
                <h3>Diagram 2</h3>
                <label for="diagram2-select">Select Diagram 2:</label>
                <select id="diagram2-select" class="comparison-select">
                    <option value="">Select a diagram...</option>
                </select>
                <div class="comparison-image-container">
                    <img id="diagram2-image" class="comparison-image" src="" alt="Diagram 2">
                </div>
                <div class="comparison-details" id="diagram2-details">
                    <p><strong>Type:</strong> <span id="diagram2-type"></span></p>
                    <p><strong>Description:</strong> <span id="diagram2-desc"></span></p>
                </div>
            </div>
        </div>
    </dialog>
    <dialog id="keyboard-shortcuts-modal" aria-labelledby="shortcuts-title">
        <div class="keyboard-shortcuts-content">
            <span class="keyboard-shortcuts-close" aria-label="Close keyboard shortcuts">&times;</span>
            <h2 id="shortcuts-title">Keyboard Shortcuts</h2>
            <table>
                <thead>
                    <tr>
                        <th scope="col">Action</th>
                        <th scope="col">Shortcut</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Focus search box</td>
                        <td><kbd>/</kbd></td>
                    </tr>
                    <tr>
                        <td>Search</td>
                        <td><kbd>Enter</kbd></td>
                    </tr>
                    <tr>
                        <td>Reset search</td>
                        <td>
<kbd>Esc</kbd> (when search is focused)</td>
                    </tr>
                    <tr>
                        <td>Toggle theme</td>
                        <td><kbd>T</kbd></td>
                    </tr>
                    <tr>
                        <td>Toggle high contrast mode</td>
                        <td><kbd>H</kbd></td>
                    </tr>
                    <tr>
                        <td>Show keyboard shortcuts</td>
                        <td><kbd>?</kbd></td>
                    </tr>
                    <tr>
                        <td>Open diagram comparison tool</td>
                        <td><kbd>C</kbd></td>
                    </tr>
                    <tr>
                        <td>Open feedback form</td>
                        <td><kbd>F</kbd></td>
                    </tr>
                    <tr>
                        <td>Navigate to top</td>
                        <td><kbd>Home</kbd></td>
                    </tr>
                    <tr>
                        <td>Navigate to bottom</td>
                        <td><kbd>End</kbd></td>
                    </tr>
                    <tr>
                        <td>Print view</td>
                        <td>
<kbd>Ctrl</kbd> + <kbd>P</kbd> or <kbd>&#8984;</kbd> + <kbd>P</kbd>
</td>
                    </tr>
                    <tr>
                        <td colspan="2"><strong>When viewing diagram preview:</strong></td>
                    </tr>
                    <tr>
                        <td>Close preview</td>
                        <td><kbd>Esc</kbd></td>
                    </tr>
                    <tr>
                        <td>Previous diagram</td>
                        <td>
<kbd>&larr;</kbd> or <kbd>P</kbd>
</td>
                    </tr>
                    <tr>
                        <td>Next diagram</td>
                        <td>
<kbd>&rarr;</kbd> or <kbd>N</kbd>
</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </dialog>

    <div class="container">
        <h1>Enhanced Laravel Application - Diagram Index</h1>

        <p><strong>Last Updated</strong>: 2025-05-16</p>

        <p>This index provides a comprehensive list of all diagrams and illustrations used in the Enhanced Laravel Application documentation. Use this document to quickly locate and access diagrams across the project documentation.</p>

        <!-- Recently Updated Section -->
        <div style="margin: 20px 0; padding: 15px; background-color: var(--card-bg); border: 1px solid var(--card-border); border-radius: 5px;">
            <h3 style="margin-top: 0;">Recently Updated Diagrams</h3>
            <p>These diagrams have been recently added or updated:</p>
            <div style="display: flex; flex-wrap: wrap; gap: 15px;" class="recently-updated-diagrams">
                <div style="flex: 1; min-width: 200px; max-width: 250px; border: 1px solid var(--card-border); border-radius: 5px; padding: 10px; background-color: var(--body-bg);" class="recently-updated-item">
                    <div class="thumbnail-container" data-diagram-name="Authentication Flow" data-diagram-type="Sequence" data-diagram-desc="User authentication process" data-diagram-dark="mermaid/dark/authentication-flow-dark.md" data-diagram-light="mermaid/light/authentication-flow-light.md">
                        <img src="thumbnails/plantuml/dark/authentication-flow-dark-thumb.svg" alt="Authentication Flow thumbnail" width="80" />
                    </div>
                    <h4 style="margin: 10px 0 5px 0;">Authentication Flow</h4>
                    <p style="margin: 0; font-size: 0.9em;">Updated: 2025-05-15</p>
                </div>
                <div style="flex: 1; min-width: 200px; max-width: 250px; border: 1px solid var(--card-border); border-radius: 5px; padding: 10px; background-color: var(--body-bg);" class="recently-updated-item">
                    <div class="thumbnail-container" data-diagram-name="ERD Overview" data-diagram-type="ERD" data-diagram-desc="Entity Relationship Diagram" data-diagram-dark="mermaid/dark/erd-overview-dark.md" data-diagram-light="mermaid/light/erd-overview-light.md">
                        <img src="thumbnails/mermaid/light/erd-overview-light-thumb.svg" alt="ERD Overview thumbnail" width="100%" />
                    </div>
                    <h4 style="margin: 10px 0 5px 0;">ERD Overview</h4>
                    <p style="margin: 0; font-size: 0.9em;">Updated: 2025-05-14</p>
                </div>
                <div style="flex: 1; min-width: 200px; max-width: 250px; border: 1px solid var(--card-border); border-radius: 5px; padding: 10px; background-color: var(--body-bg);" class="recently-updated-item">
                    <div class="thumbnail-container" data-diagram-name="Project Roadmap" data-diagram-type="Gantt" data-diagram-desc="Project roadmap timeline" data-diagram-dark="mermaid/dark/project-roadmap-dark.md" data-diagram-light="mermaid/light/project-roadmap-light.md">
                        <img src="thumbnails/plantuml/dark/project-roadmap-dark-thumb.svg" alt="Project Roadmap thumbnail" width="80" />
                    </div>
                    <h4 style="margin: 10px 0 5px 0;">Project Roadmap</h4>
                    <p style="margin: 0; font-size: 0.9em;">Updated: 2025-05-10</p>
                </div>
            </div>
        </div>

        <!-- Search Container -->
        <div id="diagram-search-container" role="search" aria-labelledby="search-heading">
            <div style="margin-bottom: 10px; padding: 8px; background-color: var(--tip-bg); border-radius: 4px; border: 1px solid var(--tip-border); color: var(--tip-text);" role="note">
                <strong>Search Tip:</strong> Type at least 2 characters to search automatically, or click the Search button. Use the checkboxes to filter by diagram type.
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <h3 id="search-heading" style="margin-top: 0; margin-bottom: 0;">Diagram Search</h3>
                <!-- Theme toggles moved to floating action buttons -->
            </div>
            <div style="display: flex; gap: 10px; margin-bottom: 10px;" class="search-input-container">
                <input type="text" id="diagram-search" placeholder="Search diagrams by name, type, or tags..." aria-label="Search diagrams">
                <div class="search-buttons">
                    <button id="search-button" aria-label="Search">Search</button>
                    <button id="reset-button" aria-label="Reset search">Reset</button>
                </div>
            </div>
            <div class="filter-options" style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 10px;">
                <label class="filter-option" style="margin-right: 15px;"><input type="checkbox" id="filter-flowchart"> Flowcharts</label>
                <label class="filter-option" style="margin-right: 15px;"><input type="checkbox" id="filter-erd"> ERDs</label>
                <label class="filter-option" style="margin-right: 15px;"><input type="checkbox" id="filter-sequence"> Sequence</label>
                <label class="filter-option" style="margin-right: 15px;"><input type="checkbox" id="filter-class"> Class</label>
                <label class="filter-option" style="margin-right: 15px;"><input type="checkbox" id="filter-state"> State</label>
                <label class="filter-option"><input type="checkbox" id="filter-gantt"> Gantt</label>
            </div>
            <section id="search-results" style="margin-top: 15px; display: none;" aria-live="polite" aria-labelledby="results-heading">
                <h4 id="results-heading">Search Results: <span id="result-count">0</span> diagrams found</h4>
                <div id="results-container"></div>
            </section>
        </div>

        <!-- Table of Contents -->
        <details>
            <summary>Table of Contents</summary>
            <ul>
                <li>
<a href="#introduction">Introduction</a>
                    <ul>
                        <li>
<a href="#organization">Organization</a>
                            <ul>
                                <li><a href="#by-project-area">By Project Area</a></li>
                                <li><a href="#by-diagram-type">By Diagram Type</a></li>
                                <li><a href="#by-feature">By Feature</a></li>
                            </ul>
                        </li>
                        <li><a href="#format-availability">Format Availability</a></li>
                        <li><a href="#diagram-naming-conventions">Diagram Naming Conventions</a></li>
                        <li><a href="#how-to-use-this-index">How to Use This Index</a></li>
                    </ul>
                </li>
                <li><a href="#quick-reference">Quick Reference</a></li>
                <li>
<a href="#illustrations-index">Illustrations Index</a>
                    <ul>
                        <li><a href="#project-overview-planning">Project Overview &amp; Planning</a></li>
                        <li><a href="#technical-architecture">Technical Architecture</a></li>
                        <li><a href="#core-functionality">Core Functionality</a></li>
                        <li><a href="#implementation-details">Implementation Details</a></li>
                        <li><a href="#documentation">Documentation</a></li>
                    </ul>
                </li>
                <li>
<a href="#diagram-types">Diagram Types</a>
                    <ul>
                        <li><a href="#flowchart-diagrams">Flowcharts</a></li>
                        <li><a href="#entity-relationship-diagrams">Entity Relationship Diagrams (ERD)</a></li>
                        <li><a href="#sequence-diagrams">Sequence Diagrams</a></li>
                        <li><a href="#class-diagrams">Class Diagrams</a></li>
                        <li><a href="#state-diagrams">State Diagrams</a></li>
                        <li><a href="#gantt-diagrams">Gantt Charts</a></li>
                        <li><a href="#deployment-diagrams">Deployment Diagrams</a></li>
                    </ul>
                </li>
                <li>
<a href="#search-tips">Search Tips</a>
                    <ul>
                        <li><a href="#basic-search-strategies">Basic Search Strategies</a></li>
                        <li><a href="#advanced-search-techniques">Advanced Search Techniques</a></li>
                        <li><a href="#if-you-cant-find-a-diagram">If You Can't Find a Diagram</a></li>
                    </ul>
                </li>
                <li><a href="#contributing-new-diagrams">Contributing New Diagrams</a></li>
                <li><a href="#diagram-best-practices">Diagram Best Practices</a></li>
                <li><a href="#diagram-accessibility">Diagram Accessibility</a></li>
                <li><a href="#diagram-versioning-and-history">Diagram Versioning and History</a></li>
                <li><a href="#diagram-tools-and-resources">Diagram Tools and Resources</a></li>
                <li><a href="#diagram-templates">Diagram Templates</a></li>
                <li><a href="#feature-based-diagram-index">Feature-Based Diagram Index</a></li>
                <li><a href="#diagram-tags">Diagram Tags</a></li>
                <li><a href="#diagram-statistics">Diagram Statistics</a></li>
                <li><a href="#diagram-relationships">Diagram Relationships</a></li>
                <li><a href="#diagram-export-options">Diagram Export Options</a></li>
                <li><a href="#conclusion">Conclusion</a></li>
                <li><a href="#diagram-request-process">Diagram Request Process</a></li>
            </ul>
        </details>

        <h2 id="introduction">Introduction</h2>

        <p>This index provides a comprehensive catalog of all diagrams and illustrations used throughout the Enhanced Laravel Application documentation. It's designed to help you quickly find the visual resources you need for understanding the project.</p>

        <blockquote>
            <p><strong>Interactive Features</strong>: This document includes an interactive search tool at the top of the page that allows you to filter diagrams by name, type, description, or tags. The search tool supports both light and dark modes (toggle in the top-right corner of the search box). These interactive features require JavaScript to be enabled in your browser.</p>
        </blockquote>

        <h3 id="organization">Organization</h3>

        <p>The illustrations in this index are organized in multiple complementary ways to help you find the diagrams you need:</p>

        <h4 id="by-project-area">By Project Area</h4>

        <p>Diagrams are grouped into logical categories based on the aspect of the project they represent:</p>

        <ul>
            <li>
<strong>Project Overview &amp; Planning</strong>: High-level views of the project, including executive summaries, roadmaps, timelines, business value assessments, and risk analyses. These are particularly useful for stakeholders and project managers.</li>
            <li>
<strong>Technical Architecture</strong>: Diagrams detailing system architecture, deployment strategies, database schemas, and other technical foundations. Essential for developers and architects to understand the overall structure.</li>
            <li>
<strong>Core Functionality</strong>: Illustrations of core features and processes, including user management, team management, and content management workflows. These help developers understand how key features are implemented.</li>
            <li>
<strong>Implementation Details</strong>: Diagrams focusing on specific implementation aspects like CQRS, Event Sourcing, state machines, and other technical patterns.</li>
            <li>
<strong>Documentation</strong>: Diagrams explaining documentation structure, workflow, and components, providing guidance on how the project documentation is organized.</li>
        </ul>

        <h4 id="by-diagram-type">By Diagram Type</h4>

        <p>In the <a href="#diagram-types">Diagram Types</a> section, diagrams are categorized by their visual format to help you understand the different visualization techniques used throughout the documentation:</p>

        <ul>
            <li>
<strong>Flowcharts</strong>: For visualizing processes and workflows</li>
            <li>
<strong>ERDs</strong>: For database structure and relationships</li>
            <li>
<strong>Sequence Diagrams</strong>: For interactions between components</li>
            <li>
<strong>Class Diagrams</strong>: For object-oriented design</li>
            <li>
<strong>State Diagrams</strong>: For state transitions</li>
            <li>
<strong>Gantt Charts</strong>: For project timelines</li>
            <li>
<strong>Deployment Diagrams</strong>: For system infrastructure</li>
        </ul>

        <h4 id="by-feature">By Feature</h4>

        <p>Many diagrams are also relevant to specific application features. Here are the main feature categories:</p>

        <ul>
            <li>
<strong>Authentication &amp; Authorization</strong>: User login, registration, permissions</li>
            <li>
<strong>User Management</strong>: User profiles, settings, preferences</li>
            <li>
<strong>Team Management</strong>: Team creation, membership, roles</li>
            <li>
<strong>Content Management</strong>: Posts, comments, media handling</li>
            <li>
<strong>Notifications</strong>: Alerts, messages, email notifications</li>
            <li>
<strong>Search &amp; Discovery</strong>: Finding content and users</li>
            <li>
<strong>Analytics &amp; Reporting</strong>: Data visualization, metrics, KPIs</li>
        </ul>

        <h3 id="format-availability">Format Availability</h3>

        <p>Each diagram is available in both Mermaid and PlantUML formats, with dark and light mode variants to accommodate different viewing preferences and environments. The tables include direct links to all diagram files for easy access.</p>

        <h3 id="diagram-naming-conventions">Diagram Naming Conventions</h3>

        <p>Diagrams in this project follow these naming conventions:</p>

        <ol>
            <li>
<strong>Base Name</strong>: Describes the diagram's content (e.g., <code>architecture-overview</code>, <code>user-registration-sequence</code>)</li>
            <li>
<strong>Theme Suffix</strong>: Indicates whether it's a dark or light mode variant (<code>-dark</code> or <code>-light</code>)</li>
            <li>
<strong>Format Suffix</strong>: Indicates whether it's a Mermaid (<code>.md</code>) or PlantUML (<code>.puml</code>) file</li>
        </ol>

        <p>Example: <code>architecture-overview-dark.md</code> is the dark mode Mermaid version of the Architecture Overview diagram.</p>

        <h3 id="how-to-use-this-index">How to Use This Index</h3>

        <ol>
            <li>
<strong>Find by Project Area</strong>: If you're looking for diagrams related to a specific aspect of the project (e.g., architecture, implementation), use the <a href="#illustrations-index">Illustrations Index</a> section.</li>
            <li>
<strong>Find by Diagram Type</strong>: If you need a specific type of diagram (e.g. flowchart, sequence diagram), navigate to the <a href="#diagram-types">Diagram Types</a> section.</li>
            <li>
<strong>Find by Tags</strong>: Use the browser's search function (Ctrl+F/Cmd+F) to search for specific tags (e.g., <code>database</code>, <code>auth</code>, <code>timeline</code>) that are relevant to your needs. Tags are displayed in the Quick Reference section and help identify diagrams by their content or purpose.</li>
            <li>
<strong>Access Source Documents</strong>: Each diagram entry includes a link to its source document, where you can find more context and explanation.</li>
            <li>
<strong>Choose Your Preferred Format</strong>: Select either Mermaid or PlantUML format based on your needs, and choose between dark and light mode variants.</li>
            <li>
<strong>Navigate with the TOC</strong>: Use the Table of Contents at the top of this document to quickly jump to specific sections.</li>
        </ol>

        <h2 id="quick-reference">Quick Reference</h2>

        <p>This section provides quick access to the most frequently used diagrams in the project.</p>

        <blockquote>
            <p><strong>Note</strong>: This section includes thumbnail previews to help you quickly identify the diagrams visually. Thumbnails are stored in the <code>thumbnails</code> directory and are generated from the diagram source files.</p>
        </blockquote>

        <table>
            <thead>
                <tr>
                    <th scope="col">Thumbnail</th>
                    <th scope="col">Diagram Name</th>
                    <th scope="col">Type</th>
                    <th scope="col">Description</th>
                    <th scope="col">Tags</th>
                    <th scope="col">Source Document</th>
                    <th scope="col" colspan="2">Links</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div class="thumbnail-container" data-diagram-name="Executive Summary Overview" data-diagram-type="Flowchart" data-diagram-desc="Key features overview" data-diagram-dark="mermaid/dark/executive-summary-overview-dark.md" data-diagram-light="mermaid/light/executive-summary-overview-light.md">
                            <img src="thumbnails/plantuml/dark/executive-summary-overview-dark-thumb.svg" alt="Executive Summary Overview thumbnail" width="80" />
                        </div>
                    </td>
                    <td>Executive Summary Overview</td>
                    <td>Flowchart</td>
                    <td>Key features overview</td>
                    <td>
                        <span class="clickable-tag" data-tag="overview">overview</span>
                        <span class="clickable-tag" data-tag="features">features</span>
                        <span class="clickable-tag" data-tag="summary">summary</span>
                    </td>
                    <td><a href="../005-ela-executive-summary.md">Executive Summary</a></td>
                    <td><a href="mermaid/dark/executive-summary-overview-dark.md">Dark</a></td>
                    <td><a href="mermaid/light/executive-summary-overview-light.md">Light</a></td>
                </tr>
                <tr>
                    <td>
                        <div class="thumbnail-container" data-diagram-name="Architecture Overview" data-diagram-type="Flowchart" data-diagram-desc="High-level system architecture" data-diagram-dark="mermaid/dark/architecture-overview-dark.md" data-diagram-light="mermaid/light/architecture-overview-light.md">
                            <img src="thumbnails/plantuml/dark/architecture-overview-dark-thumb.svg" alt="Architecture Overview thumbnail" width="80" />
                        </div>
                    </td>
                    <td>Architecture Overview</td>
                    <td>Flowchart</td>
                    <td>High-level system architecture</td>
                    <td>
                        <span class="clickable-tag" data-tag="architecture">architecture</span>
                        <span class="clickable-tag" data-tag="system">system</span>
                        <span class="clickable-tag" data-tag="structure">structure</span>
                    </td>
                    <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                    <td><a href="mermaid/dark/architecture-overview-dark.md">Dark</a></td>
                    <td><a href="mermaid/light/architecture-overview-light.md">Light</a></td>
                </tr>
                <tr>
                    <td>
                        <div class="thumbnail-container" data-diagram-name="ERD Overview" data-diagram-type="ERD" data-diagram-desc="Entity Relationship Diagram" data-diagram-dark="mermaid/dark/erd-overview-dark.md" data-diagram-light="mermaid/light/erd-overview-light.md">
                            <img src="thumbnails/mermaid/light/erd-overview-light-thumb.svg" alt="ERD Overview thumbnail" width="120" />
                        </div>
                    </td>
                    <td>ERD Overview</td>
                    <td>ERD</td>
                    <td>Entity Relationship Diagram</td>
                    <td>
                        <span class="clickable-tag" data-tag="database">database</span>
                        <span class="clickable-tag" data-tag="entities">entities</span>
                        <span class="clickable-tag" data-tag="schema">schema</span>
                    </td>
                    <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                    <td><a href="mermaid/dark/erd-overview-dark.md">Dark</a></td>
                    <td><a href="mermaid/light/erd-overview-light.md">Light</a></td>
                </tr>
                <tr>
                    <td>
                        <div class="thumbnail-container" data-diagram-name="Authentication Flow" data-diagram-type="Sequence" data-diagram-desc="User authentication process" data-diagram-dark="mermaid/dark/authentication-flow-dark.md" data-diagram-light="mermaid/light/authentication-flow-light.md">
                            <img src="thumbnails/plantuml/dark/authentication-flow-dark-thumb.svg" alt="Authentication Flow thumbnail" width="80" />
                        </div>
                    </td>
                    <td>Authentication Flow</td>
                    <td>Sequence</td>
                    <td>User authentication process</td>
                    <td>
                        <span class="clickable-tag" data-tag="auth">auth</span>
                        <span class="clickable-tag" data-tag="login">login</span>
                        <span class="clickable-tag" data-tag="security">security</span>
                    </td>
                    <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                    <td><a href="mermaid/dark/authentication-flow-dark.md">Dark</a></td>
                    <td><a href="mermaid/light/authentication-flow-light.md">Light</a></td>
                </tr>
                <tr>
                    <td>
                        <div class="thumbnail-container" data-diagram-name="Project Roadmap" data-diagram-type="Gantt" data-diagram-desc="Project roadmap timeline" data-diagram-dark="mermaid/dark/project-roadmap-dark.md" data-diagram-light="mermaid/light/project-roadmap-light.md">
                            <img src="thumbnails/plantuml/dark/project-roadmap-dark-thumb.svg" alt="Project Roadmap thumbnail" width="80" />
                        </div>
                    </td>
                    <td>Project Roadmap</td>
                    <td>Gantt</td>
                    <td>Project roadmap timeline</td>
                    <td>
                        <span class="clickable-tag" data-tag="timeline">timeline</span>
                        <span class="clickable-tag" data-tag="planning">planning</span>
                        <span class="clickable-tag" data-tag="schedule">schedule</span>
                    </td>
                    <td><a href="../020-ela-project-roadmap.md">Project Roadmap</a></td>
                    <td><a href="mermaid/dark/project-roadmap-dark.md">Dark</a></td>
                    <td><a href="mermaid/light/project-roadmap-light.md">Light</a></td>
                </tr>
            </tbody>
        </table>

        <h2 id="illustrations-index">Illustrations Index</h2>

        <blockquote>
            <p><strong>Note</strong>: Some diagrams may appear in multiple categories if they are relevant to different aspects of the project. The primary listing for each diagram is in the most relevant category.</p>
        </blockquote>

        <details>
            <summary id="project-overview-planning"><strong style="font-size: 1.2em;">Project Overview &amp; Planning</strong></summary>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col" rowspan="2">Diagram Name</th>
                        <th scope="col" rowspan="2">Type</th>
                        <th scope="col" rowspan="2">Description</th>
                        <th scope="col" rowspan="2">Source Document</th>
                        <th scope="col" colspan="2">Mermaid File Paths</th>
                        <th scope="col" colspan="2">PlantUML File Paths</th>
                    </tr>
                    <tr>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Executive Summary Overview" data-diagram-type="Flowchart" data-diagram-desc="Executive Summary Overview" data-diagram-dark="mermaid/dark/executive-summary-overview-dark.md" data-diagram-light="mermaid/light/executive-summary-overview-light.md"><img src="thumbnails/plantuml/dark/executive-summary-overview-dark-thumb.svg" alt="Executive Summary Overview thumbnail" width="80" /></div></td>
                        <td>Executive Summary Overview</td>
                        <td>Flowchart</td>
                        <td>Key features overview</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/executive-summary-overview-dark.md">executive-summary-overview-dark.md</a></td>
                        <td><a href="mermaid/light/executive-summary-overview-light.md">executive-summary-overview-light.md</a></td>
                        <td><a href="plantuml/dark/executive-summary-overview-dark.puml">executive-summary-overview-dark.puml</a></td>
                        <td><a href="plantuml/light/executive-summary-overview-light.puml">executive-summary-overview-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Executive Summary Features" data-diagram-type="Flowchart" data-diagram-desc="Executive Summary Features"><img src="thumbnails/plantuml/dark/executive-summary-features-dark-thumb.svg" alt="Executive Summary Features thumbnail" width="80" /></div></td>
                        <td>Executive Summary Features</td>
                        <td>Flowchart</td>
                        <td>Key features overview</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/executive-summary-features-dark.md">executive-summary-features-dark.md</a></td>
                        <td><a href="mermaid/light/executive-summary-features-light.md">executive-summary-features-light.md</a></td>
                        <td><a href="plantuml/dark/executive-summary-features-dark.puml">executive-summary-features-dark.puml</a></td>
                        <td><a href="plantuml/light/executive-summary-features-light.puml">executive-summary-features-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Project Overview" data-diagram-type="Flowchart" data-diagram-desc="Project Overview"><img src="thumbnails/plantuml/dark/project-overview-dark-thumb.svg" alt="Project Overview thumbnail" width="80" /></div></td>
                        <td>Project Overview</td>
                        <td>Flowchart</td>
                        <td>Project overview diagram</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/project-overview-dark.md">project-overview-dark.md</a></td>
                        <td><a href="mermaid/light/project-overview-light.md">project-overview-light.md</a></td>
                        <td><a href="plantuml/dark/project-overview-dark.puml">project-overview-dark.puml</a></td>
                        <td><a href="plantuml/light/project-overview-light.puml">project-overview-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Project Roadmap" data-diagram-type="Gantt" data-diagram-desc="Project roadmap timeline" data-diagram-dark="mermaid/dark/project-roadmap-dark.md" data-diagram-light="mermaid/light/project-roadmap-light.md"><img src="thumbnails/plantuml/dark/project-roadmap-dark-thumb.svg" alt="Project Roadmap thumbnail" width="80" /></div></td>
                        <td>Project Roadmap</td>
                        <td>Gantt</td>
                        <td>Project roadmap timeline</td>
                        <td><a href="../020-ela-project-roadmap.md">020-ela-project-roadmap.md</a></td>
                        <td><a href="mermaid/dark/project-roadmap-dark.md">project-roadmap-dark.md</a></td>
                        <td><a href="mermaid/light/project-roadmap-light.md">project-roadmap-light.md</a></td>
                        <td><a href="plantuml/dark/project-roadmap-dark.puml">project-roadmap-dark.puml</a></td>
                        <td><a href="plantuml/light/project-roadmap-light.puml">project-roadmap-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Implementation Timeline" data-diagram-type="Gantt" data-diagram-desc="Implementation Timeline"><img src="thumbnails/plantuml/dark/implementation-timeline-dark-thumb.svg" alt="Implementation Timeline thumbnail" width="80" /></div></td>
                        <td>Implementation Timeline</td>
                        <td>Gantt</td>
                        <td>Project implementation timeline</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/implementation-timeline-dark.md">implementation-timeline-dark.md</a></td>
                        <td><a href="mermaid/light/implementation-timeline-light.md">implementation-timeline-light.md</a></td>
                        <td><a href="plantuml/dark/implementation-timeline-dark.puml">implementation-timeline-dark.puml</a></td>
                        <td><a href="plantuml/light/implementation-timeline-light.puml">implementation-timeline-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Resource Allocation Timeline" data-diagram-type="Gantt" data-diagram-desc="Resource Allocation Timeline"><img src="thumbnails/plantuml/dark/resource-allocation-timeline-dark-thumb.svg" alt="Resource Allocation Timeline thumbnail" width="80" /></div></td>
                        <td>Resource Allocation Timeline</td>
                        <td>Gantt</td>
                        <td>Project resource timeline</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/resource-allocation-timeline-dark.md">resource-allocation-timeline-dark.md</a></td>
                        <td><a href="mermaid/light/resource-allocation-timeline-light.md">resource-allocation-timeline-light.md</a></td>
                        <td><a href="plantuml/dark/resource-allocation-timeline-dark.puml">resource-allocation-timeline-dark.puml</a></td>
                        <td><a href="plantuml/light/resource-allocation-timeline-light.puml">resource-allocation-timeline-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Business Value" data-diagram-type="Flowchart" data-diagram-desc="Business Value"><img src="thumbnails/plantuml/dark/business-value-dark-thumb.svg" alt="Business Value thumbnail" width="80" /></div></td>
                        <td>Business Value</td>
                        <td>Flowchart</td>
                        <td>Business value overview</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/business-value-dark.md">business-value-dark.md</a></td>
                        <td><a href="mermaid/light/business-value-light.md">business-value-light.md</a></td>
                        <td><a href="plantuml/dark/business-value-dark.puml">business-value-dark.puml</a></td>
                        <td><a href="plantuml/light/business-value-light.puml">business-value-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Risk Assessment" data-diagram-type="Quadrant" data-diagram-desc="Risk Assessment"><img src="thumbnails/plantuml/dark/risk-assessment-dark-thumb.svg" alt="Risk Assessment thumbnail" width="80" /></div></td>
                        <td>Risk Assessment</td>
                        <td>Quadrant</td>
                        <td>Risk assessment matrix</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/risk-assessment-dark.md">risk-assessment-dark.md</a></td>
                        <td><a href="mermaid/light/risk-assessment-light.md">risk-assessment-light.md</a></td>
                        <td><a href="plantuml/dark/risk-assessment-dark.puml">risk-assessment-dark.puml</a></td>
                        <td><a href="plantuml/light/risk-assessment-light.puml">risk-assessment-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Success Metrics" data-diagram-type="Flowchart" data-diagram-desc="Success Metrics"><img src="thumbnails/plantuml/dark/success-metrics-dark-thumb.svg" alt="Success Metrics thumbnail" width="80" /></div></td>
                        <td>Success Metrics</td>
                        <td>Flowchart</td>
                        <td>Success metrics overview</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/success-metrics-dark.md">success-metrics-dark.md</a></td>
                        <td><a href="mermaid/light/success-metrics-light.md">success-metrics-light.md</a></td>
                        <td><a href="plantuml/dark/success-metrics-dark.puml">success-metrics-dark.puml</a></td>
                        <td><a href="plantuml/light/success-metrics-light.puml">success-metrics-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Technology Stack" data-diagram-type="Flowchart" data-diagram-desc="Technology Stack"><img src="thumbnails/plantuml/dark/technology-stack-dark-thumb.svg" alt="Technology Stack thumbnail" width="80" /></div></td>
                        <td>Technology Stack</td>
                        <td>Flowchart</td>
                        <td>Technology stack overview</td>
                        <td><a href="../005-ela-executive-summary.md">005-ela-executive-summary.md</a></td>
                        <td><a href="mermaid/dark/technology-stack-dark.md">technology-stack-dark.md</a></td>
                        <td><a href="mermaid/light/technology-stack-light.md">technology-stack-light.md</a></td>
                        <td><a href="plantuml/dark/technology-stack-dark.puml">technology-stack-dark.puml</a></td>
                        <td><a href="plantuml/light/technology-stack-light.puml">technology-stack-light.puml</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details>
            <summary id="technical-architecture"><strong style="font-size: 1.2em;">Technical Architecture</strong></summary>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col" rowspan="2">Diagram Name</th>
                        <th scope="col" rowspan="2">Type</th>
                        <th scope="col" rowspan="2">Description</th>
                        <th scope="col" rowspan="2">Source Document</th>
                        <th scope="col" colspan="2">Mermaid File Paths</th>
                        <th scope="col" colspan="2">PlantUML File Paths</th>
                    </tr>
                    <tr>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Architecture Overview" data-diagram-type="Flowchart" data-diagram-desc="Architecture Overview" data-diagram-dark="mermaid/dark/architecture-overview-dark.md" data-diagram-light="mermaid/light/architecture-overview-light.md"><img src="thumbnails/plantuml/dark/architecture-overview-dark-thumb.svg" alt="Architecture Overview thumbnail" width="80" /></div></td>
                        <td>Architecture Overview</td>
                        <td>Flowchart</td>
                        <td>High-level system architecture</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/architecture-overview-dark.md">architecture-overview-dark.md</a></td>
                        <td><a href="mermaid/light/architecture-overview-light.md">architecture-overview-light.md</a></td>
                        <td><a href="plantuml/dark/architecture-overview-dark.puml">architecture-overview-dark.puml</a></td>
                        <td><a href="plantuml/light/architecture-overview-light.puml">architecture-overview-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="TAD Architecture" data-diagram-type="Flowchart" data-diagram-desc="TAD Architecture"><img src="thumbnails/mermaid/light/tad-architecture-light-thumb.svg" alt="TAD Architecture thumbnail" width="80" /></div></td>
                        <td>TAD Architecture</td>
                        <td>Flowchart</td>
                        <td>Technical architecture diagram</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/tad-architecture-dark.md">tad-architecture-dark.md</a></td>
                        <td><a href="mermaid/light/tad-architecture-light.md">tad-architecture-light.md</a></td>
                        <td><a href="plantuml/dark/tad-architecture-dark.puml">tad-architecture-dark.puml</a></td>
                        <td><a href="plantuml/light/tad-architecture-light.puml">tad-architecture-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Deployment Architecture" data-diagram-type="Deployment" data-diagram-desc="Deployment Architecture"><img src="thumbnails/plantuml/dark/deployment-architecture-dark-thumb.svg" alt="Deployment Architecture thumbnail" width="80" /></div></td>
                        <td>Deployment Architecture</td>
                        <td>Deployment</td>
                        <td>System deployment architecture</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/deployment-architecture-dark.md">deployment-architecture-dark.md</a></td>
                        <td><a href="mermaid/light/deployment-architecture-light.md">deployment-architecture-light.md</a></td>
                        <td><a href="plantuml/dark/deployment-architecture-dark.puml">deployment-architecture-dark.puml</a></td>
                        <td><a href="plantuml/light/deployment-architecture-light.puml">deployment-architecture-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="TAD Deployment" data-diagram-type="Deployment" data-diagram-desc="TAD Deployment"><img src="thumbnails/mermaid/light/tad-deployment-light-thumb.svg" alt="TAD Deployment thumbnail" width="80" /></div></td>
                        <td>TAD Deployment</td>
                        <td>Deployment</td>
                        <td>Deployment diagram</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/tad-deployment-dark.md">tad-deployment-dark.md</a></td>
                        <td><a href="mermaid/light/tad-deployment-light.md">tad-deployment-light.md</a></td>
                        <td><a href="plantuml/dark/tad-deployment-dark.puml">tad-deployment-dark.puml</a></td>
                        <td><a href="plantuml/light/tad-deployment-light.puml">tad-deployment-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="ERD Overview" data-diagram-type="ERD" data-diagram-desc="Entity Relationship Diagram" data-diagram-dark="mermaid/dark/erd-overview-dark.md" data-diagram-light="mermaid/light/erd-overview-light.md"><img src="thumbnails/mermaid/light/erd-overview-light-thumb.svg" alt="ERD Overview thumbnail" width="80" /></div></td>
                        <td>ERD Overview</td>
                        <td>ERD</td>
                        <td>Entity Relationship Diagram (TAD version)</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/erd-overview-dark.md">erd-overview-dark.md</a></td>
                        <td><a href="mermaid/light/erd-overview-light.md">erd-overview-light.md</a></td>
                        <td><a href="plantuml/dark/erd-overview-dark.puml">erd-overview-dark.puml</a></td>
                        <td><a href="plantuml/light/erd-overview-light.puml">erd-overview-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="ERD Overview (Enhanced)" data-diagram-type="ERD" data-diagram-desc="Entity Relationship Diagram (Enhanced version)" data-diagram-dark="mermaid/dark/erd-overview-dark.md" data-diagram-light="mermaid/light/erd-overview-light.md"><img src="thumbnails/mermaid/light/erd-overview-light-thumb.svg" alt="ERD Overview (Enhanced) thumbnail" width="80" /></div></td>
                        <td>ERD Overview (Enhanced)</td>
                        <td>ERD</td>
                        <td>Entity Relationship Diagram (Enhanced version)</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">100-610-enhanced-diagrams.md</a></td>
                        <td><a href="mermaid/dark/erd-overview-dark.md">erd-overview-dark.md</a></td>
                        <td><a href="mermaid/light/erd-overview-light.md">erd-overview-light.md</a></td>
                        <td><a href="plantuml/dark/erd-overview-dark.puml">erd-overview-dark.puml</a></td>
                        <td><a href="plantuml/light/erd-overview-light.puml">erd-overview-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="TAD Database Schema" data-diagram-type="ERD" data-diagram-desc="TAD Database Schema"><img src="thumbnails/mermaid/light/tad-database-schema-light-thumb.svg" alt="TAD Database Schema thumbnail" width="80" /></div></td>
                        <td>TAD Database Schema</td>
                        <td>ERD</td>
                        <td>Database schema diagram</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/tad-database-schema-dark.md">tad-database-schema-dark.md</a></td>
                        <td><a href="mermaid/light/tad-database-schema-light.md">tad-database-schema-light.md</a></td>
                        <td><a href="plantuml/dark/tad-database-schema-dark.puml">tad-database-schema-dark.puml</a></td>
                        <td><a href="plantuml/light/tad-database-schema-light.puml">tad-database-schema-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Class Diagram (Detailed)" data-diagram-type="Class" data-diagram-desc="Class Diagram (Detailed)"><img src="thumbnails/mermaid/light/class-diagram-detailed-light-thumb.svg" alt="Class Diagram (Detailed) thumbnail" width="80" /></div></td>
                        <td>Class Diagram (Detailed)</td>
                        <td>Class</td>
                        <td>Comprehensive class structure with all attributes and methods</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">100-610-enhanced-diagrams.md</a></td>
                        <td><a href="mermaid/dark/class-diagram-detailed-dark.md">class-diagram-detailed-dark.md</a></td>
                        <td><a href="mermaid/light/class-diagram-detailed-light.md">class-diagram-detailed-light.md</a></td>
                        <td><a href="plantuml/dark/class-diagram-detailed-dark.puml">class-diagram-detailed-dark.puml</a></td>
                        <td><a href="plantuml/light/class-diagram-detailed-light.puml">class-diagram-detailed-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Class Diagram (Overview)" data-diagram-type="Class" data-diagram-desc="Class Diagram (Overview)"><img src="thumbnails/mermaid/light/class-diagram-light-thumb.svg" alt="Class Diagram (Overview) thumbnail" width="80" /></div></td>
                        <td>Class Diagram (Overview)</td>
                        <td>Class</td>
                        <td>Simplified class structure showing key relationships</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">100-610-enhanced-diagrams.md</a></td>
                        <td><a href="mermaid/dark/class-diagram-dark.md">class-diagram-dark.md</a></td>
                        <td><a href="mermaid/light/class-diagram-light.md">class-diagram-light.md</a></td>
                        <td><a href="plantuml/dark/class-diagram-dark.puml">class-diagram-dark.puml</a></td>
                        <td><a href="plantuml/light/class-diagram-light.puml">class-diagram-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="TAD Request Lifecycle" data-diagram-type="Sequence" data-diagram-desc="TAD Request Lifecycle"><img src="thumbnails/mermaid/light/tad-request-lifecycle-light-thumb.svg" alt="TAD Request Lifecycle thumbnail" width="80" /></div></td>
                        <td>TAD Request Lifecycle</td>
                        <td>Sequence</td>
                        <td>Request lifecycle diagram</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/tad-request-lifecycle-dark.md">tad-request-lifecycle-dark.md</a></td>
                        <td><a href="mermaid/light/tad-request-lifecycle-light.md">tad-request-lifecycle-light.md</a></td>
                        <td><a href="plantuml/dark/tad-request-lifecycle-dark.puml">tad-request-lifecycle-dark.puml</a></td>
                        <td><a href="plantuml/light/tad-request-lifecycle-light.puml">tad-request-lifecycle-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="TAD Event Flow" data-diagram-type="Flowchart" data-diagram-desc="TAD Event Flow"><img src="thumbnails/mermaid/light/tad-event-flow-light-thumb.svg" alt="TAD Event Flow thumbnail" width="80" /></div></td>
                        <td>TAD Event Flow</td>
                        <td>Flowchart</td>
                        <td>Event flow diagram</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/tad-event-flow-dark.md">tad-event-flow-dark.md</a></td>
                        <td><a href="mermaid/light/tad-event-flow-light.md">tad-event-flow-light.md</a></td>
                        <td><a href="plantuml/dark/tad-event-flow-dark.puml">tad-event-flow-dark.puml</a></td>
                        <td><a href="plantuml/light/tad-event-flow-light.puml">tad-event-flow-light.puml</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details>
            <summary id="core-functionality"><strong style="font-size: 1.2em;">Core Functionality</strong></summary>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col" rowspan="2">Diagram Name</th>
                        <th scope="col" rowspan="2">Type</th>
                        <th scope="col" rowspan="2">Description</th>
                        <th scope="col" rowspan="2">Source Document</th>
                        <th scope="col" colspan="2">Mermaid File Paths</th>
                        <th scope="col" colspan="2">PlantUML File Paths</th>
                    </tr>
                    <tr>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Authentication Flow" data-diagram-type="Sequence" data-diagram-desc="User authentication process" data-diagram-dark="mermaid/dark/authentication-flow-dark.md" data-diagram-light="mermaid/light/authentication-flow-light.md"><img src="thumbnails/plantuml/dark/authentication-flow-dark-thumb.svg" alt="Authentication Flow thumbnail" width="80" /></div></td>
                        <td>Authentication Flow</td>
                        <td>Sequence</td>
                        <td>User authentication process</td>
                        <td><a href="../030-ela-tad.md">030-ela-tad.md</a></td>
                        <td><a href="mermaid/dark/authentication-flow-dark.md">authentication-flow-dark.md</a></td>
                        <td><a href="mermaid/light/authentication-flow-light.md">authentication-flow-light.md</a></td>
                        <td><a href="plantuml/dark/authentication-flow-dark.puml">authentication-flow-dark.puml</a></td>
                        <td><a href="plantuml/light/authentication-flow-light.puml">authentication-flow-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="User Registration Sequence" data-diagram-type="Sequence" data-diagram-desc="User Registration Sequence"><img src="thumbnails/plantuml/dark/user-registration-sequence-dark-thumb.svg" alt="User Registration Sequence thumbnail" width="80" /></div></td>
                        <td>User Registration Sequence</td>
                        <td>Sequence</td>
                        <td>User registration process</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">100-610-enhanced-diagrams.md</a></td>
                        <td><a href="mermaid/dark/user-registration-sequence-dark.md">user-registration-sequence-dark.md</a></td>
                        <td><a href="mermaid/light/user-registration-sequence-light.md">user-registration-sequence-light.md</a></td>
                        <td><a href="plantuml/dark/user-registration-sequence-dark.puml">user-registration-sequence-dark.puml</a></td>
                        <td><a href="plantuml/light/user-registration-sequence-light.puml">user-registration-sequence-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Team Creation Sequence" data-diagram-type="Sequence" data-diagram-desc="Team Creation Sequence"><img src="thumbnails/plantuml/dark/team-creation-sequence-dark-thumb.svg" alt="Team Creation Sequence thumbnail" width="80" /></div></td>
                        <td>Team Creation Sequence</td>
                        <td>Sequence</td>
                        <td>Team creation process</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">100-610-enhanced-diagrams.md</a></td>
                        <td><a href="mermaid/dark/team-creation-sequence-dark.md">team-creation-sequence-dark.md</a></td>
                        <td><a href="mermaid/light/team-creation-sequence-light.md">team-creation-sequence-light.md</a></td>
                        <td><a href="plantuml/dark/team-creation-sequence-dark.puml">team-creation-sequence-dark.puml</a></td>
                        <td><a href="plantuml/light/team-creation-sequence-light.puml">team-creation-sequence-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Post Creation Sequence" data-diagram-type="Sequence" data-diagram-desc="Post Creation Sequence"><img src="thumbnails/plantuml/dark/post-creation-sequence-dark-thumb.svg" alt="Post Creation Sequence thumbnail" width="80" /></div></td>
                        <td>Post Creation Sequence</td>
                        <td>Sequence</td>
                        <td>Post creation process</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">100-610-enhanced-diagrams.md</a></td>
                        <td><a href="mermaid/dark/post-creation-sequence-dark.md">post-creation-sequence-dark.md</a></td>
                        <td><a href="mermaid/light/post-creation-sequence-light.md">post-creation-sequence-light.md</a></td>
                        <td><a href="plantuml/dark/post-creation-sequence-dark.puml">post-creation-sequence-dark.puml</a></td>
                        <td><a href="plantuml/light/post-creation-sequence-light.puml">post-creation-sequence-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="CQRS Flow" data-diagram-type="Flowchart" data-diagram-desc="CQRS Flow"><img src="thumbnails/mermaid/light/cqrs-flow-light-thumb.svg" alt="CQRS Flow thumbnail" width="80" /></div></td>
                        <td>CQRS Flow</td>
                        <td>Flowchart</td>
                        <td>Command Query Responsibility Segregation</td>
                        <td><a href="../100-implementation-plan/100-060-cqrs-configuration.md">100-060-cqrs-configuration.md</a></td>
                        <td><a href="mermaid/dark/cqrs-flow-dark.md">cqrs-flow-dark.md</a></td>
                        <td><a href="mermaid/light/cqrs-flow-light.md">cqrs-flow-light.md</a></td>
                        <td><a href="plantuml/dark/cqrs-flow-dark.puml">cqrs-flow-dark.puml</a></td>
                        <td><a href="plantuml/light/cqrs-flow-light.puml">cqrs-flow-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Event Sourcing Flow" data-diagram-type="Flowchart" data-diagram-desc="Event Sourcing Flow"><img src="thumbnails/plantuml/dark/event-sourcing-flow-dark-thumb.svg" alt="Event Sourcing Flow thumbnail" width="80" /></div></td>
                        <td>Event Sourcing Flow</td>
                        <td>Flowchart</td>
                        <td>Event sourcing implementation</td>
                        <td><a href="../100-implementation-plan/100-350-event-sourcing/050-implementation.md">050-implementation.md</a></td>
                        <td><a href="mermaid/dark/event-sourcing-flow-dark.md">event-sourcing-flow-dark.md</a></td>
                        <td><a href="mermaid/light/event-sourcing-flow-light.md">event-sourcing-flow-light.md</a></td>
                        <td><a href="plantuml/dark/event-sourcing-flow-dark.puml">event-sourcing-flow-dark.puml</a></td>
                        <td><a href="plantuml/light/event-sourcing-flow-light.puml">event-sourcing-flow-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Todo State Machine" data-diagram-type="State" data-diagram-desc="Todo state transitions diagram" data-diagram-dark="mermaid/dark/todo-state-machine-dark.md" data-diagram-light="mermaid/light/todo-state-machine-light.md"><img src="thumbnails/plantuml/dark/todo-state-machine-dark-thumb.svg" alt="Todo State Machine thumbnail" width="80" /></div></td>
                        <td>Todo State Machine</td>
                        <td>State</td>
                        <td>Todo item state transitions</td>
                        <td><a href="../100-implementation-plan/100-370-status-implementation.md">100-370-status-implementation.md</a></td>
                        <td><a href="mermaid/dark/todo-state-machine-dark.md">todo-state-machine-dark.md</a></td>
                        <td><a href="mermaid/light/todo-state-machine-light.md">todo-state-machine-light.md</a></td>
                        <td><a href="plantuml/dark/todo-state-machine-dark.puml">todo-state-machine-dark.puml</a></td>
                        <td><a href="plantuml/light/todo-state-machine-light.puml">todo-state-machine-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Filament Admin Panel" data-diagram-type="Flowchart" data-diagram-desc="Filament Admin Panel"><img src="thumbnails/plantuml/dark/filament-admin-panel-dark-thumb.svg" alt="Filament Admin Panel thumbnail" width="80" /></div></td>
                        <td>Filament Admin Panel</td>
                        <td>Flowchart</td>
                        <td>Filament admin panel structure</td>
                        <td><a href="../100-implementation-plan/030-core-components/040-filament-configuration.md">100-070-filament-configuration.md</a></td>
                        <td><a href="mermaid/dark/filament-admin-panel-dark.md">filament-admin-panel-dark.md</a></td>
                        <td><a href="mermaid/light/filament-admin-panel-light.md">filament-admin-panel-light.md</a></td>
                        <td><a href="plantuml/dark/filament-admin-panel-dark.puml">filament-admin-panel-dark.puml</a></td>
                        <td><a href="plantuml/light/filament-admin-panel-light.puml">filament-admin-panel-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Filament Resources" data-diagram-type="Flowchart" data-diagram-desc="Filament Resources"><img src="thumbnails/plantuml/dark/filament-resources-dark-thumb.svg" alt="Filament Resources thumbnail" width="80" /></div></td>
                        <td>Filament Resources</td>
                        <td>Flowchart</td>
                        <td>Filament resources structure</td>
                        <td><a href="../100-implementation-plan/030-core-components/040-filament-configuration.md">100-070-filament-configuration.md</a></td>
                        <td><a href="mermaid/dark/filament-resources-dark.md">filament-resources-dark.md</a></td>
                        <td><a href="mermaid/light/filament-resources-light.md">filament-resources-light.md</a></td>
                        <td><a href="plantuml/dark/filament-resources-dark.puml">filament-resources-dark.puml</a></td>
                        <td><a href="plantuml/light/filament-resources-light.puml">filament-resources-light.puml</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details>
            <summary id="implementation-details"><strong style="font-size: 1.2em;">Implementation Details</strong></summary>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col" rowspan="2">Diagram Name</th>
                        <th scope="col" rowspan="2">Type</th>
                        <th scope="col" rowspan="2">Description</th>
                        <th scope="col" rowspan="2">Source Document</th>
                        <th scope="col" colspan="2">Mermaid File Paths</th>
                        <th scope="col" colspan="2">PlantUML File Paths</th>
                    </tr>
                    <tr>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Migration Sequence" data-diagram-type="Sequence" data-diagram-desc="Migration Sequence"><img src="thumbnails/plantuml/dark/migration-sequence-dark-thumb.svg" alt="Migration Sequence thumbnail" width="80" /></div></td>
                        <td>Migration Sequence</td>
                        <td>Sequence</td>
                        <td>Database migration process</td>
                        <td><a href="../100-implementation-plan/100-110-database-migrations.md">100-110-database-migrations.md</a></td>
                        <td><a href="mermaid/dark/migration-sequence-dark.md">migration-sequence-dark.md</a></td>
                        <td><a href="mermaid/light/migration-sequence-light.md">migration-sequence-light.md</a></td>
                        <td><a href="plantuml/dark/migration-sequence-dark.puml">migration-sequence-dark.puml</a></td>
                        <td><a href="plantuml/light/migration-sequence-light.puml">migration-sequence-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="CQRS Implementation" data-diagram-type="Flowchart" data-diagram-desc="CQRS Implementation"><img src="thumbnails/mermaid/light/cqrs-implementation-light-thumb.svg" alt="CQRS Implementation thumbnail" width="80" /></div></td>
                        <td>CQRS Implementation</td>
                        <td>Flowchart</td>
                        <td>CQRS implementation details</td>
                        <td><a href="../100-implementation-plan/100-060-cqrs-configuration.md">100-060-cqrs-configuration.md</a></td>
                        <td><a href="mermaid/dark/cqrs-flow-dark.md">cqrs-flow-dark.md</a></td>
                        <td><a href="mermaid/light/cqrs-flow-light.md">cqrs-flow-light.md</a></td>
                        <td><a href="plantuml/dark/cqrs-flow-dark.puml">cqrs-flow-dark.puml</a></td>
                        <td><a href="plantuml/light/cqrs-flow-light.puml">cqrs-flow-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Event Sourcing Implementation" data-diagram-type="Flowchart" data-diagram-desc="Event sourcing implementation details" data-diagram-dark="mermaid/dark/event-sourcing-flow-dark.md" data-diagram-light="mermaid/light/event-sourcing-flow-light.md"><img src="thumbnails/mermaid/light/event-sourcing-flow-light-thumb.svg" alt="Event Sourcing Implementation thumbnail" width="80" /></div></td>
                        <td>Event Sourcing Implementation</td>
                        <td>Flowchart</td>
                        <td>Event sourcing implementation details</td>
                        <td><a href="../100-implementation-plan/100-350-event-sourcing/050-implementation.md">050-implementation.md</a></td>
                        <td><a href="mermaid/dark/event-sourcing-flow-dark.md">event-sourcing-flow-dark.md</a></td>
                        <td><a href="mermaid/light/event-sourcing-flow-light.md">event-sourcing-flow-light.md</a></td>
                        <td><a href="plantuml/dark/event-sourcing-flow-dark.puml">event-sourcing-flow-dark.puml</a></td>
                        <td><a href="plantuml/light/event-sourcing-flow-light.puml">event-sourcing-flow-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Status Implementation" data-diagram-type="State" data-diagram-desc="Status implementation details" data-diagram-dark="mermaid/dark/todo-state-machine-dark.md" data-diagram-light="mermaid/light/todo-state-machine-light.md"><img src="thumbnails/plantuml/dark/status-implementation-dark-thumb.svg" alt="Status Implementation thumbnail" width="80" /></div></td>
                        <td>Status Implementation</td>
                        <td>State</td>
                        <td>Status implementation details</td>
                        <td><a href="../100-implementation-plan/100-370-status-implementation.md">100-370-status-implementation.md</a></td>
                        <td><a href="mermaid/dark/todo-state-machine-dark.md">todo-state-machine-dark.md</a></td>
                        <td><a href="mermaid/light/todo-state-machine-light.md">todo-state-machine-light.md</a></td>
                        <td><a href="plantuml/dark/todo-state-machine-dark.puml">todo-state-machine-dark.puml</a></td>
                        <td><a href="plantuml/light/todo-state-machine-light.puml">todo-state-machine-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Filament Configuration" data-diagram-type="Flowchart" data-diagram-desc="Filament Configuration"><img src="thumbnails/plantuml/dark/filament-configuration-dark-thumb.svg" alt="Filament Configuration thumbnail" width="80" /></div></td>
                        <td>Filament Configuration</td>
                        <td>Flowchart</td>
                        <td>Filament configuration details</td>
                        <td><a href="../100-implementation-plan/030-core-components/040-filament-configuration.md">100-070-filament-configuration.md</a></td>
                        <td><a href="mermaid/dark/filament-configuration-dark.md">filament-configuration-dark.md</a></td>
                        <td><a href="mermaid/light/filament-configuration-light.md">filament-configuration-light.md</a></td>
                        <td><a href="plantuml/dark/filament-configuration-dark.puml">filament-configuration-dark.puml</a></td>
                        <td><a href="plantuml/light/filament-configuration-light.puml">filament-configuration-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Database Schema Implementation" data-diagram-type="ERD" data-diagram-desc="Database Schema Implementation"><img src="thumbnails/plantuml/dark/database-schema-implementation-dark-thumb.svg" alt="Database Schema Implementation thumbnail" width="80" /></div></td>
                        <td>Database Schema Implementation</td>
                        <td>ERD</td>
                        <td>Database schema implementation details</td>
                        <td><a href="../100-implementation-plan/100-110-database-migrations.md">100-110-database-migrations.md</a></td>
                        <td><a href="mermaid/dark/database-schema-implementation-dark.md">database-schema-implementation-dark.md</a></td>
                        <td><a href="mermaid/light/database-schema-implementation-light.md">database-schema-implementation-light.md</a></td>
                        <td><a href="plantuml/dark/database-schema-implementation-dark.puml">database-schema-implementation-dark.puml</a></td>
                        <td><a href="plantuml/light/database-schema-implementation-light.puml">database-schema-implementation-light.puml</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details>
            <summary id="documentation"><strong style="font-size: 1.2em;">Documentation</strong></summary>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col" rowspan="2">Diagram Name</th>
                        <th scope="col" rowspan="2">Type</th>
                        <th scope="col" rowspan="2">Description</th>
                        <th scope="col" rowspan="2">Source Document</th>
                        <th scope="col" colspan="2">Mermaid File Paths</th>
                        <th scope="col" colspan="2">PlantUML File Paths</th>
                    </tr>
                    <tr>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                        <th scope="col"><strong>Dark</strong></th>
                        <th scope="col"><strong>Light</strong></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Documentation Structure" data-diagram-type="Flowchart" data-diagram-desc="Documentation Structure"><img src="thumbnails/plantuml/dark/documentation-structure-dark-thumb.svg" alt="Documentation Structure thumbnail" width="80" /></div></td>
                        <td>Documentation Structure</td>
                        <td>Flowchart</td>
                        <td>Documentation organization</td>
                        <td><a href="../220-ela-documentation-style-guide.md">215-ela-documentation-style-guide.md</a></td>
                        <td><a href="mermaid/dark/documentation-structure-dark.md">documentation-structure-dark.md</a></td>
                        <td><a href="mermaid/light/documentation-structure-light.md">documentation-structure-light.md</a></td>
                        <td><a href="plantuml/dark/documentation-structure-dark.puml">documentation-structure-dark.puml</a></td>
                        <td><a href="plantuml/light/documentation-structure-light.puml">documentation-structure-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Documentation Workflow" data-diagram-type="Flowchart" data-diagram-desc="Documentation Workflow"><img src="thumbnails/plantuml/dark/documentation-workflow-dark-thumb.svg" alt="Documentation Workflow thumbnail" width="80" /></div></td>
                        <td>Documentation Workflow</td>
                        <td>Flowchart</td>
                        <td>Documentation creation workflow</td>
                        <td><a href="../220-ela-documentation-style-guide.md">215-ela-documentation-style-guide.md</a></td>
                        <td><a href="mermaid/dark/documentation-workflow-dark.md">documentation-workflow-dark.md</a></td>
                        <td><a href="mermaid/light/documentation-workflow-light.md">documentation-workflow-light.md</a></td>
                        <td><a href="plantuml/dark/documentation-workflow-dark.puml">documentation-workflow-dark.puml</a></td>
                        <td><a href="plantuml/light/documentation-workflow-light.puml">documentation-workflow-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Diagram Creation Process" data-diagram-type="Flowchart" data-diagram-desc="Diagram Creation Process"><img src="thumbnails/plantuml/dark/diagram-creation-process-dark-thumb.svg" alt="Diagram Creation Process thumbnail" width="80" /></div></td>
                        <td>Diagram Creation Process</td>
                        <td>Flowchart</td>
                        <td>Process for creating diagrams</td>
                        <td><a href="../220-ela-documentation-style-guide.md">215-ela-documentation-style-guide.md</a></td>
                        <td><a href="mermaid/dark/diagram-creation-process-dark.md">diagram-creation-process-dark.md</a></td>
                        <td><a href="mermaid/light/diagram-creation-process-light.md">diagram-creation-process-light.md</a></td>
                        <td><a href="plantuml/dark/diagram-creation-process-dark.puml">diagram-creation-process-dark.puml</a></td>
                        <td><a href="plantuml/light/diagram-creation-process-light.puml">diagram-creation-process-light.puml</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Documentation Components" data-diagram-type="Flowchart" data-diagram-desc="Documentation Components"><img src="thumbnails/plantuml/dark/documentation-components-dark-thumb.svg" alt="Documentation Components thumbnail" width="80" /></div></td>
                        <td>Documentation Components</td>
                        <td>Flowchart</td>
                        <td>Documentation components overview</td>
                        <td><a href="../220-ela-documentation-style-guide.md">215-ela-documentation-style-guide.md</a></td>
                        <td><a href="mermaid/dark/documentation-components-dark.md">documentation-components-dark.md</a></td>
                        <td><a href="mermaid/light/documentation-components-light.md">documentation-components-light.md</a></td>
                        <td><a href="plantuml/dark/documentation-components-dark.puml">documentation-components-dark.puml</a></td>
                        <td><a href="plantuml/light/documentation-components-light.puml">documentation-components-light.puml</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <h2 id="diagram-types">Diagram Types</h2>

        <p>This section organizes diagrams by their visual format to help you understand the different visualization techniques used throughout the documentation.</p>

        <details id="flowchart-diagrams">
            <summary><strong style="font-size: 1.2em;">Flowcharts</strong></summary>

            <p>Flowcharts visualize processes, workflows, and relationships between components. They're useful for understanding how different parts of the system interact.</p>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col">Diagram Name</th>
                        <th scope="col">Description</th>
                        <th scope="col">Source Document</th>
                        <th scope="col" colspan="2">Mermaid Files</th>
                        <th scope="col" colspan="2">PlantUML Files</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Architecture Overview" data-diagram-type="Flowchart" data-diagram-desc="Architecture Overview" data-diagram-dark="mermaid/dark/architecture-overview-dark.md" data-diagram-light="mermaid/light/architecture-overview-light.md"><img src="thumbnails/plantuml/dark/architecture-overview-dark-thumb.svg" alt="Architecture Overview thumbnail" width="80" /></div></td>
                        <td>Architecture Overview</td>
                        <td>High-level system architecture</td>
                        <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                        <td><a href="mermaid/dark/architecture-overview-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/architecture-overview-light.md">Light</a></td>
                        <td><a href="plantuml/dark/architecture-overview-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/architecture-overview-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Executive Summary Overview" data-diagram-type="Flowchart" data-diagram-desc="Executive Summary Overview" data-diagram-dark="mermaid/dark/executive-summary-overview-dark.md" data-diagram-light="mermaid/light/executive-summary-overview-light.md"><img src="thumbnails/plantuml/dark/executive-summary-overview-dark-thumb.svg" alt="Executive Summary Overview thumbnail" width="80" /></div></td>
                        <td>Executive Summary Overview</td>
                        <td>Key features overview</td>
                        <td><a href="../005-ela-executive-summary.md">Executive Summary</a></td>
                        <td><a href="mermaid/dark/executive-summary-overview-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/executive-summary-overview-light.md">Light</a></td>
                        <td><a href="plantuml/dark/executive-summary-overview-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/executive-summary-overview-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="CQRS Flow" data-diagram-type="" data-diagram-desc="CQRS Flow"><img src="thumbnails/mermaid/light/cqrs-flow-light-thumb.svg" alt="CQRS Flow thumbnail" width="80" /></div></td>
                        <td>CQRS Flow</td>
                        <td>Command Query Responsibility Segregation</td>
                        <td><a href="../100-implementation-plan/100-060-cqrs-configuration.md">CQRS Configuration</a></td>
                        <td><a href="mermaid/dark/cqrs-flow-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/cqrs-flow-light.md">Light</a></td>
                        <td><a href="plantuml/dark/cqrs-flow-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/cqrs-flow-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Event Sourcing Flow" data-diagram-type="" data-diagram-desc="Event Sourcing Flow"><img src="thumbnails/plantuml/dark/event-sourcing-flow-dark-thumb.svg" alt="Event Sourcing Flow thumbnail" width="80" /></div></td>
                        <td>Event Sourcing Flow</td>
                        <td>Event sourcing implementation</td>
                        <td><a href="../100-implementation-plan/100-350-event-sourcing/050-implementation.md">Event Sourcing Implementation</a></td>
                        <td><a href="mermaid/dark/event-sourcing-flow-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/event-sourcing-flow-light.md">Light</a></td>
                        <td><a href="plantuml/dark/event-sourcing-flow-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/event-sourcing-flow-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="User Aggregate States" data-diagram-type="State" data-diagram-desc="User state transitions" data-diagram-dark="mermaid/dark/user-aggregate-states-dark.md" data-diagram-light="mermaid/light/user-aggregate-states-light.md"><img src="thumbnails/mermaid/dark/user-aggregate-states-dark-thumb.svg" alt="User Aggregate States thumbnail" width="80" /></div></td>
                        <td>User Aggregate States</td>
                        <td>User state transitions</td>
                        <td><a href="../100-implementation-plan/100-350-event-sourcing/020-010-user-aggregate.md">User Aggregate</a></td>
                        <td><a href="mermaid/dark/user-aggregate-states-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/user-aggregate-states-light.md">Light</a></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Comment State Transitions" data-diagram-type="State" data-diagram-desc="Comment state transitions" data-diagram-dark="mermaid/dark/comment-state-transitions-dark.md" data-diagram-light="mermaid/light/comment-state-transitions-light.md"><img src="thumbnails/mermaid/dark/comment-state-transitions-dark-thumb.svg" alt="Comment State Transitions thumbnail" width="80" /></div></td>
                        <td>Comment State Transitions</td>
                        <td>Comment state transitions</td>
                        <td><a href="../100-implementation-plan/100-350-event-sourcing/100-comments-reactions.md">Comments and Reactions</a></td>
                        <td><a href="mermaid/dark/comment-state-transitions-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/comment-state-transitions-light.md">Light</a></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Comment and Reaction Structure" data-diagram-type="Class" data-diagram-desc="Comment and reaction structure" data-diagram-dark="mermaid/dark/comment-reaction-structure-dark.md" data-diagram-light="mermaid/light/comment-reaction-structure-light.md"><img src="thumbnails/mermaid/dark/comment-reaction-structure-dark-thumb.svg" alt="Comment and Reaction Structure thumbnail" width="80" /></div></td>
                        <td>Comment and Reaction Structure</td>
                        <td>Comment and reaction structure</td>
                        <td><a href="../100-implementation-plan/100-350-event-sourcing/100-comments-reactions.md">Comments and Reactions</a></td>
                        <td><a href="mermaid/dark/comment-reaction-structure-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/comment-reaction-structure-light.md">Light</a></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Real-time Architecture" data-diagram-type="Architecture" data-diagram-desc="Real-time architecture" data-diagram-dark="mermaid/dark/realtime-architecture-dark.md" data-diagram-light="mermaid/light/realtime-architecture-light.md"><img src="thumbnails/mermaid/dark/realtime-architecture-dark-thumb.svg" alt="Real-time Architecture thumbnail" width="80" /></div></td>
                        <td>Real-time Architecture</td>
                        <td>Real-time architecture</td>
                        <td><a href="../100-implementation-plan/100-350-event-sourcing/110-real-time.md">Real-time Implementation</a></td>
                        <td><a href="mermaid/dark/realtime-architecture-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/realtime-architecture-light.md">Light</a></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Documentation Structure" data-diagram-type="" data-diagram-desc="Documentation Structure"><img src="thumbnails/plantuml/dark/documentation-structure-dark-thumb.svg" alt="Documentation Structure thumbnail" width="80" /></div></td>
                        <td>Documentation Structure</td>
                        <td>Documentation organization</td>
                        <td><a href="../220-ela-documentation-style-guide.md">Documentation Style Guide</a></td>
                        <td><a href="mermaid/dark/documentation-structure-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/documentation-structure-light.md">Light</a></td>
                        <td><a href="plantuml/dark/documentation-structure-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/documentation-structure-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Filament Admin Panel" data-diagram-type="" data-diagram-desc="Filament Admin Panel"><img src="thumbnails/plantuml/dark/filament-admin-panel-dark-thumb.svg" alt="Filament Admin Panel thumbnail" width="80" /></div></td>
                        <td>Filament Admin Panel</td>
                        <td>Filament admin panel structure</td>
                        <td><a href="../100-implementation-plan/030-core-components/040-filament-configuration.md">Filament Configuration</a></td>
                        <td><a href="mermaid/dark/filament-admin-panel-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/filament-admin-panel-light.md">Light</a></td>
                        <td><a href="plantuml/dark/filament-admin-panel-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/filament-admin-panel-light.puml">Light</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details id="entity-relationship-diagrams">
            <summary><strong style="font-size: 1.2em;">Entity Relationship Diagrams (ERD)</strong></summary>

            <p>Entity Relationship Diagrams visualize database structure and relationships between entities. They're essential for understanding the data model of the application.</p>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col">Diagram Name</th>
                        <th scope="col">Description</th>
                        <th scope="col">Source Document</th>
                        <th scope="col" colspan="2">Mermaid Files</th>
                        <th scope="col" colspan="2">PlantUML Files</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="ERD Overview" data-diagram-type="ERD" data-diagram-desc="Entity Relationship Diagram" data-diagram-dark="mermaid/dark/erd-overview-dark.md" data-diagram-light="mermaid/light/erd-overview-light.md"><img src="thumbnails/mermaid/light/erd-overview-light-thumb.svg" alt="ERD Overview thumbnail" width="80" /></div></td>
                        <td>ERD Overview</td>
                        <td>Entity Relationship Diagram (TAD version)</td>
                        <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                        <td><a href="mermaid/dark/erd-overview-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/erd-overview-light.md">Light</a></td>
                        <td><a href="plantuml/dark/erd-overview-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/erd-overview-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="ERD Overview (Enhanced)" data-diagram-type="ERD" data-diagram-desc="Entity Relationship Diagram (Enhanced version)" data-diagram-dark="mermaid/dark/erd-overview-dark.md" data-diagram-light="mermaid/light/erd-overview-light.md"><img src="thumbnails/mermaid/light/erd-overview-light-thumb.svg" alt="ERD Overview (Enhanced) thumbnail" width="80" /></div></td>
                        <td>ERD Overview (Enhanced)</td>
                        <td>Entity Relationship Diagram (Enhanced version)</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">Enhanced Diagrams</a></td>
                        <td><a href="mermaid/dark/erd-overview-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/erd-overview-light.md">Light</a></td>
                        <td><a href="plantuml/dark/erd-overview-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/erd-overview-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="TAD Database Schema" data-diagram-type="ERD" data-diagram-desc="Database schema diagram" data-diagram-dark="mermaid/dark/tad-database-schema-dark.md" data-diagram-light="mermaid/light/tad-database-schema-light.md"><img src="thumbnails/mermaid/light/tad-database-schema-light-thumb.svg" alt="TAD Database Schema thumbnail" width="80" /></div></td>
                        <td>TAD Database Schema</td>
                        <td>Database schema diagram</td>
                        <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                        <td><a href="mermaid/dark/tad-database-schema-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/tad-database-schema-light.md">Light</a></td>
                        <td><a href="plantuml/dark/tad-database-schema-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/tad-database-schema-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Database Schema Implementation" data-diagram-type="" data-diagram-desc="Database Schema Implementation"><img src="thumbnails/plantuml/dark/database-schema-implementation-dark-thumb.svg" alt="Database Schema Implementation thumbnail" width="80" /></div></td>
                        <td>Database Schema Implementation</td>
                        <td>Database schema implementation details</td>
                        <td><a href="../100-implementation-plan/100-110-database-migrations.md">Database Migrations</a></td>
                        <td><a href="mermaid/dark/database-schema-implementation-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/database-schema-implementation-light.md">Light</a></td>
                        <td><a href="plantuml/dark/database-schema-implementation-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/database-schema-implementation-light.puml">Light</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details id="sequence-diagrams">
            <summary><strong style="font-size: 1.2em;">Sequence Diagrams</strong></summary>

            <p>Sequence Diagrams show interactions between components over time. They're particularly useful for understanding the flow of operations and communication between different parts of the system.</p>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col">Diagram Name</th>
                        <th scope="col">Description</th>
                        <th scope="col">Source Document</th>
                        <th scope="col" colspan="2">Mermaid Files</th>
                        <th scope="col" colspan="2">PlantUML Files</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Authentication Flow" data-diagram-type="Sequence" data-diagram-desc="User authentication process" data-diagram-dark="mermaid/dark/authentication-flow-dark.md" data-diagram-light="mermaid/light/authentication-flow-light.md"><img src="thumbnails/plantuml/dark/authentication-flow-dark-thumb.svg" alt="Authentication Flow thumbnail" width="80" /></div></td>
                        <td>Authentication Flow</td>
                        <td>User authentication process</td>
                        <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                        <td><a href="mermaid/dark/authentication-flow-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/authentication-flow-light.md">Light</a></td>
                        <td><a href="plantuml/dark/authentication-flow-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/authentication-flow-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="User Registration Sequence" data-diagram-type="" data-diagram-desc="User Registration Sequence"><img src="thumbnails/plantuml/dark/user-registration-sequence-dark-thumb.svg" alt="User Registration Sequence thumbnail" width="80" /></div></td>
                        <td>User Registration Sequence</td>
                        <td>User registration process</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">Enhanced Diagrams</a></td>
                        <td><a href="mermaid/dark/user-registration-sequence-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/user-registration-sequence-light.md">Light</a></td>
                        <td><a href="plantuml/dark/user-registration-sequence-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/user-registration-sequence-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Team Creation Sequence" data-diagram-type="" data-diagram-desc="Team Creation Sequence"><img src="thumbnails/plantuml/dark/team-creation-sequence-dark-thumb.svg" alt="Team Creation Sequence thumbnail" width="80" /></div></td>
                        <td>Team Creation Sequence</td>
                        <td>Team creation process</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">Enhanced Diagrams</a></td>
                        <td><a href="mermaid/dark/team-creation-sequence-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/team-creation-sequence-light.md">Light</a></td>
                        <td><a href="plantuml/dark/team-creation-sequence-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/team-creation-sequence-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Post Creation Sequence" data-diagram-type="" data-diagram-desc="Post Creation Sequence"><img src="thumbnails/plantuml/dark/post-creation-sequence-dark-thumb.svg" alt="Post Creation Sequence thumbnail" width="80" /></div></td>
                        <td>Post Creation Sequence</td>
                        <td>Post creation process</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">Enhanced Diagrams</a></td>
                        <td><a href="mermaid/dark/post-creation-sequence-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/post-creation-sequence-light.md">Light</a></td>
                        <td><a href="plantuml/dark/post-creation-sequence-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/post-creation-sequence-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Migration Sequence" data-diagram-type="" data-diagram-desc="Migration Sequence"><img src="thumbnails/plantuml/dark/migration-sequence-dark-thumb.svg" alt="Migration Sequence thumbnail" width="80" /></div></td>
                        <td>Migration Sequence</td>
                        <td>Database migration process</td>
                        <td><a href="../100-implementation-plan/100-110-database-migrations.md">Database Migrations</a></td>
                        <td><a href="mermaid/dark/migration-sequence-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/migration-sequence-light.md">Light</a></td>
                        <td><a href="plantuml/dark/migration-sequence-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/migration-sequence-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="TAD Request Lifecycle" data-diagram-type="" data-diagram-desc="TAD Request Lifecycle"><img src="thumbnails/mermaid/light/tad-request-lifecycle-light-thumb.svg" alt="TAD Request Lifecycle thumbnail" width="80" /></div></td>
                        <td>TAD Request Lifecycle</td>
                        <td>Request lifecycle diagram</td>
                        <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                        <td><a href="mermaid/dark/tad-request-lifecycle-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/tad-request-lifecycle-light.md">Light</a></td>
                        <td><a href="plantuml/dark/tad-request-lifecycle-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/tad-request-lifecycle-light.puml">Light</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details id="class-diagrams">
            <summary><strong style="font-size: 1.2em;">Class Diagrams</strong></summary>

            <p>Class Diagrams show the structure of the application's classes, their attributes, methods, and relationships. They're essential for understanding the object-oriented design of the system.</p>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col">Diagram Name</th>
                        <th scope="col">Description</th>
                        <th scope="col">Source Document</th>
                        <th scope="col" colspan="2">Mermaid Files</th>
                        <th scope="col" colspan="2">PlantUML Files</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Class Diagram (Overview)" data-diagram-type="Class" data-diagram-desc="Overview of class structure and relationships" data-diagram-dark="mermaid/dark/class-diagram-dark.md" data-diagram-light="mermaid/light/class-diagram-light.md"><img src="thumbnails/mermaid/light/class-diagram-light-thumb.svg" alt="Class Diagram (Overview) thumbnail" width="80" /></div></td>
                        <td>Class Diagram (Overview)</td>
                        <td>Simplified class structure showing key relationships</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">Enhanced Diagrams</a></td>
                        <td><a href="mermaid/dark/class-diagram-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/class-diagram-light.md">Light</a></td>
                        <td><a href="plantuml/dark/class-diagram-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/class-diagram-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Class Diagram (Detailed)" data-diagram-type="Class" data-diagram-desc="Comprehensive class structure with all attributes and methods" data-diagram-dark="mermaid/dark/class-diagram-detailed-dark.md" data-diagram-light="mermaid/light/class-diagram-detailed-light.md"><img src="thumbnails/mermaid/light/class-diagram-detailed-light-thumb.svg" alt="Class Diagram (Detailed) thumbnail" width="80" /></div></td>
                        <td>Class Diagram (Detailed)</td>
                        <td>Comprehensive class structure with all attributes and methods</td>
                        <td><a href="../100-implementation-plan/100-610-enhanced-diagrams.md">Enhanced Diagrams</a></td>
                        <td><a href="mermaid/dark/class-diagram-detailed-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/class-diagram-detailed-light.md">Light</a></td>
                        <td><a href="plantuml/dark/class-diagram-detailed-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/class-diagram-detailed-light.puml">Light</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details id="state-diagrams">
            <summary><strong style="font-size: 1.2em;">State Diagrams</strong></summary>

            <p>State Diagrams visualize the different states an object can be in and the transitions between those states. They're useful for understanding how objects change over time in response to events.</p>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col">Diagram Name</th>
                        <th scope="col">Description</th>
                        <th scope="col">Source Document</th>
                        <th scope="col" colspan="2">Mermaid Files</th>
                        <th scope="col" colspan="2">PlantUML Files</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Todo State Machine" data-diagram-type="" data-diagram-desc="Todo State Machine"><img src="thumbnails/plantuml/dark/todo-state-machine-dark-thumb.svg" alt="Todo State Machine thumbnail" width="80" /></div></td>
                        <td>Todo State Machine</td>
                        <td>Todo item state transitions</td>
                        <td><a href="../100-implementation-plan/100-370-status-implementation.md">Status Implementation</a></td>
                        <td><a href="mermaid/dark/todo-state-machine-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/todo-state-machine-light.md">Light</a></td>
                        <td><a href="plantuml/dark/todo-state-machine-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/todo-state-machine-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Status Implementation" data-diagram-type="" data-diagram-desc="Status Implementation"><img src="thumbnails/plantuml/dark/status-implementation-dark-thumb.svg" alt="Status Implementation thumbnail" width="80" /></div></td>
                        <td>Status Implementation</td>
                        <td>Status implementation details</td>
                        <td><a href="../100-implementation-plan/100-370-status-implementation.md">Status Implementation</a></td>
                        <td><a href="mermaid/dark/status-implementation-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/status-implementation-light.md">Light</a></td>
                        <td><a href="plantuml/dark/status-implementation-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/status-implementation-light.puml">Light</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details id="gantt-diagrams">
            <summary><strong style="font-size: 1.2em;">Gantt Charts</strong></summary>

            <p>Gantt Charts visualize project schedules and timelines. They show tasks, dependencies, and durations to help with project planning and tracking.</p>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col">Diagram Name</th>
                        <th scope="col">Description</th>
                        <th scope="col">Source Document</th>
                        <th scope="col" colspan="2">Mermaid Files</th>
                        <th scope="col" colspan="2">PlantUML Files</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Project Roadmap" data-diagram-type="Gantt" data-diagram-desc="Project roadmap timeline" data-diagram-dark="mermaid/dark/project-roadmap-dark.md" data-diagram-light="mermaid/light/project-roadmap-light.md"><img src="thumbnails/plantuml/dark/project-roadmap-dark-thumb.svg" alt="Project Roadmap thumbnail" width="80" /></div></td>
                        <td>Project Roadmap</td>
                        <td>Project roadmap timeline</td>
                        <td><a href="../020-ela-project-roadmap.md">Project Roadmap</a></td>
                        <td><a href="mermaid/dark/project-roadmap-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/project-roadmap-light.md">Light</a></td>
                        <td><a href="plantuml/dark/project-roadmap-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/project-roadmap-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Implementation Timeline" data-diagram-type="" data-diagram-desc="Implementation Timeline"><img src="thumbnails/plantuml/dark/implementation-timeline-dark-thumb.svg" alt="Implementation Timeline thumbnail" width="80" /></div></td>
                        <td>Implementation Timeline</td>
                        <td>Project implementation timeline</td>
                        <td><a href="../005-ela-executive-summary.md">Executive Summary</a></td>
                        <td><a href="mermaid/dark/implementation-timeline-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/implementation-timeline-light.md">Light</a></td>
                        <td><a href="plantuml/dark/implementation-timeline-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/implementation-timeline-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Resource Allocation Timeline" data-diagram-type="" data-diagram-desc="Resource Allocation Timeline"><img src="thumbnails/plantuml/dark/resource-allocation-timeline-dark-thumb.svg" alt="Resource Allocation Timeline thumbnail" width="80" /></div></td>
                        <td>Resource Allocation Timeline</td>
                        <td>Project resource timeline</td>
                        <td><a href="../005-ela-executive-summary.md">Executive Summary</a></td>
                        <td><a href="mermaid/dark/resource-allocation-timeline-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/resource-allocation-timeline-light.md">Light</a></td>
                        <td><a href="plantuml/dark/resource-allocation-timeline-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/resource-allocation-timeline-light.puml">Light</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <details id="deployment-diagrams">
            <summary><strong style="font-size: 1.2em;">Deployment Diagrams</strong></summary>

            <p>Deployment Diagrams show the physical deployment of artifacts on nodes. They're useful for understanding the system infrastructure and how components are distributed.</p>

            <table>
                <thead>
                    <tr>
<th scope="col">Thumbnail</th>
                        <th scope="col">Diagram Name</th>
                        <th scope="col">Description</th>
                        <th scope="col">Source Document</th>
                        <th scope="col" colspan="2">Mermaid Files</th>
                        <th scope="col" colspan="2">PlantUML Files</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="Deployment Architecture" data-diagram-type="" data-diagram-desc="Deployment Architecture"><img src="thumbnails/plantuml/dark/deployment-architecture-dark-thumb.svg" alt="Deployment Architecture thumbnail" width="80" /></div></td>
                        <td>Deployment Architecture</td>
                        <td>System deployment architecture</td>
                        <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                        <td><a href="mermaid/dark/deployment-architecture-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/deployment-architecture-light.md">Light</a></td>
                        <td><a href="plantuml/dark/deployment-architecture-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/deployment-architecture-light.puml">Light</a></td>
                    </tr>
                    <tr>
<td><div class="thumbnail-container" data-diagram-name="TAD Deployment" data-diagram-type="" data-diagram-desc="TAD Deployment"><img src="thumbnails/mermaid/light/tad-deployment-light-thumb.svg" alt="TAD Deployment thumbnail" width="80" /></div></td>
                        <td>TAD Deployment</td>
                        <td>Deployment diagram</td>
                        <td><a href="../030-ela-tad.md">Technical Architecture Document</a></td>
                        <td><a href="mermaid/dark/tad-deployment-dark.md">Dark</a></td>
                        <td><a href="mermaid/light/tad-deployment-light.md">Light</a></td>
                        <td><a href="plantuml/dark/tad-deployment-dark.puml">Dark</a></td>
                        <td><a href="plantuml/light/tad-deployment-light.puml">Light</a></td>
                    </tr>
                </tbody>
            </table>
        </details>

        <h2 id="search-tips">Search Tips</h2>

        <p>This section provides guidance on how to effectively search for diagrams in this index.</p>

        <h3 id="basic-search-strategies">Basic Search Strategies</h3>

        <ul>
            <li>
<strong>Search by Name</strong>: Enter part of the diagram name in the search box (e.g., "authentication", "erd", "flow").</li>
            <li>
<strong>Search by Type</strong>: Use the checkboxes to filter by diagram type (e.g., Flowcharts, ERDs, Sequence diagrams).</li>
            <li>
<strong>Search by Description</strong>: Enter keywords from the description (e.g., "database", "process", "implementation").</li>
            <li>
<strong>Search by Tags</strong>: Enter tag keywords (e.g., "auth", "database", "timeline").</li>
        </ul>

        <h3 id="advanced-search-techniques">Advanced Search Techniques</h3>

        <ul>
            <li>
<strong>Combine Filters</strong>: Use both the search box and type checkboxes to narrow down results.</li>
            <li>
<strong>Browse by Category</strong>: Use the collapsible sections to browse diagrams by project area or diagram type.</li>
            <li>
<strong>Check Source Documents</strong>: If you know which document contains the diagram you need, look for diagrams from that source.</li>
        </ul>

        <h3 id="if-you-cant-find-a-diagram">If You Can't Find a Diagram</h3>

        <ul>
            <li>
<strong>Try Different Keywords</strong>: Use synonyms or related terms.</li>
            <li>
<strong>Browse Categories</strong>: Look through the relevant category sections.</li>
            <li>
<strong>Check Source Documents</strong>: Go directly to the source document that might contain the diagram.</li>
            <li>
<strong>Request a New Diagram</strong>: If you need a diagram that doesn't exist, follow the <a href="#diagram-request-process">Diagram Request Process</a>.</li>
        </ul>

        <h2 id="conclusion">Conclusion</h2>

        <p>This diagram index serves as a comprehensive resource for accessing all visual representations used throughout the Enhanced Laravel Application documentation. By organizing diagrams by project area, diagram type, and providing powerful search capabilities, this index makes it easy to find the visual resources you need to understand the project.</p>

        <p>Remember that diagrams are available in both Mermaid and PlantUML formats, with dark and light mode variants to accommodate different viewing preferences. If you need a diagram that doesn't exist, follow the diagram request process to suggest new visual resources.</p>

        <h2 id="diagram-request-process">Diagram Request Process</h2>

        <p>If you need a diagram that doesn't exist in the current documentation, follow these steps to request a new diagram:</p>

        <h3 id="1-check-existing-diagrams">1. Check Existing Diagrams</h3>

        <p>Before requesting a new diagram, thoroughly search this index to ensure the diagram doesn't already exist. Try different search terms and browse relevant categories.</p>

        <h3 id="2-prepare-your-request">2. Prepare Your Request</h3>

        <p>If you confirm the diagram doesn't exist, prepare a request that includes:</p>

        <ul>
            <li>The type of diagram needed (flowchart, ERD, sequence, etc.)</li>
            <li>A clear description of what the diagram should show</li>
            <li>The purpose of the diagram and how it will be used</li>
            <li>Any specific elements that must be included</li>
            <li>The document where the diagram will be referenced</li>
        </ul>

        <h3 id="3-submit-your-request">3. Submit Your Request</h3>

        <p>Submit your diagram request through the project's issue tracking system, using the "Diagram Request" template if available.</p>

        <h3 id="4-review-process">4. Review Process</h3>

        <p>Your request will be reviewed by the documentation team, who will determine if the diagram is needed and prioritize its creation. You may be asked to provide additional information or clarification.</p>

        <h2 id="diagram-tools-and-resources">Diagram Tools and Resources</h2>

        <p>This section provides information about tools and resources for working with the diagrams in this index.</p>

        <h3 id="mermaid-tools">Mermaid Tools</h3>

        <ul>
            <li>
<a href="https://mermaid.live/" target="_blank">Mermaid Live Editor</a> - Online editor for creating and editing Mermaid diagrams</li>
            <li>
<a href="https://github.com/mermaid-js/mermaid-cli" target="_blank">Mermaid CLI</a> - Command-line tool for generating Mermaid diagrams</li>
            <li>
<a href="https://github.com/mermaid-js/mermaid" target="_blank">Mermaid GitHub Repository</a> - Source code and documentation for Mermaid</li>
            <li>
<a href="https://mermaid.js.org/intro/" target="_blank">Mermaid Documentation</a> - Official documentation for Mermaid syntax</li>
        </ul>

        <h3 id="plantuml-tools">PlantUML Tools</h3>

        <ul>
            <li>
<a href="https://www.plantuml.com/plantuml/uml/" target="_blank">PlantUML Web Server</a> - Online editor for creating and editing PlantUML diagrams</li>
            <li>
<a href="https://plantuml.com/command-line" target="_blank">PlantUML Command Line</a> - Command-line tool for generating PlantUML diagrams</li>
            <li>
<a href="https://github.com/plantuml/plantuml" target="_blank">PlantUML GitHub Repository</a> - Source code and documentation for PlantUML</li>
            <li>
<a href="https://plantuml.com/" target="_blank">PlantUML Documentation</a> - Official documentation for PlantUML syntax</li>
        </ul>

        <h3 id="other-useful-resources">Other Useful Resources</h3>

        <ul>
            <li>
<a href="https://kroki.io/" target="_blank">Kroki</a> - Unified API for creating diagrams from textual descriptions</li>
            <li>
<a href="https://github.com/pmsipilot/docker-compose-viz" target="_blank">Docker Compose Viz</a> - Tool for visualizing Docker Compose files</li>
            <li>
<a href="https://graphviz.org/" target="_blank">Graphviz</a> - Open source graph visualization software</li>
            <li>
<a href="https://draw.io/" target="_blank">draw.io</a> - Free online diagram software for making flowcharts, process diagrams, org charts, UML, ER and network diagrams</li>
        </ul>

        <h2 id="diagram-templates">Diagram Templates</h2>

        <p>This section provides templates for creating new diagrams. Use these templates to ensure consistency across the project.</p>

        <h3 id="flowchart-template">Flowchart Template</h3>

        <p>Use this template for creating flowcharts in Mermaid:</p>

        <pre><code>```mermaid
flowchart TD
    %% Define nodes
    A[Start] --&gt; B{Decision}
    B --&gt;|Yes| C[Process 1]
    B --&gt;|No| D[Process 2]
    C --&gt; E[End]
    D --&gt; E

    %% Styling
    classDef default fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef highlight fill:#ffffcc,stroke:#333,stroke-width:2px;
    class A,E highlight;
```</code></pre>

        <p>Use this template for creating flowcharts in PlantUML:</p>

        <pre><code>@startuml
!theme plain

start
if (Decision?) then (yes)
  :Process 1;
else (no)
  :Process 2;
endif
end

@enduml</code></pre>

        <h3 id="sequence-diagram-template">Sequence Diagram Template</h3>

        <p>Use this template for creating sequence diagrams in Mermaid:</p>

        <pre><code>```mermaid
sequenceDiagram
    %% Define participants
    participant User
    participant System
    participant Database

    %% Define interactions
    User-&gt;&gt;System: Request Data
    System-&gt;&gt;Database: Query Data
    Database--&gt;&gt;System: Return Data
    System--&gt;&gt;User: Display Data

    %% Add notes
    Note over System,Database: Secure connection
```</code></pre>

        <p>Use this template for creating sequence diagrams in PlantUML:</p>

        <pre><code>@startuml
!theme plain

actor User
participant System
database Database

User -&gt; System: Request Data
System -&gt; Database: Query Data
Database --&gt; System: Return Data
System --&gt; User: Display Data

note over System, Database: Secure connection

@enduml</code></pre>

        <h3 id="erd-template">ERD Template</h3>

        <p>Use this template for creating ERD diagrams in Mermaid:</p>

        <pre><code>```mermaid
erDiagram
    %% Define entities and relationships
    CUSTOMER ||--o{ ORDER : places
    ORDER ||--|{ LINE_ITEM : contains
    CUSTOMER }|..|{ PRODUCT : views

    %% Define attributes
    CUSTOMER {
        string id PK
        string name
        string email
    }
    ORDER {
        string id PK
        string customer_id FK
        date created_at
    }
    LINE_ITEM {
        string id PK
        string order_id FK
        string product_id FK
        int quantity
    }
    PRODUCT {
        string id PK
        string name
        float price
    }
```</code></pre>

        <p>Use this template for creating ERD diagrams in PlantUML:</p>

        <pre><code>@startuml
!theme plain

entity Customer {
    * id : string &gt;
    --
    * name : string
    * email : string
}

entity Order {
    * id : string &gt;
    --
    * customer_id : string &gt;
    * created_at : date
}

entity LineItem {
    * id : string &gt;
    --
    * order_id : string &gt;
    * product_id : string &gt;
    * quantity : int
}

entity Product {
    * id : string &gt;
    --
    * name : string
    * price : float
}

Customer ||--o{ Order
Order ||--|{ LineItem
Customer }|..o{ Product : views
LineItem }|--|| Product

@enduml</code></pre>

        <h2 id="feature-based-diagram-index">Feature-Based Diagram Index</h2>

        <p>This section organizes diagrams by feature area to help you find diagrams related to specific features of the application.</p>

        <h3 id="authentication--authorization">Authentication &amp; Authorization</h3>

        <table>
            <thead>
                <tr>
<th scope="col">Thumbnail</th>
                    <th scope="col">Diagram Name</th>
                    <th scope="col">Type</th>
                    <th scope="col">Description</th>
                    <th scope="col" colspan="2">Links</th>
                </tr>
            </thead>
            <tbody>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Authentication Flow" data-diagram-type="Sequence" data-diagram-desc="User authentication process" data-diagram-dark="mermaid/dark/authentication-flow-dark.md" data-diagram-light="mermaid/light/authentication-flow-light.md"><img src="thumbnails/plantuml/dark/authentication-flow-dark-thumb.svg" alt="Authentication Flow thumbnail" width="80" /></div></td>
                    <td>Authentication Flow</td>
                    <td>Sequence</td>
                    <td>User authentication process</td>
                    <td><a href="mermaid/dark/authentication-flow-dark.md">Mermaid</a></td>
                    <td><a href="plantuml/dark/authentication-flow-dark.puml">PlantUML</a></td>
                </tr>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="User Registration Sequence" data-diagram-type="Sequence" data-diagram-desc="User Registration Sequence"><img src="thumbnails/plantuml/dark/user-registration-sequence-dark-thumb.svg" alt="User Registration Sequence thumbnail" width="80" /></div></td>
                    <td>User Registration Sequence</td>
                    <td>Sequence</td>
                    <td>User registration process</td>
                    <td><a href="mermaid/dark/user-registration-sequence-dark.md">Mermaid</a></td>
                    <td><a href="plantuml/dark/user-registration-sequence-dark.puml">PlantUML</a></td>
                </tr>
            </tbody>
        </table>

        <h3 id="user-management">User Management</h3>

        <table>
            <thead>
                <tr>
<th scope="col">Thumbnail</th>
                    <th scope="col">Diagram Name</th>
                    <th scope="col">Type</th>
                    <th scope="col">Description</th>
                    <th scope="col" colspan="2">Links</th>
                </tr>
            </thead>
            <tbody>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="User Registration Sequence" data-diagram-type="Sequence" data-diagram-desc="User Registration Sequence"><img src="thumbnails/plantuml/dark/user-registration-sequence-dark-thumb.svg" alt="User Registration Sequence thumbnail" width="80" /></div></td>
                    <td>User Registration Sequence</td>
                    <td>Sequence</td>
                    <td>User registration process</td>
                    <td><a href="mermaid/dark/user-registration-sequence-dark.md">Mermaid</a></td>
                    <td><a href="plantuml/dark/user-registration-sequence-dark.puml">PlantUML</a></td>
                </tr>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="User Entity Relationships" data-diagram-type="ERD" data-diagram-desc="User Entity Relationships"><img src="thumbnails/plantuml/dark/user-entity-relationships-dark-thumb.svg" alt="User Entity Relationships thumbnail" width="80" /></div></td>
                    <td>User Entity Relationships</td>
                    <td>ERD</td>
                    <td>User database relationships</td>
                    <td><a href="mermaid/dark/user-entity-relationships-dark.md">Mermaid</a></td>
                    <td><a href="plantuml/dark/user-entity-relationships-dark.puml">PlantUML</a></td>
                </tr>
            </tbody>
        </table>

        <h3 id="team-management">Team Management</h3>

        <table>
            <thead>
                <tr>
<th scope="col">Thumbnail</th>
                    <th scope="col">Diagram Name</th>
                    <th scope="col">Type</th>
                    <th scope="col">Description</th>
                    <th scope="col" colspan="2">Links</th>
                </tr>
            </thead>
            <tbody>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Team Creation Sequence" data-diagram-type="Sequence" data-diagram-desc="Team Creation Sequence"><img src="thumbnails/plantuml/dark/team-creation-sequence-dark-thumb.svg" alt="Team Creation Sequence thumbnail" width="80" /></div></td>
                    <td>Team Creation Sequence</td>
                    <td>Sequence</td>
                    <td>Team creation process</td>
                    <td><a href="mermaid/dark/team-creation-sequence-dark.md">Mermaid</a></td>
                    <td><a href="plantuml/dark/team-creation-sequence-dark.puml">PlantUML</a></td>
                </tr>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Team Entity Relationships" data-diagram-type="ERD" data-diagram-desc="Team Entity Relationships"><img src="thumbnails/plantuml/dark/team-entity-relationships-dark-thumb.svg" alt="Team Entity Relationships thumbnail" width="80" /></div></td>
                    <td>Team Entity Relationships</td>
                    <td>ERD</td>
                    <td>Team database relationships</td>
                    <td><a href="mermaid/dark/team-entity-relationships-dark.md">Mermaid</a></td>
                    <td><a href="plantuml/dark/team-entity-relationships-dark.puml">PlantUML</a></td>
                </tr>
            </tbody>
        </table>

        <h3 id="content-management">Content Management</h3>

        <table>
            <thead>
                <tr>
<th scope="col">Thumbnail</th>
                    <th scope="col">Diagram Name</th>
                    <th scope="col">Type</th>
                    <th scope="col">Description</th>
                    <th scope="col" colspan="2">Links</th>
                </tr>
            </thead>
            <tbody>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Post Creation Sequence" data-diagram-type="Sequence" data-diagram-desc="Post Creation Sequence"><img src="thumbnails/plantuml/dark/post-creation-sequence-dark-thumb.svg" alt="Post Creation Sequence thumbnail" width="80" /></div></td>
                    <td>Post Creation Sequence</td>
                    <td>Sequence</td>
                    <td>Post creation process</td>
                    <td><a href="mermaid/dark/post-creation-sequence-dark.md">Mermaid</a></td>
                    <td><a href="plantuml/dark/post-creation-sequence-dark.puml">PlantUML</a></td>
                </tr>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Content Entity Relationships" data-diagram-type="ERD" data-diagram-desc="Content Entity Relationships"><img src="thumbnails/plantuml/dark/content-entity-relationships-dark-thumb.svg" alt="Content Entity Relationships thumbnail" width="80" /></div></td>
                    <td>Content Entity Relationships</td>
                    <td>ERD</td>
                    <td>Content database relationships</td>
                    <td><a href="mermaid/dark/content-entity-relationships-dark.md">Mermaid</a></td>
                    <td><a href="plantuml/dark/content-entity-relationships-dark.puml">PlantUML</a></td>
                </tr>
            </tbody>
        </table>

        <h2 id="diagram-tags">Diagram Tags</h2>

        <p>This section provides information about the tags used to categorize diagrams in this index.</p>

        <h3 id="functional-tags">Functional Tags</h3>

        <ul>
            <li>
<code>auth</code> - Authentication and authorization</li>
            <li>
<code>user</code> - User management</li>
            <li>
<code>team</code> - Team management</li>
            <li>
<code>content</code> - Content management</li>
            <li>
<code>notification</code> - Notifications</li>
            <li>
<code>search</code> - Search and discovery</li>
            <li>
<code>analytics</code> - Analytics and reporting</li>
        </ul>

        <h3 id="technical-tags">Technical Tags</h3>

        <ul>
            <li>
<code>database</code> - Database-related diagrams</li>
            <li>
<code>architecture</code> - Architecture diagrams</li>
            <li>
<code>deployment</code> - Deployment diagrams</li>
            <li>
<code>api</code> - API-related diagrams</li>
            <li>
<code>security</code> - Security-related diagrams</li>
            <li>
<code>performance</code> - Performance-related diagrams</li>
        </ul>

        <h3 id="process-tags">Process Tags</h3>

        <ul>
            <li>
<code>workflow</code> - Workflow diagrams</li>
            <li>
<code>sequence</code> - Sequence diagrams</li>
            <li>
<code>state</code> - State diagrams</li>
            <li>
<code>timeline</code> - Timeline diagrams</li>
            <li>
<code>planning</code> - Planning diagrams</li>
        </ul>

        <h2 id="diagram-statistics">Diagram Statistics</h2>

        <p>This section provides statistics about the diagrams in this index.</p>

        <div class="stats-dashboard">
            <div class="stats-card">
                <h3>Diagrams by Type</h3>
                <div class="chart-container">
                    <canvas id="diagramTypeChart"></canvas>
                </div>
            </div>
            <div class="stats-card">
                <h3>Most Referenced Diagrams</h3>
                <div class="chart-container">
                    <canvas id="referencedDiagramsChart"></canvas>
                </div>
            </div>
        </div>

        <h3 id="diagrams-by-type">Diagrams by Type</h3>

        <table>
            <thead>
                <tr>
                    <th scope="col">Diagram Type</th>
                    <th scope="col">Count</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Flowchart</td>
                    <td>15</td>
                </tr>
                <tr>
                    <td>ERD</td>
                    <td>8</td>
                </tr>
                <tr>
                    <td>Sequence</td>
                    <td>12</td>
                </tr>
                <tr>
                    <td>Class</td>
                    <td>4</td>
                </tr>
                <tr>
                    <td>State</td>
                    <td>3</td>
                </tr>
                <tr>
                    <td>Gantt</td>
                    <td>5</td>
                </tr>
                <tr>
                    <td>Deployment</td>
                    <td>3</td>
                </tr>
                <tr>
                    <td>Total</td>
                    <td>50</td>
                </tr>
            </tbody>
        </table>

        <h3 id="diagrams-by-feature-area">Diagrams by Feature Area</h3>

        <table>
            <thead>
                <tr>
                    <th scope="col">Feature Area</th>
                    <th scope="col">Count</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Authentication &amp; Authorization</td>
                    <td>5</td>
                </tr>
                <tr>
                    <td>User Management</td>
                    <td>7</td>
                </tr>
                <tr>
                    <td>Team Management</td>
                    <td>6</td>
                </tr>
                <tr>
                    <td>Content Management</td>
                    <td>8</td>
                </tr>
                <tr>
                    <td>Notifications</td>
                    <td>3</td>
                </tr>
                <tr>
                    <td>Search &amp; Discovery</td>
                    <td>2</td>
                </tr>
                <tr>
                    <td>Analytics &amp; Reporting</td>
                    <td>4</td>
                </tr>
            </tbody>
        </table>

        <h3 id="most-referenced-diagrams">Most Referenced Diagrams</h3>

        <table>
            <thead>
                <tr>
<th scope="col">Thumbnail</th>
                    <th scope="col">Diagram Name</th>
                    <th scope="col">Type</th>
                    <th scope="col">References</th>
                </tr>
            </thead>
            <tbody>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Architecture Overview" data-diagram-type="Flowchart" data-diagram-desc="Architecture Overview" data-diagram-dark="mermaid/dark/architecture-overview-dark.md" data-diagram-light="mermaid/light/architecture-overview-light.md"><img src="thumbnails/plantuml/dark/architecture-overview-dark-thumb.svg" alt="Architecture Overview thumbnail" width="80" /></div></td>
                    <td>Architecture Overview</td>
                    <td>Flowchart</td>
                    <td>12</td>
                </tr>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="ERD Overview" data-diagram-type="ERD" data-diagram-desc="Entity Relationship Diagram" data-diagram-dark="mermaid/dark/erd-overview-dark.md" data-diagram-light="mermaid/light/erd-overview-light.md"><img src="thumbnails/mermaid/light/erd-overview-light-thumb.svg" alt="ERD Overview thumbnail" width="80" /></div></td>
                    <td>ERD Overview</td>
                    <td>ERD</td>
                    <td>10</td>
                </tr>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Authentication Flow" data-diagram-type="Sequence" data-diagram-desc="User authentication process" data-diagram-dark="mermaid/dark/authentication-flow-dark.md" data-diagram-light="mermaid/light/authentication-flow-light.md"><img src="thumbnails/plantuml/dark/authentication-flow-dark-thumb.svg" alt="Authentication Flow thumbnail" width="80" /></div></td>
                    <td>Authentication Flow</td>
                    <td>Sequence</td>
                    <td>8</td>
                </tr>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Class Diagram (Overview)" data-diagram-type="Class" data-diagram-desc="Simplified class structure showing key relationships" data-diagram-dark="mermaid/dark/class-diagram-dark.md" data-diagram-light="mermaid/light/class-diagram-light.md"><img src="thumbnails/mermaid/light/class-diagram-light-thumb.svg" alt="Class Diagram (Overview) thumbnail" width="80" /></div></td>
                    <td>Class Diagram (Overview)</td>
                    <td>Class</td>
                    <td>7</td>
                </tr>
                <tr>
<td><div class="thumbnail-container" data-diagram-name="Project Roadmap" data-diagram-type="Gantt" data-diagram-desc="Project roadmap timeline" data-diagram-dark="mermaid/dark/project-roadmap-dark.md" data-diagram-light="mermaid/light/project-roadmap-light.md"><img src="thumbnails/plantuml/dark/project-roadmap-dark-thumb.svg" alt="Project Roadmap thumbnail" width="80" /></div></td>
                    <td>Project Roadmap</td>
                    <td>Gantt</td>
                    <td>6</td>
                </tr>
            </tbody>
        </table>

        <h2 id="diagram-relationships">Diagram Relationships</h2>

        <p>This section shows how diagrams relate to each other.</p>

        <div id="relationship-visualization"></div>

        <h3 id="diagram-dependency-map">Diagram Dependency Map</h3>

        <p>The diagram dependency map shows how diagrams depend on each other. For example, a detailed diagram might depend on a higher-level overview diagram.</p>

        <p>Key relationships:</p>

        <ul>
            <li>
<strong>Architecture Overview</strong> is referenced by most implementation diagrams</li>
            <li>
<strong>ERD Overview</strong> is the foundation for all database-related diagrams</li>
            <li>
<strong>Class Diagram (Overview)</strong> provides context for more detailed class diagrams</li>
            <li>
<strong>Authentication Flow</strong> is referenced by user management diagrams</li>
        </ul>

        <h3 id="related-diagrams">Related Diagrams</h3>

        <p>For each diagram, you can find related diagrams that provide additional context or detail:</p>

        <table>
            <thead>
                <tr>
                    <th scope="col">Diagram</th>
                    <th scope="col">Related Diagrams</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Architecture Overview</td>
                    <td>Deployment Architecture, TAD Architecture</td>
                </tr>
                <tr>
                    <td>ERD Overview</td>
                    <td>ERD Overview (Enhanced), TAD Database Schema</td>
                </tr>
                <tr>
                    <td>Authentication Flow</td>
                    <td>User Registration Sequence, TAD Authentication Flow</td>
                </tr>
                <tr>
                    <td>Class Diagram (Overview)</td>
                    <td>Class Diagram (Detailed)</td>
                </tr>
            </tbody>
        </table>

        <h2 id="diagram-export-options">Diagram Export Options</h2>

        <p>This section provides information about exporting diagrams to different formats.</p>

        <h3 id="export-formats">Export Formats</h3>

        <p>Diagrams can be exported to the following formats:</p>

        <ul>
            <li>
<strong>PNG</strong> - Raster image format, good for web use</li>
            <li>
<strong>SVG</strong> - Vector image format, scalable without quality loss</li>
            <li>
<strong>PDF</strong> - Portable Document Format, good for printing</li>
        </ul>

        <h3 id="batch-export">Batch Export</h3>

        <p>To export multiple diagrams at once, you can use the batch export script:</p>

        <pre><code>./scripts/export-diagrams.sh --format=svg --output=./exports</code></pre>

        <h3 id="command-line-export">Command-Line Export</h3>

        <p>For Mermaid diagrams:</p>

        <pre><code>npx @mermaid-js/mermaid-cli -i input.md -o output.svg</code></pre>

        <p>For PlantUML diagrams:</p>

        <pre><code>java -jar plantuml.jar -tsvg input.puml</code></pre>

        <script>
            // Define performSearch globally so it can be called from other scripts
            let globalPerformSearch;

            // Variables for the modal/lightbox functionality
            let currentDiagramIndex = 0;
            let diagramGallery = [];

            document.addEventListener('DOMContentLoaded', function() {
                const searchInput = document.getElementById('diagram-search');
                const searchButton = document.getElementById('search-button');
                const resetButton = document.getElementById('reset-button');
                const themeToggle = document.getElementById('theme-toggle');
                const searchResults = document.getElementById('search-results');
                const resultCount = document.getElementById('result-count');
                const resultsContainer = document.getElementById('results-container');
                const typeFilters = {
                    flowchart: document.getElementById('filter-flowchart'),
                    erd: document.getElementById('filter-erd'),
                    sequence: document.getElementById('filter-sequence'),
                    class: document.getElementById('filter-class'),
                    state: document.getElementById('filter-state'),
                    gantt: document.getElementById('filter-gantt')
                };

                // Persistent settings functionality
                function saveUserPreferences() {
                    // Save search term
                    localStorage.setItem('diagramIndexSearchTerm', searchInput.value);

                    // Save filter states
                    const filterStates = {};
                    Object.entries(typeFilters).forEach(([type, checkbox]) => {
                        if (checkbox) {
                            filterStates[type] = checkbox.checked;
                        }
                    });
                    localStorage.setItem('diagramIndexFilterStates', JSON.stringify(filterStates));
                }

                function loadUserPreferences() {
                    // Load theme preference
                    const savedTheme = localStorage.getItem('diagramIndexTheme') || 'light';

                    // Set the theme
                    if (savedTheme !== 'light') {
                        document.documentElement.setAttribute('data-theme', savedTheme);

                        // Switch thumbnail images based on theme if it's dark mode
                        if (savedTheme === 'dark' || savedTheme === 'high-contrast-dark') {
                            const thumbnailImages = document.querySelectorAll('img[src*="thumbnails"]');

                            thumbnailImages.forEach(img => {
                                const currentSrc = img.getAttribute('src');
                                let newSrc = currentSrc;

                                // Determine the correct thumbnail path based on the container's data attributes
                                const container = img.closest('.thumbnail-container');
                                if (container) {
                                    const diagramType = container.dataset.diagramType?.toLowerCase() || '';
                                    const diagramName = container.dataset.diagramName?.toLowerCase().replace(/\s+/g, '-') || '';

                                    // Determine the correct source folder (mermaid or plantuml)
                                    let sourceFolder = 'mermaid';
                                    if (diagramType === 'sequence' || diagramType === 'flowchart' ||
                                        diagramType === 'gantt' || diagramType === 'deployment') {
                                        sourceFolder = 'plantuml';
                                    }

                                    // Create the correct path for dark mode
                                    newSrc = `thumbnails/${sourceFolder}/dark/${diagramName.toLowerCase()}-dark-thumb.svg`;
                                } else {
                                    // Fallback to simple replacement if container not found
                                    if (currentSrc.includes('/light/')) {
                                        newSrc = currentSrc.replace('/light/', '/dark/').replace('-light-thumb.svg', '-dark-thumb.svg');
                                    }
                                }

                                // Only update if the source has changed
                                if (newSrc !== currentSrc) {
                                    img.setAttribute('src', newSrc);
                                }
                            });
                        }
                    }

                    // Update the theme toggle button icon regardless of the current theme
                    const themeToggleBtn = document.getElementById('theme-toggle-btn');
                    if (themeToggleBtn) {
                        if (savedTheme === 'dark' || savedTheme === 'high-contrast-dark') {
                            themeToggleBtn.innerHTML = '&#9728;&#65039;'; // Sun for dark modes (to switch to light)
                            themeToggleBtn.title = 'Switch to Light Mode';
                            themeToggleBtn.setAttribute('aria-label', 'Switch to Light Mode');
                        } else {
                            themeToggleBtn.innerHTML = '&#127763;'; // Moon for light modes (to switch to dark)
                            themeToggleBtn.title = 'Switch to Dark Mode';
                            themeToggleBtn.setAttribute('aria-label', 'Switch to Dark Mode');
                        }
                    }

                    // Update the high contrast button icon
                    const highContrastBtn = document.getElementById('high-contrast-btn');
                    if (highContrastBtn) {
                        if (savedTheme === 'high-contrast-light' || savedTheme === 'high-contrast-dark') {
                            highContrastBtn.innerHTML = '&#128065;&#65039;&zwj;&#128488;'; // Eye with speech bubble (return to normal)
                            highContrastBtn.title = 'Exit High Contrast Mode';
                            highContrastBtn.setAttribute('aria-label', 'Exit High Contrast Mode');
                        } else {
                            highContrastBtn.innerHTML = '&#128065;&#65039;'; // Eye (enable high contrast)
                            highContrastBtn.title = 'Enable High Contrast Mode';
                            highContrastBtn.setAttribute('aria-label', 'Enable High Contrast Mode');
                        }
                    }

                    // Load search term
                    const savedSearchTerm = localStorage.getItem('diagramIndexSearchTerm') || '';
                    searchInput.value = savedSearchTerm;

                    // Load filter states
                    const savedFilterStates = localStorage.getItem('diagramIndexFilterStates');
                    if (savedFilterStates) {
                        const filterStates = JSON.parse(savedFilterStates);
                        Object.entries(filterStates).forEach(([type, checked]) => {
                            const checkbox = typeFilters[type];
                            if (checkbox) {
                                checkbox.checked = checked;
                            }
                        });
                    }

                    // If there was a saved search term or active filters, perform search
                    if (savedSearchTerm || (savedFilterStates && Object.values(JSON.parse(savedFilterStates)).some(v => v))) {
                        // Delay slightly to ensure DOM is ready
                        setTimeout(performSearch, 100);
                    }
                }

                // Load user preferences
                loadUserPreferences();

                // Get all diagram tables
                const diagramTables = document.querySelectorAll('table');

                // Define the search function
                function performSearch() {
                    // Safety check - if elements don't exist, don't proceed
                    if (!searchInput || !resultsContainer || !searchResults) {
                        console.error('Search elements not found');
                        return;
                    }

                    const searchTerm = searchInput.value.toLowerCase();
                    const selectedTypes = Object.entries(typeFilters)
                        .filter(([_, checkbox]) => checkbox && checkbox.checked)
                        .map(([type, _]) => type);

                    let results = [];

                    if (!diagramTables || diagramTables.length === 0) {
                        console.warn('No diagram tables found to search');
                    }

                    diagramTables.forEach(table => {
                        const rows = table.querySelectorAll('tbody tr');

                        rows.forEach(row => {
                            const cells = row.querySelectorAll('td');
                            if (cells.length < 3) return;

                            const nameCell = cells[0]; // Adjust index based on your table structure
                            const typeCell = cells[1];
                            const descCell = cells[2];
                            const tagsCell = cells[3] || { textContent: '' };

                            const name = nameCell.textContent.toLowerCase();
                            const type = typeCell.textContent.toLowerCase();
                            const desc = descCell.textContent.toLowerCase();
                            const tags = tagsCell.textContent.toLowerCase();

                            // Check if matches search term and type filters
                            const matchesSearch = searchTerm === '' ||
                                                name.includes(searchTerm) ||
                                                desc.includes(searchTerm) ||
                                                tags.includes(searchTerm);

                            const matchesType = selectedTypes.length === 0 ||
                                            selectedTypes.some(t => type.includes(t));

                            if (matchesSearch && matchesType) {
                                results.push({
                                    name: nameCell.textContent,
                                    type: typeCell.textContent,
                                    desc: descCell.textContent,
                                    row: row.cloneNode(true)
                                });
                            }
                        });
                    });

                    // Display results
                    resultsContainer.innerHTML = '';
                    if (results.length > 0) {
                        const table = document.createElement('table');
                        table.innerHTML = `
                            <thead>
                                <tr>
                                    <th scope="col">Diagram Name</th>
                                    <th scope="col">Type</th>
                                    <th scope="col">Description</th>
                                    <th scope="col">Links</th>


                            <tbody>
                        `;

                        const tbody = table.querySelector('tbody');
                        results.forEach(result => {
                            tbody.appendChild(result.row);
                        });

                        resultsContainer.appendChild(table);
                    } else {
                        resultsContainer.innerHTML = '<p>No diagrams found matching your search criteria.';
                    }

                    resultCount.textContent = results.length;
                    searchResults.style.display = 'block';
                }

                // Make the search function globally accessible
                globalPerformSearch = performSearch;

                function resetSearch() {
                    searchInput.value = '';
                    Object.values(typeFilters).forEach(checkbox => {
                        checkbox.checked = false;
                    });
                    searchResults.style.display = 'none';

                    // Save the reset state
                    saveUserPreferences();
                }

                // Theme toggle functionality
                function toggleTheme() {
                    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
                    let newTheme = 'light';

                    // Toggle between light and dark while preserving high contrast preference
                    if (currentTheme === 'light') {
                        newTheme = 'dark';
                    } else if (currentTheme === 'dark') {
                        newTheme = 'light';
                    } else if (currentTheme === 'high-contrast-light') {
                        newTheme = 'high-contrast-dark';
                    } else if (currentTheme === 'high-contrast-dark') {
                        newTheme = 'high-contrast-light';
                    }

                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('diagramIndexTheme', newTheme);

                    // Update the theme toggle button icon
                    const themeToggleBtn = document.getElementById('theme-toggle-btn');
                    if (themeToggleBtn) {
                        if (newTheme === 'dark' || newTheme === 'high-contrast-dark') {
                            themeToggleBtn.innerHTML = '&#9728;&#65039;'; // Sun for dark modes (to switch to light)
                            themeToggleBtn.title = 'Switch to Light Mode';
                            themeToggleBtn.setAttribute('aria-label', 'Switch to Light Mode');
                        } else {
                            themeToggleBtn.innerHTML = '&#127763;'; // Moon for light modes (to switch to dark)
                            themeToggleBtn.title = 'Switch to Dark Mode';
                            themeToggleBtn.setAttribute('aria-label', 'Switch to Dark Mode');
                        }
                    }

                    // Switch thumbnail images based on theme
                    const isDarkMode = newTheme === 'dark' || newTheme === 'high-contrast-dark';
                    const thumbnailImages = document.querySelectorAll('img[src*="thumbnails"]');

                    thumbnailImages.forEach(img => {
                        const currentSrc = img.getAttribute('src');
                        let newSrc = currentSrc;

                        // Determine the correct thumbnail path based on the container's data attributes
                        const container = img.closest('.thumbnail-container');
                        if (container) {
                            const diagramType = container.dataset.diagramType?.toLowerCase() || '';
                            const diagramName = container.dataset.diagramName?.toLowerCase().replace(/\s+/g, '-') || '';

                            // Determine the correct source folder (mermaid or plantuml)
                            let sourceFolder = 'mermaid';
                            if (diagramType === 'sequence' || diagramType === 'flowchart' ||
                                diagramType === 'gantt' || diagramType === 'deployment') {
                                sourceFolder = 'plantuml';
                            }

                            // Create the correct path based on theme
                            if (isDarkMode) {
                                newSrc = `thumbnails/${sourceFolder}/dark/${diagramName.toLowerCase()}-dark-thumb.svg`;
                            } else {
                                newSrc = `thumbnails/${sourceFolder}/light/${diagramName.toLowerCase()}-light-thumb.svg`;
                            }
                        } else {
                            // Fallback to simple replacement if container not found
                            if (isDarkMode && currentSrc.includes('/light/')) {
                                newSrc = currentSrc.replace('/light/', '/dark/').replace('-light-thumb.svg', '-dark-thumb.svg');
                            } else if (!isDarkMode && currentSrc.includes('/dark/')) {
                                newSrc = currentSrc.replace('/dark/', '/light/').replace('-dark-thumb.svg', '-light-thumb.svg');
                            }
                        }

                        // Only update if the source has changed
                        if (newSrc !== currentSrc) {
                            img.setAttribute('src', newSrc);
                        }
                    });

                    // Also update the high contrast button icon
                    const highContrastBtn = document.getElementById('high-contrast-btn');
                    if (highContrastBtn) {
                        if (newTheme === 'high-contrast-light' || newTheme === 'high-contrast-dark') {
                            highContrastBtn.innerHTML = '&#128065;&#65039;&zwj;&#128488;'; // Eye with speech bubble (return to normal)
                            highContrastBtn.title = 'Exit High Contrast Mode';
                            highContrastBtn.setAttribute('aria-label', 'Exit High Contrast Mode');
                        } else {
                            highContrastBtn.innerHTML = '&#128065;&#65039;'; // Eye (enable high contrast)
                            highContrastBtn.title = 'Enable High Contrast Mode';
                            highContrastBtn.setAttribute('aria-label', 'Enable High Contrast Mode');
                        }
                    }
                }

                // High contrast toggle functionality
                function toggleHighContrast() {
                    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
                    let newTheme;

                    // Toggle between normal and high contrast modes while preserving light/dark preference
                    if (currentTheme === 'light') {
                        newTheme = 'high-contrast-light';
                    } else if (currentTheme === 'dark') {
                        newTheme = 'high-contrast-dark';
                    } else if (currentTheme === 'high-contrast-light') {
                        newTheme = 'light';
                    } else if (currentTheme === 'high-contrast-dark') {
                        newTheme = 'dark';
                    }

                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('diagramIndexTheme', newTheme);

                    // Switch thumbnail images based on theme
                    const isDarkMode = newTheme === 'dark' || newTheme === 'high-contrast-dark';
                    const thumbnailImages = document.querySelectorAll('img[src*="thumbnails"]');

                    thumbnailImages.forEach(img => {
                        const currentSrc = img.getAttribute('src');
                        let newSrc = currentSrc;

                        // Determine the correct thumbnail path based on the container's data attributes
                        const container = img.closest('.thumbnail-container');
                        if (container) {
                            const diagramType = container.dataset.diagramType?.toLowerCase() || '';
                            const diagramName = container.dataset.diagramName?.toLowerCase().replace(/\s+/g, '-') || '';

                            // Determine the correct source folder (mermaid or plantuml)
                            let sourceFolder = 'mermaid';
                            if (diagramType === 'sequence' || diagramType === 'flowchart' ||
                                diagramType === 'gantt' || diagramType === 'deployment') {
                                sourceFolder = 'plantuml';
                            }

                            // Create the correct path based on theme
                            if (isDarkMode) {
                                newSrc = `thumbnails/${sourceFolder}/dark/${diagramName.toLowerCase()}-dark-thumb.svg`;
                            } else {
                                newSrc = `thumbnails/${sourceFolder}/light/${diagramName.toLowerCase()}-light-thumb.svg`;
                            }
                        } else {
                            // Fallback to simple replacement if container not found
                            if (isDarkMode && currentSrc.includes('/light/')) {
                                newSrc = currentSrc.replace('/light/', '/dark/').replace('-light-thumb.svg', '-dark-thumb.svg');
                            } else if (!isDarkMode && currentSrc.includes('/dark/')) {
                                newSrc = currentSrc.replace('/dark/', '/light/').replace('-dark-thumb.svg', '-light-thumb.svg');
                            }
                        }

                        // Only update if the source has changed
                        if (newSrc !== currentSrc) {
                            img.setAttribute('src', newSrc);
                        }
                    });

                    // Update the high contrast button icon
                    const highContrastBtn = document.getElementById('high-contrast-btn');
                    if (highContrastBtn) {
                        if (newTheme === 'high-contrast-light' || newTheme === 'high-contrast-dark') {
                            highContrastBtn.innerHTML = '&#128065;&#65039;&zwj;&#128488;'; // Eye with speech bubble (return to normal)
                            highContrastBtn.title = 'Exit High Contrast Mode';
                            highContrastBtn.setAttribute('aria-label', 'Exit High Contrast Mode');
                        } else {
                            highContrastBtn.innerHTML = '&#128065;&#65039;'; // Eye (enable high contrast)
                            highContrastBtn.title = 'Enable High Contrast Mode';
                            highContrastBtn.setAttribute('aria-label', 'Enable High Contrast Mode');
                        }
                    }

                    // Also update the theme toggle button to show correct icon
                    const themeToggleBtn = document.getElementById('theme-toggle-btn');
                    if (themeToggleBtn) {
                        if (newTheme === 'dark' || newTheme === 'high-contrast-dark') {
                            themeToggleBtn.innerHTML = '&#9728;&#65039;'; // Sun for dark modes (to switch to light)
                            themeToggleBtn.title = 'Switch to Light Mode';
                            themeToggleBtn.setAttribute('aria-label', 'Switch to Light Mode');
                        } else {
                            themeToggleBtn.innerHTML = '&#127763;'; // Moon for light modes (to switch to dark)
                            themeToggleBtn.title = 'Switch to Dark Mode';
                            themeToggleBtn.setAttribute('aria-label', 'Switch to Dark Mode');
                        }
                    }
                }

                // Event listeners
                if (searchButton) {
                    searchButton.addEventListener('click', performSearch);
                }

                if (resetButton) {
                    resetButton.addEventListener('click', resetSearch);
                }

                // Old theme toggle button removed from search bar

                // High contrast toggle button in floating bar
                const highContrastBtn = document.getElementById('high-contrast-btn');
                if (highContrastBtn) {
                    highContrastBtn.addEventListener('click', function() {
                        toggleHighContrast();

                        // Update comparison tool if it's open
                        if (comparisonContainer.open) {
                            if (diagram1Select.value) {
                                updateComparisonDisplay(diagram1Select, diagram1Image, diagram1Type, diagram1Desc);
                            }
                            if (diagram2Select.value) {
                                updateComparisonDisplay(diagram2Select, diagram2Image, diagram2Type, diagram2Desc);
                            }
                        }
                    });
                }

                // Floating theme toggle button
                const themeToggleBtn = document.getElementById('theme-toggle-btn');
                if (themeToggleBtn) {
                    themeToggleBtn.addEventListener('click', function() {
                        toggleTheme();

                        // Update comparison tool if it's open
                        if (comparisonContainer.open) {
                            if (diagram1Select.value) {
                                updateComparisonDisplay(diagram1Select, diagram1Image, diagram1Type, diagram1Desc);
                            }
                            if (diagram2Select.value) {
                                updateComparisonDisplay(diagram2Select, diagram2Image, diagram2Type, diagram2Desc);
                            }
                        }
                    });
                }

                if (searchInput) {
                    searchInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            performSearch();
                            saveUserPreferences();
                        }
                    });

                    // Add input event listener to enable real-time search as user types
                    searchInput.addEventListener('input', function() {
                        // Only perform search if input has at least 2 characters or is empty
                        if (this.value.length >= 2 || this.value.length === 0) {
                            performSearch();
                            saveUserPreferences();
                        }
                    });
                }

                // Add event listeners to type filter checkboxes
                Object.values(typeFilters).forEach(checkbox => {
                    if (checkbox) {
                        checkbox.addEventListener('change', function() {
                            performSearch();
                            saveUserPreferences();
                        });
                    }
                });

                // Modal/Lightbox functionality
                const modal = document.getElementById('diagramModal');
                const modalImage = document.getElementById('modalImage');
                const modalCaption = document.getElementById('modalCaption');
                const modalDescription = document.getElementById('modalDescription');
                const modalClose = document.querySelector('.modal-close');
                const modalPrev = document.querySelector('.modal-prev');
                const modalNext = document.querySelector('.modal-next');

                // Get all thumbnail containers
                const thumbnailContainers = document.querySelectorAll('.thumbnail-container');

                // Build the diagram gallery array
                thumbnailContainers.forEach((container, index) => {
                    const diagramName = container.dataset.diagramName;
                    const diagramType = container.dataset.diagramType;
                    const diagramNameSlug = diagramName.toLowerCase().replace(/\s+/g, '-');

                    // Determine the correct source folder (mermaid or plantuml)
                    let sourceFolder = 'mermaid';
                    if (diagramType.toLowerCase() === 'sequence' ||
                        diagramType.toLowerCase() === 'flowchart' ||
                        diagramType.toLowerCase() === 'gantt' ||
                        diagramType.toLowerCase() === 'deployment') {
                        sourceFolder = 'plantuml';
                    }

                    // Generate the correct thumbnail paths
                    const darkThumbnailPath = `thumbnails/${sourceFolder}/dark/${diagramNameSlug}-dark-thumb.svg`;
                    const lightThumbnailPath = `thumbnails/${sourceFolder}/light/${diagramNameSlug}-light-thumb.svg`;

                    diagramGallery.push({
                        name: diagramName,
                        type: diagramType,
                        description: container.dataset.diagramDesc,
                        darkPath: container.dataset.diagramDark,
                        lightPath: container.dataset.diagramLight,
                        darkThumbnailPath: darkThumbnailPath,
                        lightThumbnailPath: lightThumbnailPath,
                        thumbnailSrc: container.querySelector('img').src
                    });

                    // Add click event to open the modal
                    container.addEventListener('click', function() {
                        openModal(index);
                    });
                });

                // Function to open the modal with a specific diagram
                function openModal(index) {
                    currentDiagramIndex = index;
                    const diagram = diagramGallery[index];
                    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark' ||
                                      document.documentElement.getAttribute('data-theme') === 'high-contrast-dark';

                    // Determine the correct thumbnail path based on theme
                    let thumbnailSrc = isDarkMode ? diagram.darkThumbnailPath : diagram.lightThumbnailPath;

                    // Fallback to the original thumbnail source if the new paths are not available
                    if (!thumbnailSrc) {
                        thumbnailSrc = diagram.thumbnailSrc;
                    }

                    // Use the thumbnail as a placeholder while loading the actual diagram
                    modalImage.src = thumbnailSrc;

                    // Set the caption (now an h2 element)
                    modalCaption.textContent = `${diagram.name} (${diagram.type})`;

                    // Show the modal using the native dialog method
                    if (!modal.open) {
                        modal.showModal();
                    }

                    // Load the actual diagram content (this would typically fetch the Mermaid/PlantUML content)
                    // For now, we'll just use the thumbnail as a placeholder
                    // In a real implementation, you would fetch the diagram content and render it

                    // Update navigation buttons visibility
                    updateNavigationButtons();
                }

                // Function to close the modal
                function closeModal() {
                    modal.close();
                }

                // Function to navigate to the previous diagram
                function showPreviousDiagram() {
                    if (currentDiagramIndex > 0) {
                        openModal(currentDiagramIndex - 1);
                    }
                }

                // Function to navigate to the next diagram
                function showNextDiagram() {
                    if (currentDiagramIndex < diagramGallery.length - 1) {
                        openModal(currentDiagramIndex + 1);
                    }
                }

                // Function to update the visibility of navigation buttons
                function updateNavigationButtons() {
                    modalPrev.style.display = currentDiagramIndex > 0 ? 'flex' : 'none';
                    modalNext.style.display = currentDiagramIndex < diagramGallery.length - 1 ? 'flex' : 'none';
                }

                // Add event listeners for modal controls
                modalClose.addEventListener('click', closeModal);
                modalPrev.addEventListener('click', showPreviousDiagram);
                modalNext.addEventListener('click', showNextDiagram);

                // Close the modal when clicking on the backdrop (native dialog behavior)
                modal.addEventListener('click', function(event) {
                    const rect = modal.getBoundingClientRect();
                    const isInDialog = rect.top <= event.clientY && event.clientY <= rect.top + rect.height &&
                                      rect.left <= event.clientX && event.clientX <= rect.left + rect.width;
                    if (!isInDialog) {
                        closeModal();
                    }
                });

                // Add keyboard navigation for the modal
                document.addEventListener('keydown', function(event) {
                    if (modal.open) {
                        // Note: Escape is handled automatically by the browser for <dialog> elements
                        if (event.key === 'ArrowLeft') {
                            showPreviousDiagram();
                        } else if (event.key === 'ArrowRight') {
                            showNextDiagram();
                        }
                    }
                });

                // Add touch event handling for mobile devices
                let touchStartX = 0;
                let touchEndX = 0;

                modal.addEventListener('touchstart', function(event) {
                    touchStartX = event.changedTouches[0].screenX;
                }, false);

                modal.addEventListener('touchend', function(event) {
                    touchEndX = event.changedTouches[0].screenX;
                    handleSwipe();
                }, false);

                function handleSwipe() {
                    if (!modal.open) return;

                    const swipeThreshold = 50; // Minimum distance for a swipe
                    const swipeDistance = touchEndX - touchStartX;

                    if (swipeDistance > swipeThreshold) {
                        // Swiped right - show previous
                        showPreviousDiagram();
                    } else if (swipeDistance < -swipeThreshold) {
                        // Swiped left - show next
                        showNextDiagram();
                    }
                }

                // Clickable tags functionality
                const clickableTags = document.querySelectorAll('.clickable-tag');

                clickableTags.forEach(tag => {
                    tag.addEventListener('click', function() {
                        const tagValue = this.dataset.tag;
                        searchInput.value = tagValue;
                        performSearch();

                        // Scroll to search results
                        document.getElementById('search-results').scrollIntoView({ behavior: 'smooth' });

                        // Highlight the active tag
                        clickableTags.forEach(t => t.classList.remove('active'));
                        this.classList.add('active');
                    });
                });

                // Statistics dashboard charts
                function initCharts() {
                    // Chart for diagram types
                    const typeCtx = document.getElementById('diagramTypeChart').getContext('2d');
                    const typeData = {
                        labels: ['Flowchart', 'ERD', 'Sequence', 'Class', 'State', 'Gantt', 'Deployment'],
                        datasets: [{
                            label: 'Number of Diagrams',
                            data: [15, 8, 12, 4, 3, 5, 3],
                            backgroundColor: [
                                '#0077cc',
                                '#087f5b',
                                '#e67700',
                                '#9c36b5',
                                '#c92a2a',
                                '#5f3dc4',
                                '#1864ab'
                            ],
                            borderWidth: 1
                        }]
                    };

                    // Use different colors in dark mode
                    if (document.documentElement.getAttribute('data-theme') === 'dark') {
                        typeData.datasets[0].backgroundColor = [
                            '#4dabf7',
                            '#63e6be',
                            '#ffc078',
                            '#da77f2',
                            '#ff8787',
                            '#b197fc',
                            '#74c0fc'
                        ];
                    }

                    new Chart(typeCtx, {
                        type: 'pie',
                        data: typeData,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'right',
                                    labels: {
                                        color: document.documentElement.getAttribute('data-theme') === 'dark' ? '#e0e0e0' : '#333'
                                    }
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const label = context.label || '';
                                            const value = context.raw || 0;
                                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                            const percentage = Math.round((value / total) * 100);
                                            return `${label}: ${value} (${percentage}%)`;
                                        }
                                    }
                                }
                            }
                        }
                    });

                    // Chart for most referenced diagrams
                    const referencedCtx = document.getElementById('referencedDiagramsChart').getContext('2d');
                    const referencedData = {
                        labels: ['Architecture Overview', 'ERD Overview', 'Authentication Flow', 'Class Diagram', 'Project Roadmap'],
                        datasets: [{
                            label: 'References',
                            data: [12, 10, 8, 7, 6],
                            backgroundColor: document.documentElement.getAttribute('data-theme') === 'dark' ? '#4dabf7' : '#0077cc',
                            borderColor: document.documentElement.getAttribute('data-theme') === 'dark' ? '#4dabf7' : '#0077cc',
                            borderWidth: 1
                        }]
                    };

                    new Chart(referencedCtx, {
                        type: 'bar',
                        data: referencedData,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        color: document.documentElement.getAttribute('data-theme') === 'dark' ? '#e0e0e0' : '#333'
                                    },
                                    grid: {
                                        color: document.documentElement.getAttribute('data-theme') === 'dark' ? '#444' : '#ddd'
                                    }
                                },
                                x: {
                                    ticks: {
                                        color: document.documentElement.getAttribute('data-theme') === 'dark' ? '#e0e0e0' : '#333'
                                    },
                                    grid: {
                                        color: document.documentElement.getAttribute('data-theme') === 'dark' ? '#444' : '#ddd'
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }

                // Initialize charts
                initCharts();

                // Update charts and comparison tool when theme changes
                themeToggle.addEventListener('click', function() {
                    // Allow time for the theme to change
                    setTimeout(initCharts, 100);
                    setTimeout(initRelationshipVisualization, 100);

                    // Update comparison tool if it's open
                    if (comparisonContainer.open) {
                        if (diagram1Select.value) {
                            updateComparisonDisplay(diagram1Select, diagram1Image, diagram1Type, diagram1Desc);
                        }
                        if (diagram2Select.value) {
                            updateComparisonDisplay(diagram2Select, diagram2Image, diagram2Type, diagram2Desc);
                        }
                    }
                });

                // Diagram relationship visualization
                function initRelationshipVisualization() {
                    // Clear previous visualization
                    d3.select('#relationship-visualization').html('');

                    // Sample data for the diagram relationships
                    const nodes = [
                        { id: 'architecture-overview', name: 'Architecture Overview', type: 'Flowchart' },
                        { id: 'erd-overview', name: 'ERD Overview', type: 'ERD' },
                        { id: 'authentication-flow', name: 'Authentication Flow', type: 'Sequence' },
                        { id: 'class-diagram', name: 'Class Diagram', type: 'Class' },
                        { id: 'project-roadmap', name: 'Project Roadmap', type: 'Gantt' },
                        { id: 'deployment-architecture', name: 'Deployment Architecture', type: 'Deployment' },
                        { id: 'tad-database-schema', name: 'TAD Database Schema', type: 'ERD' },
                        { id: 'user-registration', name: 'User Registration', type: 'Sequence' },
                        { id: 'team-creation', name: 'Team Creation', type: 'Sequence' },
                        { id: 'post-creation', name: 'Post Creation', type: 'Sequence' },
                        { id: 'todo-state-machine', name: 'Todo State Machine', type: 'State' }
                    ];

                    const links = [
                        { source: 'architecture-overview', target: 'deployment-architecture' },
                        { source: 'architecture-overview', target: 'erd-overview' },
                        { source: 'erd-overview', target: 'tad-database-schema' },
                        { source: 'authentication-flow', target: 'user-registration' },
                        { source: 'class-diagram', target: 'erd-overview' },
                        { source: 'user-registration', target: 'team-creation' },
                        { source: 'team-creation', target: 'post-creation' },
                        { source: 'post-creation', target: 'todo-state-machine' }
                    ];

                    // Set up the SVG container
                    const width = document.getElementById('relationship-visualization').clientWidth;
                    const height = 400;

                    const svg = d3.select('#relationship-visualization')
                        .append('svg')
                        .attr('width', width)
                        .attr('height', height);

                    // Create a group for the visualization
                    const g = svg.append('g');

                    // Set up the simulation
                    const simulation = d3.forceSimulation(nodes)
                        .force('link', d3.forceLink(links).id(d => d.id).distance(100))
                        .force('charge', d3.forceManyBody().strength(-200))
                        .force('center', d3.forceCenter(width / 2, height / 2))
                        .force('collision', d3.forceCollide().radius(40));

                    // Create the links
                    const link = g.append('g')
                        .attr('class', 'links')
                        .selectAll('line')
                        .data(links)
                        .enter().append('line')
                        .attr('class', 'link');

                    // Create the nodes
                    const node = g.append('g')
                        .attr('class', 'nodes')
                        .selectAll('circle')
                        .data(nodes)
                        .enter().append('circle')
                        .attr('class', 'node')
                        .attr('r', d => getNodeRadius(d.type))
                        .attr('fill', d => getNodeColor(d.type))
                        .call(d3.drag()
                            .on('start', dragstarted)
                            .on('drag', dragged)
                            .on('end', dragended));

                    // Add node labels
                    const label = g.append('g')
                        .attr('class', 'labels')
                        .selectAll('text')
                        .data(nodes)
                        .enter().append('text')
                        .attr('class', 'node-label')
                        .text(d => d.name)
                        .attr('dy', 30); // Position below the node

                    // Add tooltips
                    node.append('title')
                        .text(d => `${d.name} (${d.type})`);

                    // Update positions on each tick
                    simulation.on('tick', () => {
                        link
                            .attr('x1', d => d.source.x)
                            .attr('y1', d => d.source.y)
                            .attr('x2', d => d.target.x)
                            .attr('y2', d => d.target.y);

                        node
                            .attr('cx', d => d.x)
                            .attr('cy', d => d.y);

                        label
                            .attr('x', d => d.x)
                            .attr('y', d => d.y);
                    });

                    // Drag functions
                    function dragstarted(event, d) {
                        if (!event.active) simulation.alphaTarget(0.3).restart();
                        d.fx = d.x;
                        d.fy = d.y;
                    }

                    function dragged(event, d) {
                        d.fx = event.x;
                        d.fy = event.y;
                    }

                    function dragended(event, d) {
                        if (!event.active) simulation.alphaTarget(0);
                        d.fx = null;
                        d.fy = null;
                    }

                    // Helper functions for node styling
                    function getNodeRadius(type) {
                        switch (type) {
                            case 'Flowchart': return 15;
                            case 'ERD': return 14;
                            case 'Sequence': return 13;
                            case 'Class': return 12;
                            case 'State': return 11;
                            case 'Gantt': return 10;
                            case 'Deployment': return 9;
                            default: return 10;
                        }
                    }

                    function getNodeColor(type) {
                        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

                        if (isDarkMode) {
                            switch (type) {
                                case 'Flowchart': return '#4dabf7';
                                case 'ERD': return '#63e6be';
                                case 'Sequence': return '#ffc078';
                                case 'Class': return '#da77f2';
                                case 'State': return '#ff8787';
                                case 'Gantt': return '#b197fc';
                                case 'Deployment': return '#74c0fc';
                                default: return '#4dabf7';
                            }
                        } else {
                            switch (type) {
                                case 'Flowchart': return '#0077cc';
                                case 'ERD': return '#087f5b';
                                case 'Sequence': return '#e67700';
                                case 'Class': return '#9c36b5';
                                case 'State': return '#c92a2a';
                                case 'Gantt': return '#5f3dc4';
                                case 'Deployment': return '#1864ab';
                                default: return '#0077cc';
                            }
                        }
                    }
                }

                // Initialize relationship visualization
                initRelationshipVisualization();

                // Keyboard shortcuts functionality
                const keyboardShortcutsBtn = document.getElementById('keyboard-shortcuts-btn');
                const keyboardShortcutsModal = document.getElementById('keyboard-shortcuts-modal');
                const keyboardShortcutsClose = document.querySelector('.keyboard-shortcuts-close');

                // Print view functionality
                const printViewBtn = document.getElementById('print-view-btn');

                // Diagram comparison tool functionality
                const compareBtn = document.getElementById('compare-btn');
                const comparisonContainer = document.getElementById('comparison-container');
                const comparisonClose = document.querySelector('.comparison-close');
                const diagram1Select = document.getElementById('diagram1-select');
                const diagram2Select = document.getElementById('diagram2-select');
                const diagram1Image = document.getElementById('diagram1-image');
                const diagram2Image = document.getElementById('diagram2-image');
                const diagram1Type = document.getElementById('diagram1-type');
                const diagram2Type = document.getElementById('diagram2-type');
                const diagram1Desc = document.getElementById('diagram1-desc');
                const diagram2Desc = document.getElementById('diagram2-desc');

                // Feedback form functionality
                const feedbackBtn = document.getElementById('feedback-btn');
                const feedbackContainer = document.getElementById('feedback-container');
                const feedbackClose = document.querySelector('.feedback-close');
                const feedbackForm = document.getElementById('feedback-form');
                const feedbackDiagramSelect = document.getElementById('feedback-diagram');
                const feedbackSuccess = document.querySelector('.feedback-success');

                // Function to show keyboard shortcuts modal
                function showKeyboardShortcuts() {
                    if (!keyboardShortcutsModal.open) {
                        keyboardShortcutsModal.showModal();
                    }
                }

                // Function to hide keyboard shortcuts modal
                function hideKeyboardShortcuts() {
                    keyboardShortcutsModal.close();
                }

                // Add event listeners for keyboard shortcuts modal
                keyboardShortcutsBtn.addEventListener('click', showKeyboardShortcuts);
                keyboardShortcutsClose.addEventListener('click', hideKeyboardShortcuts);
                keyboardShortcutsModal.addEventListener('click', function(event) {
                    const rect = keyboardShortcutsModal.getBoundingClientRect();
                    const isInDialog = rect.top <= event.clientY && event.clientY <= rect.top + rect.height &&
                                      rect.left <= event.clientX && event.clientX <= rect.left + rect.width;
                    if (!isInDialog) {
                        hideKeyboardShortcuts();
                    }
                });

                // Function to prepare page for printing
                function preparePrintView() {
                    // Expand all details elements
                    const allDetails = document.querySelectorAll('details');
                    allDetails.forEach(detail => {
                        detail.setAttribute('open', '');
                    });

                    // Print the page
                    window.print();

                    // After printing, collapse details that were originally collapsed
                    // We'll use a timeout to ensure this happens after the print dialog closes
                    setTimeout(() => {
                        // For simplicity, we'll just collapse all details except the first one
                        // In a real implementation, you might want to track which ones were originally open
                        allDetails.forEach((detail, index) => {
                            if (index > 0) { // Keep the first one open
                                detail.removeAttribute('open');
                            }
                        });
                    }, 1000);
                }

                // Add event listener for print view button
                printViewBtn.addEventListener('click', preparePrintView);

                // Functions for diagram comparison tool
                function showComparisonTool() {
                    // Populate the select dropdowns if they're empty
                    if (diagram1Select.options.length <= 1) {
                        populateComparisonSelects();
                    }

                    // Show the dialog using the native dialog method
                    if (!comparisonContainer.open) {
                        comparisonContainer.showModal();
                    }
                }

                function hideComparisonTool() {
                    comparisonContainer.close();
                }

                function populateComparisonSelects() {
                    // Clear existing options except the first one
                    diagram1Select.innerHTML = '<option value="">Select a diagram...';
                    diagram2Select.innerHTML = '<option value="">Select a diagram...';

                    // Add options for each diagram in the gallery
                    diagramGallery.forEach((diagram, index) => {
                        const option1 = document.createElement('option');
                        option1.value = index;
                        option1.textContent = diagram.name;
                        diagram1Select.appendChild(option1);

                        const option2 = document.createElement('option');
                        option2.value = index;
                        option2.textContent = diagram.name;
                        diagram2Select.appendChild(option2);
                    });
                }

                function updateComparisonDisplay(selectElement, imageElement, typeElement, descElement) {
                    const selectedIndex = parseInt(selectElement.value);
                    if (isNaN(selectedIndex) || selectedIndex < 0 || selectedIndex >= diagramGallery.length) {
                        // Clear the display if no valid selection
                        imageElement.src = '';
                        typeElement.textContent = '';
                        descElement.textContent = '';
                        return;
                    }

                    const selectedDiagram = diagramGallery[selectedIndex];
                    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark' ||
                                      document.documentElement.getAttribute('data-theme') === 'high-contrast-dark';

                    // Use the appropriate thumbnail based on the current theme
                    let thumbnailSrc = isDarkMode ? selectedDiagram.darkThumbnailPath : selectedDiagram.lightThumbnailPath;

                    // Fallback to the original thumbnail source if the new paths are not available
                    if (!thumbnailSrc) {
                        thumbnailSrc = selectedDiagram.thumbnailSrc;
                    }

                    imageElement.src = thumbnailSrc;
                    typeElement.textContent = selectedDiagram.type;
                    descElement.textContent = selectedDiagram.description;
                }

                // Add event listeners for comparison tool
                compareBtn.addEventListener('click', showComparisonTool);
                comparisonClose.addEventListener('click', hideComparisonTool);
                comparisonContainer.addEventListener('click', function(event) {
                    const rect = comparisonContainer.getBoundingClientRect();
                    const isInDialog = rect.top <= event.clientY && event.clientY <= rect.top + rect.height &&
                                      rect.left <= event.clientX && event.clientX <= rect.left + rect.width;
                    if (!isInDialog) {
                        hideComparisonTool();
                    }
                });

                diagram1Select.addEventListener('change', function() {
                    updateComparisonDisplay(this, diagram1Image, diagram1Type, diagram1Desc);
                });

                diagram2Select.addEventListener('change', function() {
                    updateComparisonDisplay(this, diagram2Image, diagram2Type, diagram2Desc);
                });

                // Functions for feedback form
                function showFeedbackForm() {
                    feedbackSuccess.style.display = 'none';
                    feedbackForm.reset();

                    // Populate the diagram select if it's empty
                    if (feedbackDiagramSelect.options.length <= 1) {
                        populateFeedbackDiagramSelect();
                    }

                    // Show the dialog using the native dialog method
                    if (!feedbackContainer.open) {
                        feedbackContainer.showModal();
                    }
                }

                function hideFeedbackForm() {
                    feedbackContainer.close();
                }

                function populateFeedbackDiagramSelect() {
                    // Clear existing options except the first one
                    feedbackDiagramSelect.innerHTML = '<option value="">Select a diagram...';

                    // Add options for each diagram in the gallery
                    diagramGallery.forEach((diagram, index) => {
                        const option = document.createElement('option');
                        option.value = diagram.name;
                        option.textContent = diagram.name;
                        feedbackDiagramSelect.appendChild(option);
                    });
                }

                function handleFeedbackSubmit(event) {
                    event.preventDefault();

                    // In a real implementation, you would send the feedback to a server
                    // For this demo, we'll just show a success message
                    feedbackSuccess.style.display = 'block';
                    feedbackForm.style.display = 'none';

                    // Reset the form after a delay
                    setTimeout(() => {
                        feedbackForm.reset();
                        feedbackForm.style.display = 'flex';
                        feedbackSuccess.style.display = 'none';
                        hideFeedbackForm();
                    }, 3000);
                }

                // Add event listeners for feedback form
                feedbackBtn.addEventListener('click', showFeedbackForm);
                feedbackClose.addEventListener('click', hideFeedbackForm);
                feedbackForm.addEventListener('submit', handleFeedbackSubmit);
                feedbackContainer.addEventListener('click', function(event) {
                    const rect = feedbackContainer.getBoundingClientRect();
                    const isInDialog = rect.top <= event.clientY && event.clientY <= rect.top + rect.height &&
                                      rect.left <= event.clientX && event.clientX <= rect.left + rect.width;
                    if (!isInDialog) {
                        hideFeedbackForm();
                    }
                });

                // Global keyboard shortcuts
                document.addEventListener('keydown', function(event) {
                    // Don't trigger shortcuts if user is typing in an input field
                    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                        // But allow Escape to reset search when focused on search input
                        if (event.key === 'Escape' && event.target === searchInput) {
                            resetSearch();
                            event.target.blur();
                        }
                        return;
                    }

                    // Modal is open - handle modal-specific shortcuts
                    if (modal.style.display === 'block') {
                        if (event.key === 'Escape') {
                            closeModal();
                        } else if (event.key === 'ArrowLeft' || event.key === 'p' || event.key === 'P') {
                            showPreviousDiagram();
                        } else if (event.key === 'ArrowRight' || event.key === 'n' || event.key === 'N') {
                            showNextDiagram();
                        }
                        return;
                    }

                    // Keyboard shortcuts modal is open
                    if (keyboardShortcutsModal.open) {
                        // Escape is handled automatically by the browser for <dialog> elements
                        return;
                    }

                    // Comparison tool is open
                    if (comparisonContainer.open) {
                        // Escape is handled automatically by the browser for <dialog> elements
                        return;
                    }

                    // Feedback form is open
                    if (feedbackContainer.open) {
                        // Escape is handled automatically by the browser for <dialog> elements
                        return;
                    }

                    // Global shortcuts
                    switch (event.key) {
                        case '/':
                            // Focus search box
                            searchInput.focus();
                            event.preventDefault();
                            break;
                        case 't':
                        case 'T':
                            // Toggle theme
                            toggleTheme();
                            break;
                        case 'h':
                        case 'H':
                            // Toggle high contrast
                            toggleHighContrast();
                            break;
                        case '?':
                            // Show keyboard shortcuts
                            showKeyboardShortcuts();
                            break;
                        case 'c':
                        case 'C':
                            // Show comparison tool
                            showComparisonTool();
                            break;
                        case 'f':
                        case 'F':
                            // Show feedback form
                            showFeedbackForm();
                            break;
                        case 'Home':
                            // Scroll to top
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                            break;
                        case 'End':
                            // Scroll to bottom
                            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                            break;
                        case 'p':
                            if (event.ctrlKey || event.metaKey) {
                                // Print view (Ctrl+P or Cmd+P)
                                event.preventDefault();
                                preparePrintView();
                            }
                            break;
                    }
                });
            });

            // Position tooltips for floating buttons
            document.querySelectorAll('.floating-btn').forEach(btn => {
                const tooltip = btn.querySelector('.tooltip');
                if (tooltip) {
                    btn.addEventListener('mouseenter', () => {
                        // Get button position
                        const btnRect = btn.getBoundingClientRect();

                        // Position tooltip above the button
                        tooltip.style.bottom = (window.innerHeight - btnRect.top + 10) + 'px';
                        tooltip.style.left = (btnRect.left + btnRect.width / 2) + 'px';
                    });
                }
            });
        </script>

        <!-- Added missing sections referenced in TOC -->
        <h2 id="contributing-new-diagrams">Contributing New Diagrams</h2>
        <p>This section provides guidelines for contributing new diagrams to the project documentation.</p>

        <h3>Contribution Process</h3>
        <ol>
            <li><strong>Identify the Need</strong>: Determine if a new diagram would help clarify a concept or process</li>
            <li><strong>Choose the Right Format</strong>: Select the appropriate diagram type (flowchart, ERD, sequence, etc.)</li>
            <li><strong>Create the Diagram</strong>: Use Mermaid or PlantUML to create both light and dark versions</li>
            <li><strong>Generate Thumbnails</strong>: Create thumbnails for the diagram index</li>
            <li><strong>Submit for Review</strong>: Submit the diagram for peer review before inclusion</li>
        </ol>

        <h2 id="diagram-best-practices">Diagram Best Practices</h2>
        <p>Follow these best practices when creating diagrams for the project documentation:</p>

        <ul>
            <li><strong>Keep it Simple</strong>: Focus on clarity and simplicity</li>
            <li><strong>Consistent Styling</strong>: Use consistent colors, shapes, and terminology</li>
            <li><strong>Proper Labeling</strong>: Ensure all elements are properly labeled</li>
            <li><strong>Appropriate Detail Level</strong>: Include enough detail to be useful without overwhelming</li>
            <li><strong>Dark/Light Versions</strong>: Always create both dark and light mode versions</li>
        </ul>

        <h2 id="diagram-accessibility">Diagram Accessibility</h2>
        <p>Ensure your diagrams are accessible to all users:</p>

        <ul>
            <li><strong>Text Alternatives</strong>: Provide text descriptions for all diagrams</li>
            <li><strong>Color Contrast</strong>: Ensure sufficient contrast between elements</li>
            <li><strong>Text Size</strong>: Use readable text sizes</li>
            <li><strong>Alternative Formats</strong>: Provide alternative ways to access the information</li>
            <li><strong>High Contrast Mode</strong>: Support high contrast viewing options</li>
        </ul>

        <h2 id="diagram-versioning-and-history">Diagram Versioning and History</h2>
        <p>This section explains how diagram versions are managed:</p>

        <ul>
            <li><strong>Version Tracking</strong>: All diagrams are version-controlled in the repository</li>
            <li><strong>Change History</strong>: Major changes are documented in the diagram metadata</li>
            <li><strong>Deprecation Process</strong>: Outdated diagrams are marked as deprecated before removal</li>
            <li><strong>Update Frequency</strong>: Diagrams are reviewed and updated with major releases</li>
        </ul>
    </div>
</body>
</html>
