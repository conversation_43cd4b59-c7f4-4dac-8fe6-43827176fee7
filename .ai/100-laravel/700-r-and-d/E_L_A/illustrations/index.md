# Enhanced Laravel Application - Diagram Index

**Version:** 1.1.0
**Date:** 2025-05-20
**Author:** AI Assistant
**Status:** Updated
**Progress:** 100%

---

<div style="background-color:#e0e8f0; padding:15px; border-radius:5px; border: 1px solid #b0c4de; margin:10px 0;">
<h4 style="margin-top: 0; ">Navigation</h4>

<div style="display: flex; align-items: center; margin-bottom: 5px;">
  <div style="width: 100px; font-weight: bold; color: #333;">Main:</div>
  <div>
    <a href="/_root/docs/E_L_A/README.md">Home</a> |
    <a href="/_root/docs/E_L_A/000-index.md">Documentation Index</a> |
    <a href="/_root/docs/E_L_A/illustrations/README.md">Illustrations README</a>
  </div>
</div>

<div style="display: flex; align-items: center;">
  <div style="width: 100px; font-weight: bold; color: #333;">You are here:</div>
  <div>
    <a href="/_root/docs/E_L_A/README.md">Home</a> &gt;
    <a href="/_root/docs/E_L_A/000-index.md">Documentation Index</a> &gt;
    <a href="/_root/docs/E_L_A/illustrations/README.md">Illustrations</a> &gt;
    <span style="font-weight: bold;">Diagram Index</span>
  </div>
</div>
</div>

This index provides a comprehensive list of all diagrams and illustrations used in the Enhanced Laravel Application
documentation. Use this document to quickly locate and access diagrams across the project documentation.

## Recently Updated Diagrams

These diagrams have been recently added or updated:

- **Authentication Flow** - Updated: 2025-05-15
- **ERD Overview** - Updated: 2025-05-14
- **Project Roadmap** - Updated: 2025-05-10

<details>
<summary><strong style="font-size: 1.8em;">Table of Contents</strong></summary>

- [Introduction](#introduction)
  - [Organization](#organization)
    - [By Project Area](#by-project-area)
    - [By Diagram Type](#by-diagram-type)
    - [By Feature](#by-feature)
  - [Format Availability](#format-availability)
  - [Diagram Naming Conventions](#diagram-naming-conventions)
  - [How to Use This Index](#how-to-use-this-index)
- [Quick Reference](#quick-reference)
- [Illustrations Index](#illustrations-index)
  - [Project Overview & Planning](#project-overview--planning)
  - [Technical Architecture](#technical-architecture)
  - [Core Functionality](#core-functionality)
  - [Implementation Details](#implementation-details)
  - [Documentation](#documentation)
- [Diagram Types](#diagram-types)
  - [Flowcharts](#flowcharts)
  - [Entity Relationship Diagrams (ERD)](#entity-relationship-diagrams-erd)
  - [Sequence Diagrams](#sequence-diagrams)
  - [Class Diagrams](#class-diagrams)
  - [State Diagrams](#state-diagrams)
  - [Gantt Charts](#gantt-charts)
  - [Deployment Diagrams](#deployment-diagrams)
- [Search Tips](#search-tips)
  - [Basic Search Strategies](#basic-search-strategies)
  - [Advanced Search Techniques](#advanced-search-techniques)
  - [If You Can't Find a Diagram](#if-you-cant-find-a-diagram)
- [Contributing New Diagrams](#contributing-new-diagrams)
  - [Preparation](#preparation)
  - [Creation Process](#creation-process)
  - [Documentation](#documentation)
  - [Thumbnail Generation](#thumbnail-generation)
  - [Quality Assurance](#quality-assurance)
- [Diagram Best Practices](#diagram-best-practices)
  - [Clarity and Simplicity](#clarity-and-simplicity)
  - [Visual Design](#visual-design)
  - [Technical Considerations](#technical-considerations)
  - [Accessibility](#accessibility)
- [Diagram Accessibility](#diagram-accessibility)
  - [Visual Accessibility](#visual-accessibility)
  - [Alternative Text](#alternative-text)
  - [Text Alternatives](#text-alternatives)
- [Diagram Versioning and History](#diagram-versioning-and-history)
  - [Version Tracking](#version-tracking)
  - [Handling Updates](#handling-updates)
  - [Archiving](#archiving)
- [Diagram Tools and Resources](#diagram-tools-and-resources)
  - [Mermaid Tools](#mermaid-tools)
  - [PlantUML Tools](#plantuml-tools)
  - [Other Useful Resources](#other-useful-resources)
- [Diagram Templates](#diagram-templates)
  - [Flowchart Template](#flowchart-template)
  - [Sequence Diagram Template](#sequence-diagram-template)
  - [ERD Template](#erd-template)
- [Feature-Based Diagram Index](#feature-based-diagram-index)
  - [Authentication & Authorization](#authentication--authorization)
  - [User Management](#user-management)
  - [Team Management](#team-management)
  - [Content Management](#content-management)
  - [Notifications](#notifications)
  - [Search & Discovery](#search--discovery)
  - [Analytics & Reporting](#analytics--reporting)
- [Diagram Tags](#diagram-tags)
  - [Functional Tags](#functional-tags)
  - [Technical Tags](#technical-tags)
  - [Process Tags](#process-tags)
  - [Tag Cloud](#tag-cloud)
- [Search Tips](#search-tips)
  - [Basic Search Strategies](#basic-search-strategies)
  - [Advanced Search Techniques](#advanced-search-techniques)
  - [If You Can't Find a Diagram](#if-you-cant-find-a-diagram)
- [Diagram Statistics](#diagram-statistics)
  - [Diagrams by Type](#diagrams-by-type)
  - [Diagrams by Feature Area](#diagrams-by-feature-area)
  - [Most Referenced Diagrams](#most-referenced-diagrams)
- [Diagram Relationships](#diagram-relationships)
  - [Diagram Dependency Map](#diagram-dependency-map)
  - [Related Diagrams](#related-diagrams)
- [Diagram Export Options](#diagram-export-options)
  - [Export Formats](#export-formats)
  - [Batch Export](#batch-export)
  - [Command-Line Export](#command-line-export)
- [Conclusion](#conclusion)
- [Diagram Request Process](#diagram-request-process)
  - [1. Check Existing Diagrams](#1-check-existing-diagrams)
  - [2. Prepare Your Request](#2-prepare-your-request)
  - [3. Submit Your Request](#3-submit-your-request)
  - [4. Review Process](#4-review-process)

</details>

## Introduction

This index provides a comprehensive catalog of all diagrams and illustrations used throughout the Enhanced Laravel
Application documentation. It's designed to help you quickly find the visual resources you need for understanding the
project.

> **Interactive Features**: This document includes an interactive search tool at the top of the page that allows you to
> filter diagrams by name, type, description, or tags. The search tool supports both light and dark modes (toggle in the
> top-right corner of the search box). These interactive features require JavaScript to be enabled in your browser.

### Organization

The illustrations in this index are organized in multiple complementary ways to help you find the diagrams you need:

#### By Project Area

Diagrams are grouped into logical categories based on the aspect of the project they represent:

- **Project Overview & Planning**: High-level views of the project, including executive summaries, roadmaps, timelines,
  business value assessments, and risk analyses. These are particularly useful for stakeholders and project managers.

- **Technical Architecture**: Diagrams detailing system architecture, deployment strategies, database schemas, and other
  technical foundations. Essential for developers and architects to understand the overall structure.

- **Core Functionality**: Illustrations of core features and processes, including user management, team management, and
  content management workflows. These help developers understand how key features are implemented.

- **Implementation Details**: Diagrams focusing on specific implementation aspects like CQRS, Event Sourcing, state
  machines, and other technical patterns.

- **Documentation**: Diagrams explaining documentation structure, workflow, and components, providing guidance on how
  the project documentation is organized.

#### By Diagram Type

In the [Diagram Types](#diagram-types) section, diagrams are categorized by their visual format to help you understand
the different visualization techniques used throughout the documentation:

- **Flowcharts**: For visualizing processes and workflows
- **ERDs**: For database structure and relationships
- **Sequence Diagrams**: For interactions between components
- **Class Diagrams**: For object-oriented design
- **State Diagrams**: For state transitions
- **Gantt Charts**: For project timelines
- **Deployment Diagrams**: For system infrastructure

#### By Feature

Many diagrams are also relevant to specific application features. Here are the main feature categories:

- **Authentication & Authorization**: User login, registration, permissions
- **User Management**: User profiles, settings, preferences
- **Team Management**: Team creation, membership, roles
- **Content Management**: Posts, comments, media handling
- **Notifications**: Alerts, messages, email notifications
- **Search & Discovery**: Finding content and users
- **Analytics & Reporting**: Data visualization, metrics, KPIs

### Format Availability

Each diagram is available in both Mermaid and PlantUML formats, with dark and light mode variants to accommodate
different viewing preferences and environments. Many diagrams are also available in interactive HTML format and animated
versions. The tables include direct links to all diagram files for easy access.

- **Static Diagrams**: Available in Mermaid (`.md`) and PlantUML (`.puml`) formats with dark and light mode variants
- **Interactive Diagrams**: Available in HTML format with component highlighting, tooltips, and detailed information
  panels
- **Animated Diagrams**: Available in HTML format with step-by-step animations, controls, and detailed step descriptions

### Diagram Naming Conventions

Diagrams in this project follow these naming conventions:

- **Base Name**: Describes the diagram's content (e.g., `architecture-overview`, `user-registration-sequence`)
- **Format Suffix**: Indicates whether it's a Mermaid (`.md`), PlantUML (`.puml`), or HTML (`.html`) file
- **Theme Suffix**: Indicates whether it's a dark or light mode variant (`-dark` or `-light`)
- **Interactive Suffix**: Interactive diagrams use the `-interactive` suffix (e.g.,
  `event-sourcing-flow-interactive.html`)
- **Animated Suffix**: Animated diagrams use the `-animated` suffix (e.g., `event-sourcing-flow-animated.html`)

Examples:

- `architecture-overview-dark.md` is the dark mode Mermaid version of the Architecture Overview diagram
- `event-sourcing-flow-interactive.html` is the interactive HTML version of the Event Sourcing Flow diagram
- `team-aggregate-states-animated.html` is the animated HTML version of the Team Aggregate States diagram

### How to Use This Index

1. **Find by Project Area**: If you're looking for diagrams related to a specific aspect of the project (e.g.,
   architecture, implementation), use the [Illustrations Index](#illustrations-index) section.

2. **Find by Diagram Type**: If you need a specific type of diagram (e.g., flowchart, sequence diagram), navigate to the
   [Diagram Types](#diagram-types) section.

3. **Find by Tags**: Use the browser's search function (Ctrl+F/Cmd+F) to search for specific tags (e.g., `database`,
   `auth`, `timeline`) that are relevant to your needs. Tags are displayed in the Quick Reference section and help
   identify diagrams by their content or purpose.

4. **Access Source Documents**: Each diagram entry includes a link to its source document, where you can find more
   context and explanation.

5. **Choose Your Preferred Format**: Select either Mermaid or PlantUML format based on your needs, and choose between
   dark and light mode variants.

6. **Navigate with the TOC**: Use the Table of Contents at the top of this document to quickly jump to specific
   sections.

## Quick Reference

This section provides quick access to the most frequently used diagrams in the project.

> **Note**: This section includes thumbnail previews to help you quickly identify the diagrams visually. Thumbnails are
> stored in the `thumbnails/mermaid/light` and `thumbnails/mermaid/dark` directories for light and dark mode versions
> respectively. Only light mode thumbnails are shown in the table for brevity, but both versions are available.

\n<details>\n<summary>Table Details</summary>\n\n| Thumbnail (Light Mode) | Diagram Name | Type | Description | Tags | Source Document | Links (Dark/Light) |
| --- | --- | --- | --- | --- | --- | --- |
|  | Executive Summary Overview | Flowchart | Key features overview | `overview`, `features`, `summary` | [Executive Summary](/_root/docs/E_L_A/005-ela-executive-summary.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/executive-summary-overview-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/executive-summary-overview-light.md) |
|  | Architecture Overview | Flowchart | High-level system architecture | `architecture`, `system`, `structure` | [Technical Architecture Document](/_root/docs/E_L_A/030-ela-tad.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/architecture-overview-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/architecture-overview-light.md) |
|  | ERD Overview | ERD | Entity Relationship Diagram | `database`, `entities`, `schema` | [Technical Architecture Document](/_root/docs/E_L_A/030-ela-tad.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/erd-overview-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/erd-overview-light.md) |
|  | Authentication Flow | Sequence | User authentication process | `auth`, `login`, `security` | [Technical Architecture Document](/_root/docs/E_L_A/030-ela-tad.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/authentication-flow-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/authentication-flow-light.md) |
|  | Project Roadmap | Gantt | Project roadmap timeline | `timeline`, `planning`, `schedule` | [Project Roadmap](/_root/docs/E_L_A/020-ela-project-roadmap.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/project-roadmap-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/project-roadmap-light.md) |
\n</details>\n

## Illustrations Index

> **Note**: Some diagrams may appear in multiple categories if they are relevant to different aspects of the project.
> The primary listing for each diagram is in the most relevant category.

<details>
<summary><strong style="font-size: 1.2em;">Project Overview & Planning</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Mermaid File Paths | PlantUML File Paths |
| --- | --- | --- | --- | --- | --- |
|  | **Dark** | **Light** | **Dark** | **Light** |
| Executive Summary Overview | Flowchart | Key features overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [executive-summary-overview-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/executive-summary-overview-dark.md) | [executive-summary-overview-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/executive-summary-overview-light.md) | [executive-summary-overview-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/executive-summary-overview-dark.puml) | [executive-summary-overview-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/executive-summary-overview-light.puml) |
| Executive Summary Features | Flowchart | Key features overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [executive-summary-features-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/executive-summary-features-dark.md) | [executive-summary-features-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/executive-summary-features-light.md) | [executive-summary-features-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/executive-summary-features-dark.puml) | [executive-summary-features-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/executive-summary-features-light.puml) |
| Project Overview | Flowchart | Project overview diagram | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [project-overview-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/project-overview-dark.md) | [project-overview-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/project-overview-light.md) | [project-overview-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/project-overview-dark.puml) | [project-overview-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/project-overview-light.puml) |
| Project Roadmap | Gantt | Project roadmap timeline | [020-ela-project-roadmap.md](/_root/docs/E_L_A/020-ela-project-roadmap.md) | [project-roadmap-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/project-roadmap-dark.md) | [project-roadmap-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/project-roadmap-light.md) | [project-roadmap-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/project-roadmap-dark.puml) | [project-roadmap-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/project-roadmap-light.puml) |
| Implementation Timeline | Gantt | Project implementation timeline | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [implementation-timeline-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/implementation-timeline-dark.md) | [implementation-timeline-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/implementation-timeline-light.md) | [implementation-timeline-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/implementation-timeline-dark.puml) | [implementation-timeline-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/implementation-timeline-light.puml) |
| Resource Allocation Timeline | Gantt | Project resource timeline | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [resource-allocation-timeline-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/resource-allocation-timeline-dark.md) | [resource-allocation-timeline-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/resource-allocation-timeline-light.md) | [resource-allocation-timeline-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/resource-allocation-timeline-dark.puml) | [resource-allocation-timeline-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/resource-allocation-timeline-light.puml) |
| Business Value | Flowchart | Business value overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [business-value-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/business-value-dark.md) | [business-value-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/business-value-light.md) | [business-value-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/business-value-dark.puml) | [business-value-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/business-value-light.puml) |
| Risk Assessment | Quadrant | Risk assessment matrix | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [risk-assessment-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/risk-assessment-dark.md) | [risk-assessment-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/risk-assessment-light.md) | [risk-assessment-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/risk-assessment-dark.puml) | [risk-assessment-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/risk-assessment-light.puml) |
| Success Metrics | Flowchart | Success metrics overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [success-metrics-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/success-metrics-dark.md) | [success-metrics-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/success-metrics-light.md) | [success-metrics-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/success-metrics-dark.puml) | [success-metrics-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/success-metrics-light.puml) |
| Technology Stack | Flowchart | Technology stack overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [technology-stack-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/technology-stack-dark.md) | [technology-stack-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/technology-stack-light.md) | [technology-stack-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/technology-stack-dark.puml) | [technology-stack-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/technology-stack-light.puml) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Technical Architecture</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Mermaid File Paths | PlantUML File Paths |
| --- | --- | --- | --- | --- | --- |
| **Dark** | **Light** | **Dark** | **Light** |
| Architecture Overview | Flowchart | High-level system architecture | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [architecture-overview-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/architecture-overview-dark.md) | [architecture-overview-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/architecture-overview-light.md) | [architecture-overview-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/architecture-overview-dark.puml) | [architecture-overview-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/architecture-overview-light.puml) |
| TAD Architecture | Flowchart | Technical architecture diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-architecture-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-architecture-dark.md) | [tad-architecture-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-architecture-light.md) | [tad-architecture-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-architecture-dark.puml) | [tad-architecture-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-architecture-light.puml) |
| Deployment Architecture | Deployment | System deployment architecture | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [deployment-architecture-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/deployment-architecture-dark.md) | [deployment-architecture-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/deployment-architecture-light.md) | [deployment-architecture-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/deployment-architecture-dark.puml) | [deployment-architecture-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/deployment-architecture-light.puml) |
| TAD Deployment | Deployment | Deployment diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-deployment-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-deployment-dark.md) | [tad-deployment-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-deployment-light.md) | [tad-deployment-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-deployment-dark.puml) | [tad-deployment-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-deployment-light.puml) |
| ERD Overview | ERD | Entity Relationship Diagram (TAD version) | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [erd-overview-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/erd-overview-dark.md) | [erd-overview-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/erd-overview-light.md) | [erd-overview-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/erd-overview-dark.puml) | [erd-overview-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/erd-overview-light.puml) |
| ERD Overview (Enhanced) | ERD | Entity Relationship Diagram (Enhanced version) | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [erd-overview-enhanced-dark.md](mermaid/dark/erd-overview-enhanced-dark.md) | [erd-overview-enhanced-light.md](mermaid/light/erd-overview-enhanced-light.md) | [erd-overview-enhanced-dark.puml](plantuml/dark/erd-overview-enhanced-dark.puml) | [erd-overview-enhanced-light.puml](plantuml/light/erd-overview-enhanced-light.puml) |
| TAD Database Schema | ERD | Database schema diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-database-schema-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-database-schema-dark.md) | [tad-database-schema-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-database-schema-light.md) | [tad-database-schema-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-database-schema-dark.puml) | [tad-database-schema-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-database-schema-light.puml) |
| Class Diagram (Detailed) | Class | Comprehensive class structure with all attributes and methods | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [class-diagram-detailed-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/class-diagram-detailed-dark.md) | [class-diagram-detailed-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/class-diagram-detailed-light.md) | [class-diagram-detailed-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/class-diagram-detailed-dark.puml) | [class-diagram-detailed-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/class-diagram-detailed-light.puml) |
| Class Diagram (Overview) | Class | Simplified class structure showing key relationships | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [class-diagram-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/class-diagram-dark.md) | [class-diagram-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/class-diagram-light.md) | [class-diagram-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/class-diagram-dark.puml) | [class-diagram-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/class-diagram-light.puml) |
| TAD Request Lifecycle | Sequence | Request lifecycle diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-request-lifecycle-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-request-lifecycle-dark.md) | [tad-request-lifecycle-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-request-lifecycle-light.md) | [tad-request-lifecycle-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-request-lifecycle-dark.puml) | [tad-request-lifecycle-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-request-lifecycle-light.puml) |
| TAD Event Flow | Flowchart | Event flow diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-event-flow-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-event-flow-dark.md) | [tad-event-flow-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-event-flow-light.md) | [tad-event-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-event-flow-dark.puml) | [tad-event-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-event-flow-light.puml) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Core Functionality</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Mermaid File Paths | PlantUML File Paths |
| --- | --- | --- | --- | --- | --- |
| **Dark** | **Light** | **Dark** | **Light** |
| Authentication Flow | Sequence | User authentication process | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [authentication-flow-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/authentication-flow-dark.md) | [authentication-flow-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/authentication-flow-light.md) | [authentication-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/authentication-flow-dark.puml) | [authentication-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/authentication-flow-light.puml) |
| Class Diagram (Detailed) | Class | Comprehensive class structure with all attributes and methods | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [class-diagram-detailed-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/class-diagram-detailed-dark.md) | [class-diagram-detailed-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/class-diagram-detailed-light.md) | [class-diagram-detailed-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/class-diagram-detailed-dark.puml) | [class-diagram-detailed-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/class-diagram-detailed-light.puml) |
| CQRS Flow | Flowchart | Command Query Responsibility Segregation | [100-060-cqrs-configuration.md](/_root/docs/E_L_A/100-implementation-plan/100-060-cqrs-configuration.md) | [cqrs-flow-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/cqrs-flow-dark.md) | [cqrs-flow-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/cqrs-flow-light.md) | [cqrs-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/cqrs-flow-dark.puml) | [cqrs-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/cqrs-flow-light.puml) |
| Deployment Architecture | Deployment | System deployment architecture | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [deployment-architecture-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/deployment-architecture-dark.md) | [deployment-architecture-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/deployment-architecture-light.md) | [deployment-architecture-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/deployment-architecture-dark.puml) | [deployment-architecture-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/deployment-architecture-light.puml) |
| ERD Overview | ERD | Entity Relationship Diagram (TAD version) | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [erd-overview-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/erd-overview-dark.md) | [erd-overview-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/erd-overview-light.md) | [erd-overview-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/erd-overview-dark.puml) | [erd-overview-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/erd-overview-light.puml) |
| Event Sourcing Flow | Flowchart | Event sourcing implementation | [050-implementation.md](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/050-implementation.md) | [event-sourcing-flow-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/event-sourcing-flow-dark.md) | [event-sourcing-flow-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/event-sourcing-flow-light.md) | [event-sourcing-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/event-sourcing-flow-dark.puml) | [event-sourcing-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/event-sourcing-flow-light.puml) |
| Executive Summary Overview | Flowchart | Key features overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [executive-summary-overview-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/executive-summary-overview-dark.md) | [executive-summary-overview-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/executive-summary-overview-light.md) | [executive-summary-overview-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/executive-summary-overview-dark.puml) | [executive-summary-overview-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/executive-summary-overview-light.puml) |
| Migration Sequence | Sequence | Database migration process | [100-110-database-migrations.md](/_root/docs/E_L_A/100-implementation-plan/100-110-database-migrations.md) | [migration-sequence-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/migration-sequence-dark.md) | [migration-sequence-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/migration-sequence-light.md) | [migration-sequence-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/migration-sequence-dark.puml) | [migration-sequence-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/migration-sequence-light.puml) |
| Resource Allocation Timeline | Gantt | Project resource timeline | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [resource-allocation-timeline-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/resource-allocation-timeline-dark.md) | [resource-allocation-timeline-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/resource-allocation-timeline-light.md) | [resource-allocation-timeline-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/resource-allocation-timeline-dark.puml) | [resource-allocation-timeline-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/resource-allocation-timeline-light.puml) |
| Todo State Machine | State | Todo item state transitions | [100-370-status-implementation.md](/_root/docs/E_L_A/100-implementation-plan/100-370-status-implementation.md) | [todo-state-machine-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/todo-state-machine-dark.md) | [todo-state-machine-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/todo-state-machine-light.md) | [todo-state-machine-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/todo-state-machine-dark.puml) | [todo-state-machine-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/todo-state-machine-light.puml) |
| Executive Summary Features | Flowchart | Key features overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [executive-summary-features-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/executive-summary-features-dark.md) | [executive-summary-features-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/executive-summary-features-light.md) | [executive-summary-features-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/executive-summary-features-dark.puml) | [executive-summary-features-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/executive-summary-features-light.puml) |
| Implementation Timeline | Gantt | Project implementation timeline | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [implementation-timeline-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/implementation-timeline-dark.md) | [implementation-timeline-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/implementation-timeline-light.md) | [implementation-timeline-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/implementation-timeline-dark.puml) | [implementation-timeline-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/implementation-timeline-light.puml) |
| ERD Overview (Enhanced) | ERD | Entity Relationship Diagram (Enhanced version) | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [erd-overview-enhanced-dark.md](mermaid/dark/erd-overview-enhanced-dark.md) | [erd-overview-enhanced-light.md](mermaid/light/erd-overview-enhanced-light.md) | [erd-overview-enhanced-dark.puml](plantuml/dark/erd-overview-enhanced-dark.puml) | [erd-overview-enhanced-light.puml](plantuml/light/erd-overview-enhanced-light.puml) |
| Class Diagram (Overview) | Class | Simplified class structure showing key relationships | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [class-diagram-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/class-diagram-dark.md) | [class-diagram-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/class-diagram-light.md) | [class-diagram-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/class-diagram-dark.puml) | [class-diagram-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/class-diagram-light.puml) |
| User Registration Sequence | Sequence | User registration process | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [user-registration-sequence-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/user-registration-sequence-dark.md) | [user-registration-sequence-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/user-registration-sequence-light.md) | [user-registration-sequence-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/user-registration-sequence-dark.puml) | [user-registration-sequence-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/user-registration-sequence-light.puml) |
| Team Creation Sequence | Sequence | Team creation process | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [team-creation-sequence-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/team-creation-sequence-dark.md) | [team-creation-sequence-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/team-creation-sequence-light.md) | [team-creation-sequence-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/team-creation-sequence-dark.puml) | [team-creation-sequence-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/team-creation-sequence-light.puml) |
| Post Creation Sequence | Sequence | Post creation process | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [post-creation-sequence-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/post-creation-sequence-dark.md) | [post-creation-sequence-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/post-creation-sequence-light.md) | [post-creation-sequence-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/post-creation-sequence-dark.puml) | [post-creation-sequence-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/post-creation-sequence-light.puml) |
| Filament Admin Panel | Flowchart | Filament admin panel structure | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-admin-panel-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-admin-panel-dark.md) | [filament-admin-panel-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-admin-panel-light.md) | [filament-admin-panel-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-admin-panel-dark.puml) | [filament-admin-panel-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-admin-panel-light.puml) |
| Filament Resources | Flowchart | Filament resources structure | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-resources-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-resources-dark.md) | [filament-resources-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-resources-light.md) | [filament-resources-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-resources-dark.puml) | [filament-resources-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-resources-light.puml) |
| Event Sourcing Flow (Mermaid) | Flowchart | Event sourcing implementation | [050-implementation.md](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/050-implementation.md) | [event-sourcing-flow-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/event-sourcing-flow-dark.md) | [event-sourcing-flow-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/event-sourcing-flow-light.md) | [event-sourcing-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/event-sourcing-flow-dark.puml) | [event-sourcing-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/event-sourcing-flow-light.puml) |
| TAD Architecture | Flowchart | Technical architecture diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-architecture-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-architecture-dark.md) | [tad-architecture-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-architecture-light.md) | [tad-architecture-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-architecture-dark.puml) | [tad-architecture-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-architecture-light.puml) |
| TAD Database Schema | ERD | Database schema diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-database-schema-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-database-schema-dark.md) | [tad-database-schema-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-database-schema-light.md) | [tad-database-schema-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-database-schema-dark.puml) | [tad-database-schema-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-database-schema-light.puml) |
| TAD Authentication Flow | Sequence | Authentication flow diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-authentication-flow-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-authentication-flow-dark.md) | [tad-authentication-flow-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-authentication-flow-light.md) | [tad-authentication-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-authentication-flow-dark.puml) | [tad-authentication-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-authentication-flow-light.puml) |
| TAD Request Lifecycle | Sequence | Request lifecycle diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-request-lifecycle-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-request-lifecycle-dark.md) | [tad-request-lifecycle-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-request-lifecycle-light.md) | [tad-request-lifecycle-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-request-lifecycle-dark.puml) | [tad-request-lifecycle-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-request-lifecycle-light.puml) |
| TAD Event Flow | Flowchart | Event flow diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-event-flow-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-event-flow-dark.md) | [tad-event-flow-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-event-flow-light.md) | [tad-event-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-event-flow-dark.puml) | [tad-event-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-event-flow-light.puml) |
| TAD Deployment | Deployment | Deployment diagram | [030-ela-tad.md](/_root/docs/E_L_A/030-ela-tad.md) | [tad-deployment-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-deployment-dark.md) | [tad-deployment-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/tad-deployment-light.md) | [tad-deployment-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/tad-deployment-dark.puml) | [tad-deployment-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/tad-deployment-light.puml) |
| Project Roadmap | Gantt | Project roadmap timeline | [020-ela-project-roadmap.md](/_root/docs/E_L_A/020-ela-project-roadmap.md) | [project-roadmap-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/project-roadmap-dark.md) | [project-roadmap-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/project-roadmap-light.md) | [project-roadmap-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/project-roadmap-dark.puml) | [project-roadmap-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/project-roadmap-light.puml) |
| Documentation Style Guide | Flowchart | Documentation structure | [220-ela-documentation-style-guide-v1.2.0.md](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md) | [documentation-style-guide-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/documentation-style-guide-dark.md) | [documentation-style-guide-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/documentation-style-guide-light.md) | [documentation-style-guide-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/documentation-style-guide-dark.puml) | [documentation-style-guide-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/documentation-style-guide-light.puml) |
| Documentation Workflow | Flowchart | Documentation workflow | [220-ela-documentation-style-guide-v1.2.0.md](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md) | [documentation-workflow-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/documentation-workflow-dark.md) | [documentation-workflow-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/documentation-workflow-light.md) | [documentation-workflow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/documentation-workflow-dark.puml) | [documentation-workflow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/documentation-workflow-light.puml) |
| Documentation Components | Flowchart | Documentation components | [220-ela-documentation-style-guide-v1.2.0.md](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md) | [documentation-components-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/documentation-components-dark.md) | [documentation-components-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/documentation-components-light.md) | [documentation-components-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/documentation-components-dark.puml) | [documentation-components-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/documentation-components-light.puml) |
| Filament Admin Panel Architecture | Flowchart | Filament admin panel architecture | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-admin-panel-architecture-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-admin-panel-architecture-dark.md) | [filament-admin-panel-architecture-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-admin-panel-architecture-light.md) | [filament-admin-panel-architecture-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-admin-panel-architecture-dark.puml) | [filament-admin-panel-architecture-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-admin-panel-architecture-light.puml) |
| Filament Installation Process | Flowchart | Filament installation process | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-installation-process-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-installation-process-dark.md) | [filament-installation-process-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-installation-process-light.md) | [filament-installation-process-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-installation-process-dark.puml) | [filament-installation-process-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-installation-process-light.puml) |
| Event Sourcing Components | Flowchart | Event sourcing components | [050-implementation.md](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/050-implementation.md) | [event-sourcing-components-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/event-sourcing-components-dark.md) | [event-sourcing-components-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/event-sourcing-components-light.md) | [event-sourcing-components-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/event-sourcing-components-dark.puml) | [event-sourcing-components-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/event-sourcing-components-light.puml) |
| User Registration and Authentication | Sequence | User registration and authentication process | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [user-registration-authentication-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/user-registration-authentication-dark.md) | [user-registration-authentication-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/user-registration-authentication-light.md) | [user-registration-authentication-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/user-registration-authentication-dark.puml) | [user-registration-authentication-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/user-registration-authentication-light.puml) |
| Team Creation and Management | Sequence | Team creation and management process | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [team-creation-management-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/team-creation-management-dark.md) | [team-creation-management-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/team-creation-management-light.md) | [team-creation-management-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/team-creation-management-dark.puml) | [team-creation-management-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/team-creation-management-light.puml) |
| Post Creation and Publishing | Sequence | Post creation and publishing process | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [post-creation-publishing-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/post-creation-publishing-dark.md) | [post-creation-publishing-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/post-creation-publishing-light.md) | [post-creation-publishing-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/post-creation-publishing-dark.puml) | [post-creation-publishing-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/post-creation-publishing-light.puml) |
| Technology Stack | Flowchart | Technology stack overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [technology-stack-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/technology-stack-dark.md) | [technology-stack-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/technology-stack-light.md) | [technology-stack-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/technology-stack-dark.puml) | [technology-stack-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/technology-stack-light.puml) |
| Business Value | Flowchart | Business value overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [business-value-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/business-value-dark.md) | [business-value-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/business-value-light.md) | [business-value-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/business-value-dark.puml) | [business-value-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/business-value-light.puml) |
| Risk Assessment | Quadrant | Risk assessment matrix | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [risk-assessment-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/risk-assessment-dark.md) | [risk-assessment-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/risk-assessment-light.md) | [risk-assessment-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/risk-assessment-dark.puml) | [risk-assessment-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/risk-assessment-light.puml) |
| Success Metrics | Flowchart | Success metrics overview | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [success-metrics-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/success-metrics-dark.md) | [success-metrics-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/success-metrics-light.md) | [success-metrics-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/success-metrics-dark.puml) | [success-metrics-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/success-metrics-light.puml) |
| Project Overview | Flowchart | Project overview diagram | [005-ela-executive-summary.md](/_root/docs/E_L_A/005-ela-executive-summary.md) | [project-overview-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/project-overview-dark.md) | [project-overview-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/project-overview-light.md) | [project-overview-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/project-overview-dark.puml) | [project-overview-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/project-overview-light.puml) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Implementation Details</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Mermaid File Paths | PlantUML File Paths |
| --- | --- | --- | --- | --- | --- |
| **Dark** | **Light** | **Dark** | **Light** |
| Filament Admin Panel | Flowchart | Filament admin panel structure | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-admin-panel-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-admin-panel-dark.md) | [filament-admin-panel-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-admin-panel-light.md) | [filament-admin-panel-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-admin-panel-dark.puml) | [filament-admin-panel-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-admin-panel-light.puml) |
| Filament Admin Panel Architecture | Flowchart | Filament admin panel architecture | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-admin-panel-architecture-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-admin-panel-architecture-dark.md) | [filament-admin-panel-architecture-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-admin-panel-architecture-light.md) | [filament-admin-panel-architecture-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-admin-panel-architecture-dark.puml) | [filament-admin-panel-architecture-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-admin-panel-architecture-light.puml) |
| Filament Resources | Flowchart | Filament resources structure | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-resources-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-resources-dark.md) | [filament-resources-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-resources-light.md) | [filament-resources-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-resources-dark.puml) | [filament-resources-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-resources-light.puml) |
| Filament Installation Process | Flowchart | Filament installation process | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-installation-process-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-installation-process-dark.md) | [filament-installation-process-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-installation-process-light.md) | [filament-installation-process-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-installation-process-dark.puml) | [filament-installation-process-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-installation-process-light.puml) |
| Event Sourcing Flow (Mermaid) | Flowchart | Event sourcing implementation | [050-implementation.md](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/050-implementation.md) | [event-sourcing-flow-enhanced-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/event-sourcing-flow-enhanced-dark.md) | [event-sourcing-flow-enhanced-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/event-sourcing-flow-enhanced-light.md) | [event-sourcing-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/event-sourcing-flow-dark.puml) | [event-sourcing-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/event-sourcing-flow-light.puml) |
| Event Sourcing Components | Flowchart | Event sourcing components | [050-implementation.md](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/050-implementation.md) | [event-sourcing-components-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/event-sourcing-components-dark.md) | [event-sourcing-components-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/event-sourcing-components-light.md) | [event-sourcing-components-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/event-sourcing-components-dark.puml) | [event-sourcing-components-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/event-sourcing-components-light.puml) |
| Migration Sequence | Sequence | Database migration sequence | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [migration-sequence-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/migration-sequence-dark.md) | [migration-sequence-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/migration-sequence-light.md) | [migration-sequence-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/migration-sequence-dark.puml) | [migration-sequence-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/migration-sequence-light.puml) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Documentation</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Mermaid File Paths | PlantUML File Paths |
| --- | --- | --- | --- | --- | --- |
| **Dark** | **Light** | **Dark** | **Light** |
| Filament Admin Panel | Flowchart | Filament admin panel structure | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-admin-panel-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-admin-panel-dark.md) | [filament-admin-panel-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-admin-panel-light.md) | [filament-admin-panel-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-admin-panel-dark.puml) | [filament-admin-panel-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-admin-panel-light.puml) |
| Filament Admin Panel Architecture | Flowchart | Filament admin panel architecture | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-admin-panel-architecture-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-admin-panel-architecture-dark.md) | [filament-admin-panel-architecture-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-admin-panel-architecture-light.md) | [filament-admin-panel-architecture-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-admin-panel-architecture-dark.puml) | [filament-admin-panel-architecture-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-admin-panel-architecture-light.puml) |
| Filament Resources | Flowchart | Filament resources structure | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-resources-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-resources-dark.md) | [filament-resources-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-resources-light.md) | [filament-resources-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-resources-dark.puml) | [filament-resources-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-resources-light.puml) |
| Filament Installation Process | Flowchart | Filament installation process | [100-070-filament-configuration.md](/_root/docs/E_L_A/100-implementation-plan/030-core-components/040-filament-configuration.md) | [filament-installation-process-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/filament-installation-process-dark.md) | [filament-installation-process-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/filament-installation-process-light.md) | [filament-installation-process-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/filament-installation-process-dark.puml) | [filament-installation-process-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/filament-installation-process-light.puml) |
| Event Sourcing Flow (Mermaid) | Flowchart | Event sourcing implementation | [050-implementation.md](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/050-implementation.md) | [event-sourcing-flow-enhanced-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/event-sourcing-flow-enhanced-dark.md) | [event-sourcing-flow-enhanced-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/event-sourcing-flow-enhanced-light.md) | [event-sourcing-flow-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/event-sourcing-flow-dark.puml) | [event-sourcing-flow-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/event-sourcing-flow-light.puml) |
| Event Sourcing Components | Flowchart | Event sourcing components | [050-implementation.md](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/050-implementation.md) | [event-sourcing-components-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/event-sourcing-components-dark.md) | [event-sourcing-components-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/event-sourcing-components-light.md) | [event-sourcing-components-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/event-sourcing-components-dark.puml) | [event-sourcing-components-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/event-sourcing-components-light.puml) |
| Migration Sequence | Sequence | Database migration sequence | [100-610-enhanced-diagrams.md](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [migration-sequence-dark.md](/_root/docs/E_L_A/illustrations/mermaid/dark/migration-sequence-dark.md) | [migration-sequence-light.md](/_root/docs/E_L_A/illustrations/mermaid/light/migration-sequence-light.md) | [migration-sequence-dark.puml](/_root/docs/E_L_A/illustrations/plantuml/dark/migration-sequence-dark.puml) | [migration-sequence-light.puml](/_root/docs/E_L_A/illustrations/plantuml/light/migration-sequence-light.puml) |
\n</details>\n

</details>

## Diagram Types

<details>
<summary><strong style="font-size: 1.2em;">Flowcharts</strong></summary>

Flowcharts visualize processes, workflows, and relationships between components. They are used for:

- Architecture overviews
- System component relationships
- Process flows
- Feature relationships

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| Event Sourcing Flow | Flowchart | Event sourcing implementation flow | [Event Sourcing Overview](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/010-overview.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/event-sourcing-flow-enhanced-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/event-sourcing-flow-enhanced-light.md) | [Interactive](/_root/docs/E_L_A/illustrations/interactive/event-sourcing-flow-interactive.html) \\| [Animated](/_root/docs/E_L_A/illustrations/animated/event-sourcing-flow-animated.html) |
\n</details>\n

<p><strong>Interactive Diagrams:</strong> The interactive version of the Event Sourcing Flow diagram provides additional features such as component highlighting, tooltips, and detailed information panels. It also includes accessibility features like keyboard shortcuts and font size controls. See the [Interactive Diagrams README](/_root/docs/E_L_A/illustrations/interactive/README.md) for more information.</p>

<p><strong>Animated Diagrams:</strong> The animated version of the Event Sourcing Flow diagram provides a step-by-step animation showing the flow of events through the system. It includes controls for playing, pausing, and stepping through the animation, as well as detailed descriptions for each step. It also includes accessibility features like keyboard shortcuts and animation disabling options. See the [Animated Diagrams README](/_root/docs/E_L_A/illustrations/animated/README.md) for more information.</p>

</details>

<details>
<summary><strong style="font-size: 1.2em;">Entity Relationship Diagrams (ERD)</strong></summary>

ERD diagrams show database structure and relationships between entities. They are used for:

- Database schema visualization
- Entity relationships and cardinality
- Data model documentation

</details>

<details>
<summary><strong style="font-size: 1.2em;">Sequence Diagrams</strong></summary>

Sequence diagrams illustrate interactions between components over time. They are used for:

- Authentication flows
- Request lifecycles
- User registration processes
- API interactions

</details>

<details>
<summary><strong style="font-size: 1.2em;">Class Diagrams</strong></summary>

Class diagrams show the structure of classes, interfaces, and their relationships. They are used for:

- Object-oriented design documentation
- Class hierarchies and inheritance
- Method and property visualization

</details>

<details>
<summary><strong style="font-size: 1.2em;">State Diagrams</strong></summary>

State diagrams illustrate the different states an object can be in and the transitions between states. They are used
for:

- Aggregate state transitions
- User status workflows
- Content publishing processes
- Todo item state transitions

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| User Aggregate States | State | User aggregate state transitions | [User Aggregate](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/020-010-user-aggregate.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/user-aggregate-states-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/user-aggregate-states-light.md) |  |
| Team Aggregate States | State | Team aggregate state transitions | [Team Aggregate](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/020-020-team-aggregate.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/team-aggregate-states-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/team-aggregate-states-light.md) | [Interactive](/_root/docs/E_L_A/illustrations/interactive/team-aggregate-states-interactive.html) \\| [Animated](/_root/docs/E_L_A/illustrations/animated/team-aggregate-states-animated.html) |
| Post Aggregate States | State | Post aggregate state transitions | [Post Aggregate](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/020-030-post-aggregate.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/post-aggregate-states-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/post-aggregate-states-light.md) | [Interactive](/_root/docs/E_L_A/illustrations/interactive/post-aggregate-states-interactive.html) \\| [Animated](/_root/docs/E_L_A/illustrations/animated/post-aggregate-states-animated.html) |
| Todo Aggregate States | State | Todo aggregate state transitions | [Todo Aggregate](/_root/docs/E_L_A/100-implementation-plan/100-350-event-sourcing/020-040-todo-aggregate.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/todo-aggregate-states-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/todo-aggregate-states-light.md) | [Interactive](/_root/docs/E_L_A/illustrations/interactive/todo-aggregate-states-interactive.html) \\| [Animated](/_root/docs/E_L_A/illustrations/animated/todo-aggregate-states-animated.html) |
| Todo State Machine | State | Todo item state transitions | [Status Implementation](/_root/docs/E_L_A/100-implementation-plan/100-370-status-implementation.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/todo-state-machine-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/todo-state-machine-light.md) |  |
\n</details>\n

<p><strong>Interactive Diagrams:</strong> The interactive versions of these diagrams provide additional features such as state highlighting, transition visualization, and detailed information panels. They also include accessibility features like keyboard shortcuts and font size controls. See the [Interactive Diagrams README](/_root/docs/E_L_A/illustrations/interactive/README.md) for more information.</p>

<p><strong>Animated Diagrams:</strong> The animated versions of these diagrams provide step-by-step animations showing the state transitions in sequence. They include controls for playing, pausing, and stepping through the animation, as well as detailed descriptions for each step. They also include accessibility features like keyboard shortcuts and animation disabling options. See the [Animated Diagrams README](/_root/docs/E_L_A/illustrations/animated/README.md) for more information.</p>

</details>

<details>
<summary><strong style="font-size: 1.2em;">Gantt Charts</strong></summary>

Gantt charts visualize project schedules and timelines. They are used for:

- Project roadmaps
- Implementation timelines
- Resource allocation planning

</details>

<details>
<summary><strong style="font-size: 1.2em;">Deployment Diagrams</strong></summary>

Deployment diagrams show the physical deployment of artifacts on nodes. They are used for:

- System infrastructure visualization
- Server configurations
- Component distribution across hardware

</details>

---

## Diagram Tags

Diagrams in this index are tagged with relevant keywords to make them easier to find. The following tag categories are
used throughout the document:

### Functional Tags

- `auth` - Authentication and authorization
- `user` - User management and profiles
- `team` - Team creation and management
- `content` - Content creation and management
- `notification` - Notification systems
- `search` - Search functionality
- `analytics` - Analytics and reporting

### Technical Tags

- `database` - Database structure and relationships
- `api` - API endpoints and interactions
- `security` - Security features and processes
- `performance` - Performance optimization
- `cache` - Caching mechanisms
- `queue` - Queue processing
- `storage` - File storage and management

### Process Tags

- `workflow` - Process workflows
- `sequence` - Sequential processes
- `state` - State transitions
- `lifecycle` - Object lifecycles
- `deployment` - Deployment processes
- `testing` - Testing processes

### Tag Cloud

<div style="line-height: 2.5; padding: 15px; background-color: var(--card-bg); border-radius: 5px; border: 1px solid var(--card-border); color: var(--card-text);">
  <a href="#">auth</a>
  <a href="#">user</a>
  <a href="#">team</a>
  <a href="#">content</a>
  <a href="#">database</a>
  <a href="#">api</a>
  <a href="#">workflow</a>
  <a href="#">security</a>
  <a href="#">performance</a>
  <a href="#">sequence</a>
  <a href="#">state</a>
  <a href="#">notification</a>
  <a href="#">search</a>
  <a href="#">analytics</a>
</div>

<script>
function searchByTag(tag) {
  // Get the search input and set its value to the tag
  const searchInput = document.getElementById('diagram-search');
  if (searchInput) {
    // Set the value
    searchInput.value = tag;

    // Scroll to the top of the page where the search box is located
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    // Highlight the search box to draw attention to it
    searchInput.style.transition = 'background-color 0.3s';
    searchInput.style.backgroundColor = '#ffffcc';

    // Trigger the search after a short delay to ensure the scroll completes
    setTimeout(() => {
      const searchButton = document.getElementById('search-button');
      if (searchButton) {
        searchButton.click();
      } else {
        // Fallback if button not found - try to call global search function
        if (typeof globalPerformSearch === 'function') {
          globalPerformSearch();
        } else {
          console.error('Search function not available');
        }
      }

      // Reset the highlight after a moment
      setTimeout(() => {
        searchInput.style.backgroundColor = '';
      }, 1000);
    }, 500);
  } else {
    // Fallback if search input not found
    alert(`Search for: ${tag}\n\nThe search input element was not found. Please search manually.`);
  }

  // Prevent default link behavior
  return false;
}
</script>

## Search Tips

This document includes an interactive diagram search tool at the top of the page. You can also use your browser's search
function (usually Ctrl+F or Cmd+F) with these search strategies:

### Basic Search Strategies

1. **Search by Topic**: Enter keywords related to the topic (e.g., "authentication", "database", "workflow")
2. **Search by Diagram Type**: Enter the type of diagram you need (e.g., "flowchart", "sequence", "ERD")
3. **Search by Source Document**: Enter the document name (e.g., "TAD", "executive summary")
4. **Search by File Path**: If you know part of the file path, enter it (e.g., "dark.md", "plantuml")
5. **Search by Tags**: Look for code-formatted tags like `database` or `auth` in the Quick Reference section

### Advanced Search Techniques

- **Combine Search Terms**: Use multiple keywords to narrow down results (e.g., "sequence authentication")
- **Use Partial Words**: Try searching for parts of words to catch variations (e.g., "auth" will find "authentication",
  "authorize", etc.)
- **Search for File Extensions**: Use ".md" or ".puml" to find specific file formats
- **Look for Document Numbers**: Search for document numbers like "100-410" to find diagrams from specific
  implementation documents

### If You Can't Find a Diagram

1. Check the source documents directly by following the links in the tables
2. Look in the [Diagram Types](#diagram-types) section for similar diagrams
3. Consider creating a new diagram following the project's diagramming standards (see
   [Contributing New Diagrams](#contributing-new-diagrams))
4. Ask the documentation team if you believe a diagram should exist but can't locate it

## Contributing New Diagrams

When adding new diagrams to the project, please follow these guidelines:

### Preparation

1. **Identify the Need**: Determine what aspect of the project needs visual representation
2. **Choose the Right Type**: Select the appropriate diagram type (flowchart, sequence, ERD, etc.) for your content
3. **Review Existing Diagrams**: Check if similar diagrams already exist that could be extended or modified

### Creation Process

1. **Create Both Formats**: Generate both Mermaid and PlantUML versions of your diagram
2. **Create Both Themes**: Provide both dark and light mode variants
3. **Follow Naming Conventions**: Use the established naming pattern (see
   [Diagram Naming Conventions](#diagram-naming-conventions))
4. **Place Files Correctly**: Save Mermaid files in `mermaid/dark/` or `mermaid/light/` directories, and PlantUML files
   in `plantuml/dark/` or `plantuml/light/` directories

### Documentation

1. **Update This Index**: Add your new diagram to the appropriate section(s) of this index
2. **Reference in Source**: Include a reference to your diagram in the relevant source document
3. **Add Tags**: Include relevant tags to make your diagram searchable
4. **Provide Context**: Ensure your diagram has a clear description that explains its purpose
5. **Generate Thumbnails**: Create thumbnail images for your diagrams (see below)

### Thumbnail Generation

1. **Create Thumbnails**: Generate small preview images (120-150px wide) of your diagrams
2. **Use Consistent Naming**: Name thumbnails with the pattern `[diagram-name]-thumb.png`
3. **Store in Thumbnails Directory**: Save thumbnails in the `thumbnails` directory
4. **Include in Index**: Add thumbnail references to the index tables where appropriate

#### Thumbnail Generation Commands

For Mermaid diagrams:

```bash
# Install mermaid-cli if not already installed
npm install -g @mermaid-js/mermaid-cli

# Generate PNG from Mermaid file
mmdc -i mermaid/light/diagram-name-light.md -o thumbnails/diagram-name-thumb.png -w 400

# Resize to thumbnail size (requires ImageMagick)
convert thumbnails/diagram-name-thumb.png -resize 120x thumbnails/diagram-name-thumb.png
```text

For PlantUML diagrams:

```bash
# Generate PNG from PlantUML file
java -jar plantuml.jar -tpng plantuml/light/diagram-name-light.puml -o ../thumbnails

# Resize to thumbnail size (requires ImageMagick)
convert thumbnails/diagram-name-light.png -resize 120x thumbnails/diagram-name-thumb.png
```php
### Quality Assurance

1. **Verify Accuracy**: Ensure your diagram accurately represents the system or process
2. **Check Readability**: Make sure text is legible and the diagram isn't overcrowded
3. **Test Dark/Light Modes**: Verify that both dark and light variants are properly formatted and readable

For detailed guidance on creating diagrams, refer to the
[Documentation Style Guide](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md).

## Diagram Best Practices

Follow these best practices to create effective diagrams for the Enhanced Laravel Application documentation:

### Clarity and Simplicity

- **Focus on One Concept**: Each diagram should illustrate a single concept or process
- **Limit Detail Level**: Include only the details necessary to understand the concept
- **Use Consistent Terminology**: Match terms used in the diagram with those in the text
- **Provide Context**: Include a title and brief description that explains the diagram's purpose

### Visual Design

- **Use Consistent Colors**: Follow the project's color scheme for different components
- **Ensure Readability**: Use legible font sizes and sufficient contrast
- **Balance Whitespace**: Avoid overcrowding elements in the diagram
- **Use Directional Flow**: For flowcharts and sequence diagrams, maintain a clear directional flow (typically
  top-to-bottom or left-to-right)

### Technical Considerations

- **Optimize for Both Themes**: Ensure diagrams are readable in both dark and light modes
- **Test in Different Viewers**: Verify that diagrams render correctly in different Markdown viewers
- **Keep Source Files**: Maintain editable source files for future updates
- **Version Control**: Include diagrams in version control along with documentation

### Accessibility

- **Use Text Labels**: Include text labels for all visual elements
- **Provide Alt Text**: When embedding diagrams in HTML, include descriptive alt text
- **Consider Color Blindness**: Ensure diagrams are understandable without relying solely on color
- **Offer Text Alternatives**: Provide text descriptions of complex diagrams in the documentation

## Diagram Accessibility

Creating accessible diagrams ensures that all users, including those with disabilities, can understand and benefit from
the visual information. Follow these detailed guidelines to make your diagrams more accessible:

### Visual Accessibility

#### Color Considerations

- **Use High Contrast**: Maintain a contrast ratio of at least 4.5:1 between text and background
- **Don't Rely Solely on Color**: Use patterns, shapes, or labels in addition to color to convey information
- **Test with Color Blindness Simulators**: Verify your diagrams are readable with tools like
  [Coblis](https:/www.color-blindness.com/coblis-color-blindness-simulator)
- **Use Colorblind-Friendly Palettes**: Choose from pre-tested color schemes that work for various types of color
  blindness

#### Text Readability

- **Use Adequate Font Size**: Text should be at least 12pt equivalent
- **Choose Clear Fonts**: Use sans-serif fonts like Arial, Helvetica, or Calibri
- **Limit Text Density**: Avoid overcrowding diagrams with excessive text
- **Maintain Spacing**: Ensure adequate space between elements and text

### Alternative Text

#### Writing Effective Alt Text

- **Be Concise but Complete**: Describe the purpose and content of the diagram in 1-2 sentences
- **Focus on Information**: Convey the information the diagram is meant to communicate, not just its visual appearance
- **Include Key Data Points**: Mention important numbers, trends, or relationships
- **Describe Structure**: For complex diagrams, explain the overall structure (e.g., "A flowchart showing the
  authentication process with 5 steps")

#### Implementation

- **In Markdown**: Use the alt text attribute in image tags: `![Alt text description](image-url)`
- **In HTML**: Include both alt and title attributes:
  `<img src="diagram.png" alt="Detailed description" title="Brief title">`
- **In Documentation**: Add a text description below complex diagrams

### Text Alternatives

#### For Complex Diagrams

- **Provide Textual Summaries**: Include a paragraph that explains the key information in the diagram
- **Use Lists for Steps**: For process diagrams, include a numbered list of steps
- **Create Data Tables**: For charts and graphs, include the data in tabular format
- **Link to Detailed Descriptions**: For very complex diagrams, link to a more detailed textual explanation

#### For Interactive Elements

- **Ensure Keyboard Navigation**: Make sure interactive diagrams can be navigated with keyboard alone
- **Provide Non-Interactive Alternatives**: Offer static versions of interactive diagrams
- **Include Screen Reader Instructions**: Add specific guidance for screen reader users

By following these accessibility guidelines, you ensure that your diagrams are usable by the widest possible audience,
including people with visual impairments, cognitive disabilities, and those using assistive technologies.

## Diagram Versioning and History

To maintain a clear record of diagram evolution and ensure consistency across documentation, follow these versioning
guidelines:

### Version Tracking

- **Include Version Information**: Add a version number or date in the diagram source file as a comment
- **Document Major Changes**: When significantly updating a diagram, document the changes in the source document
- **Maintain Changelog**: For critical diagrams, maintain a brief changelog in the source file comments
- **Use Git History**: Leverage Git history to track changes to diagram files over time

### Handling Updates

- **Update All Formats**: When updating a diagram, update both Mermaid and PlantUML versions
- **Update Both Themes**: Ensure both dark and light mode variants are updated consistently
- **Update References**: Check and update any references to the diagram in documentation
- **Notify Stakeholders**: For significant changes to key diagrams, notify relevant stakeholders

### Archiving

- **Archive Old Versions**: For major revisions, consider archiving the old version in an `.archive` folder
- **Use Clear Naming**: If keeping old versions, use clear naming with version numbers or dates
- **Update Index**: Ensure this index references the current version of each diagram
- **Remove Obsolete Diagrams**: Remove references to obsolete diagrams that are no longer relevant

## Diagram Tools and Resources

The following tools and resources are recommended for creating and editing diagrams for the Enhanced Laravel Application
documentation:

### Mermaid Tools

- **[Mermaid Live Editor](https:/mermaid.live)**: Online editor for creating and previewing Mermaid diagrams
- **[Mermaid CLI](https:/github.com/mermaid-js/mermaid-cli)**: Command-line tool for generating Mermaid diagrams
- **[Mermaid Documentation](https:/mermaid.js.org/intro)**: Official documentation for Mermaid syntax
- **VS Code Extensions**:
  - [Mermaid Preview](https:/marketplace.visualstudio.com/items?itemName=bierner.markdown-mermaid)
  - [Mermaid Markdown Syntax Highlighting](https:/marketplace.visualstudio.com/items?itemName=bpruitt-goddard.mermaid-markdown-syntax-highlighting)

### PlantUML Tools

- **[PlantUML Online Server](https:/www.plantuml.com/plantuml/uml)**: Online editor for creating and previewing
  PlantUML diagrams
- **[PlantUML CLI](https:/plantuml.com/command-line)**: Command-line tool for generating PlantUML diagrams
- **[PlantUML Documentation](https:/plantuml.com)**: Official documentation for PlantUML syntax
- **VS Code Extensions**:
  - [PlantUML](https:/marketplace.visualstudio.com/items?itemName=jebbs.plantuml)
  - [PlantUML Previewer](https:/marketplace.visualstudio.com/items?itemName=achil.vscode-plantuml)

### Other Useful Resources

- **[Diagram Templates](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md#diagram-020-templates)**: Standard 020-templates for
  common diagram types
- **[Color Schemes](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md#color-schemes)**: Recommended color schemes for dark
  and light mode diagrams
- **[Icon Libraries](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md#icon-libraries)**: Recommended icon sets for use in
  diagrams
- **[Accessibility Guidelines](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md#accessibility)**: Guidelines for creating
  accessible diagrams

## Diagram Templates

To ensure consistency across the documentation, use these starter 020-templates when creating new diagrams. Each template
includes the basic structure and styling for both dark and light modes.

### Flowchart Template

#### Mermaid (Light Mode)

```mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process 1]
    B -->|No| D[Process 2]
    C --> E[End]
    D --> E

    classDef default fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef highlight fill:#d4f4ff,stroke:#0077b6,stroke-width:2px;
    class A,E default;
    class B highlight;
```text

#### Mermaid (Dark Mode)

```mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Process 1]
    B -->|No| D[Process 2]
    C --> E[End]
    D --> E

    classDef default fill:#2d2d2d,stroke:#c9c9c9,stroke-width:1px,color:#e0e0e0;
    classDef highlight fill:#1e3a5f,stroke:#4dabf7,stroke-width:2px,color:#ffffff;
    class A,E default;
    class B highlight;
```sql
### Sequence Diagram Template

#### Mermaid (Light Mode)

```mermaid
sequenceDiagram
    participant User
    participant System
    participant Database

    User->>System: Request Data
    activate System
    System->>Database: Query Data
    activate Database
    Database-->>System: Return Results
    deactivate Database
    System-->>User: Display Results
    deactivate System
```text

#### Mermaid (Dark Mode)

```mermaid
sequenceDiagram
    participant User
    participant System
    participant Database

    User->>System: Request Data
    activate System
    System->>Database: Query Data
    activate Database
    Database-->>System: Return Results
    deactivate Database
    System-->>User: Display Results
    deactivate System

    %%{ init: { 'theme': 'dark' } }%%
```html
### ERD Template

#### Mermaid (Light Mode)

```mermaid
erDiagram
    USER {
        string id PK
        string name
        string email
    }
    PROFILE {
        string id PK
        string user_id FK
        string bio
    }
    POST {
        string id PK
        string user_id FK
        string title
        string content
    }

    USER ||--o{ POST : creates
    USER ||--|| PROFILE : has
```text

#### Mermaid (Dark Mode)

```mermaid
erDiagram
    USER {
        string id PK
        string name
        string email
    }
    PROFILE {
        string id PK
        string user_id FK
        string bio
    }
    POST {
        string id PK
        string user_id FK
        string title
        string content
    }

    USER ||--o{ POST : creates
    USER ||--|| PROFILE : has

    %%{ init: { 'theme': 'dark' } }%%
```php
For more 020-templates and detailed styling guidelines, refer to the
[Documentation Style Guide](/_root/docs/E_L_A/220-ela-documentation-style-guide-v1.2.0.md#diagram-020-templates).

## Feature-Based Diagram Index

This section organizes diagrams by application feature to help you find diagrams related to specific functionality.

<details>
<summary><strong style="font-size: 1.2em;">Authentication & Authorization</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| Authentication Flow | Sequence | User authentication process | [Technical Architecture Document](/_root/docs/E_L_A/030-ela-tad.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/authentication-flow-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/authentication-flow-light.md) |
| User Registration Sequence | Sequence | User registration process | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/user-registration-sequence-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/user-registration-sequence-light.md) |
| TAD Authentication Flow | Sequence | Authentication flow diagram | [Technical Architecture Document](/_root/docs/E_L_A/030-ela-tad.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/tad-authentication-flow-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/tad-authentication-flow-light.md) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">User Management</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| User Registration and Authentication | Sequence | User registration and authentication process | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/user-registration-authentication-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/user-registration-authentication-light.md) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Team Management</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| Team Creation Sequence | Sequence | Team creation process | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/team-creation-sequence-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/team-creation-sequence-light.md) |
| Team Creation and Management | Sequence | Team creation and management process | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/team-creation-management-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/team-creation-management-light.md) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Content Management</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| Post Creation Sequence | Sequence | Post creation process | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/post-creation-sequence-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/post-creation-sequence-light.md) |
| Post Creation and Publishing | Sequence | Post creation and publishing process | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/post-creation-publishing-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/post-creation-publishing-light.md) |
| Todo State Machine | State | Todo item state transitions | [Status Implementation](/_root/docs/E_L_A/100-implementation-plan/100-370-status-implementation.md) | [Dark](/_root/docs/E_L_A/illustrations/mermaid/dark/todo-state-machine-dark.md) | [Light](/_root/docs/E_L_A/illustrations/mermaid/light/todo-state-machine-light.md) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Notifications</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| Notification Flow | Sequence | Notification delivery process | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](mermaid/dark/notification-flow-dark.md) | [Light](mermaid/light/notification-flow-light.md) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Search & Discovery</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| Search Flow | Sequence | Search process and indexing | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](mermaid/dark/search-flow-dark.md) | [Light](mermaid/light/search-flow-light.md) |
\n</details>\n

</details>

<details>
<summary><strong style="font-size: 1.2em;">Analytics & Reporting</strong></summary>

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Description | Source Document | Links |
| --- | --- | --- | --- | --- |
| Analytics Dashboard Flow | Flowchart | Analytics data collection and display | [Enhanced Diagrams](/_root/docs/E_L_A/100-implementation-plan/100-610-enhanced-diagrams.md) | [Dark](mermaid/dark/analytics-dashboard-flow-dark.md) | [Light](mermaid/light/analytics-dashboard-flow-light.md) |
\n</details>\n

</details>

## Diagram Statistics

This section provides an overview of the diagrams in the Enhanced Laravel Application documentation.

### Diagrams by Type

<div style="display: flex; justify-content: space-around; flex-wrap: wrap; margin-bottom: 20px;">
  <div style="min-width: 200px; margin: 10px; padding: 15px; background-color: var(--search-bg, #f8f9fa); border-radius: 5px; border: 1px solid var(--search-border, #ddd); text-align: center;">
    <h4 style="margin-top: 0;">Flowcharts</h4>
    <div style="font-size: 2em; font-weight: bold; margin: 10px 0;">12</div>
    <div style="color: #666;">28% of all diagrams</div>
  </div>
  <div style="min-width: 200px; margin: 10px; padding: 15px; background-color: var(--search-bg, #f8f9fa); border-radius: 5px; border: 1px solid var(--search-border, #ddd); text-align: center;">
    <h4 style="margin-top: 0;">Sequence Diagrams</h4>
    <div style="font-size: 2em; font-weight: bold; margin: 10px 0;">15</div>
    <div style="color: #666;">35% of all diagrams</div>
  </div>
  <div style="min-width: 200px; margin: 10px; padding: 15px; background-color: var(--search-bg, #f8f9fa); border-radius: 5px; border: 1px solid var(--search-border, #ddd); text-align: center;">
    <h4 style="margin-top: 0;">ERD Diagrams</h4>
    <div style="font-size: 2em; font-weight: bold; margin: 10px 0;">8</div>
    <div style="color: #666;">19% of all diagrams</div>
  </div>
  <div style="min-width: 200px; margin: 10px; padding: 15px; background-color: var(--search-bg, #f8f9fa); border-radius: 5px; border: 1px solid var(--search-border, #ddd); text-align: center;">
    <h4 style="margin-top: 0;">Class Diagrams</h4>
    <div style="font-size: 2em; font-weight: bold; margin: 10px 0;">5</div>
    <div style="color: #666;">12% of all diagrams</div>
  </div>
  <div style="min-width: 200px; margin: 10px; padding: 15px; background-color: var(--search-bg, #f8f9fa); border-radius: 5px; border: 1px solid var(--search-border, #ddd); text-align: center;">
    <h4 style="margin-top: 0;">Other Types</h4>
    <div style="font-size: 2em; font-weight: bold; margin: 10px 0;">3</div>
    <div style="color: #666;">7% of all diagrams</div>
  </div>
</div>

### Diagrams by Feature Area

<div style="width: 100%; height: 300px; margin: 20px 0; padding: 15px; background-color: var(--search-bg, #f8f9fa); border-radius: 5px; border: 1px solid var(--search-border, #ddd);">
  <div style="display: flex; height: 100%; align-items: flex-end;">
    <div style="flex: 1; margin: 0 5px; display: flex; flex-direction: column; align-items: center;">
      <div style="width: 100%; background-color: #4dabf7; height: 30%;"></div>
      <div style="margin-top: 10px; text-align: center;">Project Overview</div>
      <div style="font-size: 0.8em; color: #666;">7 diagrams</div>
    </div>
    <div style="flex: 1; margin: 0 5px; display: flex; flex-direction: column; align-items: center;">
      <div style="width: 100%; background-color: #4dabf7; height: 60%;"></div>
      <div style="margin-top: 10px; text-align: center;">Technical Architecture</div>
      <div style="font-size: 0.8em; color: #666;">14 diagrams</div>
    </div>
    <div style="flex: 1; margin: 0 5px; display: flex; flex-direction: column; align-items: center;">
      <div style="width: 100%; background-color: #4dabf7; height: 45%;"></div>
      <div style="margin-top: 10px; text-align: center;">Core Functionality</div>
      <div style="font-size: 0.8em; color: #666;">10 diagrams</div>
    </div>
    <div style="flex: 1; margin: 0 5px; display: flex; flex-direction: column; align-items: center;">
      <div style="width: 100%; background-color: #4dabf7; height: 35%;"></div>
      <div style="margin-top: 10px; text-align: center;">Implementation Details</div>
      <div style="font-size: 0.8em; color: #666;">8 diagrams</div>
    </div>
    <div style="flex: 1; margin: 0 5px; display: flex; flex-direction: column; align-items: center;">
      <div style="width: 100%; background-color: #4dabf7; height: 20%;"></div>
      <div style="margin-top: 10px; text-align: center;">Documentation</div>
      <div style="font-size: 0.8em; color: #666;">4 diagrams</div>
    </div>
  </div>
</div>

### Most Referenced Diagrams

\n<details>\n<summary>Table Details</summary>\n\n| Diagram Name | Type | Referenced In | Usage Count |
| --- | --- | --- | --- |
| Architecture Overview | Flowchart | TAD, PRD, Implementation Plan | 8 |
| ERD Overview | ERD | TAD, Implementation Plan | 6 |
| Authentication Flow | Sequence | TAD, Security Documentation | 5 |
| Project Roadmap | Gantt | Project Roadmap, Executive Summary | 4 |
| Class Diagram (Detailed) | Class | Implementation Plan, Developer Guide | 4 |
\n</details>\n

## Diagram Relationships

This section visualizes how diagrams in the Enhanced Laravel Application documentation relate to each other.
Understanding these relationships can help you navigate between related diagrams and gain a more comprehensive
understanding of the system.

### Diagram Dependency Map

> **Interactive Map**: Click on any diagram node to search for that diagram. Hover over nodes to see full diagram names.

<div style="width: 100%; padding: 20px; background-color: var(--search-bg, #f8f9fa); border-radius: 5px; border: 1px solid var(--search-border, #ddd); overflow: auto;">
  <div style="margin-bottom: 10px; color: var(--diagram-text);">
    <strong>Legend:</strong>
    <span style="display: inline-block; margin-left: 10px; margin-right: 5px; width: 12px; height: 12px; border-radius: 50%; background-color: var(--diagram-flowchart);"></span> Flowchart
    <span style="display: inline-block; margin-left: 10px; margin-right: 5px; width: 12px; height: 12px; border-radius: 50%; background-color: var(--diagram-erd);"></span> ERD
    <span style="display: inline-block; margin-left: 10px; margin-right: 5px; width: 12px; height: 12px; border-radius: 50%; background-color: var(--diagram-sequence);"></span> Sequence
    <span style="display: inline-block; margin-left: 10px; margin-right: 5px; width: 12px; height: 12px; border-radius: 50%; background-color: var(--diagram-class);"></span> Class
    <span style="display: inline-block; margin-left: 10px; margin-right: 5px; width: 12px; height: 12px; border-radius: 50%; background-color: var(--diagram-state);"></span> State
    <span style="display: inline-block; margin-left: 10px; margin-right: 5px; width: 12px; height: 12px; border-radius: 50%; background-color: var(--diagram-gantt);"></span> Gantt
  </div>
  <svg width="800" height="500" id="diagram-relationships" style="max-width: 100%;">
    <!-- SVG content will be generated by JavaScript -->
    <defs>
      <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="var(--diagram-line)" />
      </marker>
    </defs>
    <g id="nodes"></g>
    <g id="links"></g>
  </svg>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Define diagram nodes and their relationships
  const diagrams = [
    { id: 1, name: "Architecture Overview", type: "Flowchart", x: 400, y: 100, radius: 40 },
    { id: 2, name: "ERD Overview", type: "ERD", x: 200, y: 200, radius: 35 },
    { id: 3, name: "Authentication Flow", type: "Sequence", x: 600, y: 200, radius: 35 },
    { id: 4, name: "Class Diagram (Detailed)", type: "Class", x: 300, y: 300, radius: 35 },
    { id: 5, name: "User Registration Sequence", type: "Sequence", x: 500, y: 300, radius: 35 },
    { id: 6, name: "Team Creation Sequence", type: "Sequence", x: 650, y: 350, radius: 30 },
    { id: 7, name: "Post Creation Sequence", type: "Sequence", x: 500, y: 400, radius: 30 },
    { id: 8, name: "Todo State Machine", type: "State", x: 350, y: 400, radius: 30 },
    { id: 9, name: "Project Roadmap", type: "Gantt", x: 200, y: 400, radius: 30 }
  ];

  // Define relationships between diagrams
  const relationships = [
    { source: 1, target: 2, description: "Includes" },
    { source: 1, target: 3, description: "References" },
    { source: 2, target: 4, description: "Details" },
    { source: 3, target: 5, description: "Extends" },
    { source: 5, target: 6, description: "Related" },
    { source: 5, target: 7, description: "Similar pattern" },
    { source: 4, target: 8, description: "Implements" },
    { source: 1, target: 9, description: "Aligns with" }
  ];

  // Get the SVG element and its groups
  const svg = document.getElementById('diagram-relationships');
  const nodesGroup = document.getElementById('nodes');
  const linksGroup = document.getElementById('links');

  // Get colors from CSS variables based on current theme
  const typeColors = {
    "Flowchart": getComputedStyle(document.documentElement).getPropertyValue('--diagram-flowchart').trim(),
    "ERD": getComputedStyle(document.documentElement).getPropertyValue('--diagram-erd').trim(),
    "Sequence": getComputedStyle(document.documentElement).getPropertyValue('--diagram-sequence').trim(),
    "Class": getComputedStyle(document.documentElement).getPropertyValue('--diagram-class').trim(),
    "State": getComputedStyle(document.documentElement).getPropertyValue('--diagram-state').trim(),
    "Gantt": getComputedStyle(document.documentElement).getPropertyValue('--diagram-gantt').trim()
  };

  // Draw the relationships (lines)
  relationships.forEach(rel => {
    const source = diagrams.find(d => d.id === rel.source);
    const target = diagrams.find(d => d.id === rel.target);

    if (source && target) {
      // Calculate the angle between nodes
      const dx = target.x - source.x;
      const dy = target.y - source.y;
      const angle = Math.atan2(dy, dx);

      // Calculate start and end points (on the circle perimeters)
      const startX = source.x + source.radius * Math.cos(angle);
      const startY = source.y + source.radius * Math.sin(angle);
      const endX = target.x - target.radius * Math.cos(angle);
      const endY = target.y - target.radius * Math.sin(angle);

      // Create the line
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', startX);
      line.setAttribute('y1', startY);
      line.setAttribute('x2', endX);
      line.setAttribute('y2', endY);
      line.setAttribute('stroke', 'var(--diagram-line)');
      line.setAttribute('stroke-width', '2');
      line.setAttribute('marker-end', 'url(#arrowhead)');

      // Add relationship description
      const textX = (startX + endX) / 2;
      const textY = (startY + endY) / 2 - 5;
      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', textX);
      text.setAttribute('y', textY);
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('font-size', '12');
      text.setAttribute('fill', 'var(--diagram-text)');
      text.textContent = rel.description;

      // Add to SVG
      linksGroup.appendChild(line);
      linksGroup.appendChild(text);
    }
  });

  // Draw the diagram nodes (circles)
  diagrams.forEach(diagram => {
    // Create the circle
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    circle.setAttribute('cx', diagram.x);
    circle.setAttribute('cy', diagram.y);
    circle.setAttribute('r', diagram.radius);
    circle.setAttribute('fill', typeColors[diagram.type] || '#4dabf7');
    circle.setAttribute('stroke', 'var(--search-border, #ddd)');
    circle.setAttribute('stroke-width', '2');

    // Add interactivity
    circle.setAttribute('data-name', diagram.name);
    circle.setAttribute('data-type', diagram.type);
    circle.style.cursor = 'pointer';
    circle.addEventListener('click', function() {
      // Search for this diagram when clicked
      searchByDiagram(diagram.name);
    });

    // Add tooltip on hover
    circle.addEventListener('mouseover', function(e) {
      const tooltip = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      tooltip.setAttribute('id', 'tooltip-' + diagram.id);
      tooltip.setAttribute('x', diagram.x);
      tooltip.setAttribute('y', diagram.y - diagram.radius - 10);
      tooltip.setAttribute('text-anchor', 'middle');
      tooltip.setAttribute('font-size', '14');
      tooltip.setAttribute('font-weight', 'bold');
      tooltip.setAttribute('fill', 'var(--diagram-text)');
      tooltip.textContent = diagram.name + ' (' + diagram.type + ')';
      svg.appendChild(tooltip);
    });

    circle.addEventListener('mouseout', function() {
      const tooltip = document.getElementById('tooltip-' + diagram.id);
      if (tooltip) tooltip.remove();
    });

    // Create the label
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', diagram.x);
    text.setAttribute('y', diagram.y);
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('font-size', '12');
    text.setAttribute('fill', '#fff');
    text.setAttribute('pointer-events', 'none');

    // Truncate long names
    let displayName = diagram.name;
    if (displayName.length > 10) {
      displayName = displayName.substring(0, 8) + '...';
    }
    text.textContent = displayName;

    // Add to SVG
    nodesGroup.appendChild(circle);
    nodesGroup.appendChild(text);
  });
});

function searchByDiagram(name) {
  // Get the search input and set its value to the diagram name
  const searchInput = document.getElementById('diagram-search');
  if (searchInput) {
    // Set the value
    searchInput.value = name;

    // Scroll to the top of the page where the search box is located
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    // Highlight the search box to draw attention to it
    searchInput.style.transition = 'background-color 0.3s';
    searchInput.style.backgroundColor = '#ffffcc';

    // Trigger the search after a short delay to ensure the scroll completes
    setTimeout(() => {
      const searchButton = document.getElementById('search-button');
      if (searchButton) {
        searchButton.click();
      } else {
        // Fallback if button not found - perform search manually
        globalPerformSearch();
      }

      // Reset the highlight after a moment
      setTimeout(() => {
        searchInput.style.backgroundColor = '';
      }, 1000);
    }, 500);
  } else {
    // Fallback if search input not found
    alert(`Search for: ${name}\n\nThe search input element was not found. Please search manually.`);
  }
}
</script>

### Related Diagrams

The table below shows diagrams that are commonly used together. When working with one diagram, you may find it helpful
to reference its related diagrams for a more complete understanding.

\n<details>\n<summary>Table Details</summary>\n\n| Primary Diagram | Related Diagrams | Relationship |
| --- | --- | --- |
| Architecture Overview | ERD Overview, Authentication Flow, Project Roadmap | The Architecture Overview provides the high-level structure, while the related diagrams detail specific aspects of the system. |
| Authentication Flow | User Registration Sequence, TAD Authentication Flow | These diagrams show different aspects of the authentication and user registration processes. |
| ERD Overview | Class Diagram (Detailed), ERD Overview (Enhanced) | The ERD shows database relationships, while the Class Diagram shows the object-oriented implementation of those entities. |
| Team Creation Sequence | User Registration Sequence, Team Creation and Management | These diagrams show the user and team management workflows, which are closely related. |
| Todo State Machine | Post Creation Sequence, Class Diagram (Detailed) | The State Machine shows status transitions, while the related diagrams show how these states are used in content management. |
\n</details>\n

## Diagram Export Options

This section provides information on how to export diagrams from the Enhanced Laravel Application documentation for use
in presentations, reports, or other documents.

### Export Formats

Diagrams in this documentation can be exported in the following formats:

<div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px;">
  <div style="flex: 1; min-width: 200px; padding: 15px; background-color: var(--card-bg); border-radius: 5px; border: 1px solid var(--card-border); color: var(--card-text);">
    <h4 style="margin-top: 0;">PNG</h4>
    <p>Raster image format with transparency support. Best for web use and presentations.</p>
    <p><strong>Resolution:</strong> 96 DPI (standard) or 300 DPI (high-res)</p>
    <button onclick="showExportModal('png')" style="padding: 8px 16px; background-color: var(--search-btn-primary, #0077cc); color: var(--search-btn-primary-text, #fff); border: none; border-radius: 4px; cursor: pointer;">Export as PNG</button>
  </div>

  <div style="flex: 1; min-width: 200px; padding: 15px; background-color: var(--card-bg); border-radius: 5px; border: 1px solid var(--card-border); color: var(--card-text);">
    <h4 style="margin-top: 0;">SVG</h4>
    <p>Vector format that scales to any size without quality loss. Best for editing and printing.</p>
    <p><strong>Features:</strong> Editable in vector graphics software</p>
    <button onclick="showExportModal('svg')" style="padding: 8px 16px; background-color: var(--search-btn-primary, #0077cc); color: var(--search-btn-primary-text, #fff); border: none; border-radius: 4px; cursor: pointer;">Export as SVG</button>
  </div>

  <div style="flex: 1; min-width: 200px; padding: 15px; background-color: var(--card-bg); border-radius: 5px; border: 1px solid var(--card-border); color: var(--card-text);">
    <h4 style="margin-top: 0;">PDF</h4>
    <p>Document format suitable for printing and sharing. Maintains vector quality.</p>
    <p><strong>Features:</strong> Print-ready with embedded fonts</p>
    <button onclick="showExportModal('pdf')" style="padding: 8px 16px; background-color: var(--search-btn-primary, #0077cc); color: var(--search-btn-primary-text, #fff); border: none; border-radius: 4px; cursor: pointer;">Export as PDF</button>
  </div>
</div>

<!-- Export Modal -->
<div id="export-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); overflow: auto;">
  <div style="background-color: var(--card-bg); margin: 10% auto; padding: 20px; border: 1px solid var(--card-border); border-radius: 5px; width: 80%; max-width: 600px; color: var(--card-text);">
    <span id="close-modal" style="color: var(--card-text); float: right; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
    <h3 id="export-modal-title" style="margin-top: 0;">Export Diagram</h3>

    <div style="margin-bottom: 20px;">
      <label for="diagram-select" style="display: block; margin-bottom: 5px;">Select Diagram:</label>
      <select id="diagram-select" style="width: 100%; padding: 8px; border: 1px solid var(--search-input-border, #ccc); border-radius: 4px; background-color: var(--search-input-bg, #fff); color: var(--search-text, #333);">
        <option value="">-- Select a diagram --</option>
        <option value="architecture-overview">Architecture Overview</option>
        <option value="erd-overview">ERD Overview</option>
        <option value="authentication-flow">Authentication Flow</option>
        <option value="class-diagram-detailed">Class Diagram (Detailed)</option>
        <option value="user-registration-sequence">User Registration Sequence</option>
        <option value="team-creation-sequence">Team Creation Sequence</option>
        <option value="post-creation-sequence">Post Creation Sequence</option>
        <option value="todo-state-machine">Todo State Machine</option>
        <option value="project-roadmap">Project Roadmap</option>
      </select>
    </div>

    <div id="export-options-png" class="export-options" style="margin-bottom: 20px;">
      <label for="png-resolution" style="display: block; margin-bottom: 5px;">Resolution:</label>
      <select id="png-resolution" style="width: 100%; padding: 8px; border: 1px solid var(--search-input-border, #ccc); border-radius: 4px; background-color: var(--search-input-bg, #fff); color: var(--search-text, #333);">
        <option value="standard">Standard (96 DPI)</option>
        <option value="high">High Resolution (300 DPI)</option>
        <option value="ultra">Ultra High Resolution (600 DPI)</option>
      </select>
    </div>

    <div id="export-options-svg" class="export-options" style="margin-bottom: 20px; display: none;">
      <label for="svg-include-styles" style="margin-right: 10px;">
        <input type="checkbox" id="svg-include-styles" checked> Include styles
      </label>
      <label for="svg-include-fonts" style="margin-right: 10px;">
        <input type="checkbox" id="svg-include-fonts" checked> Embed fonts
      </label>
    </div>

    <div id="export-options-pdf" class="export-options" style="margin-bottom: 20px; display: none;">
      <label for="pdf-page-size" style="display: block; margin-bottom: 5px;">Page Size:</label>
      <select id="pdf-page-size" style="width: 100%; padding: 8px; border: 1px solid var(--search-input-border, #ccc); border-radius: 4px; background-color: var(--search-input-bg, #fff); color: var(--search-text, #333);">
        <option value="a4">A4</option>
        <option value="letter">Letter</option>
        <option value="legal">Legal</option>
      </select>
    </div>

    <div style="display: flex; justify-content: space-between;">
      <button id="cancel-export" style="padding: 8px 16px; background-color: var(--search-btn-secondary, #6c757d); color: var(--search-btn-secondary-text, #fff); border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
      <button id="download-export" style="padding: 8px 16px; background-color: var(--search-btn-primary, #0077cc); color: var(--search-btn-primary-text, #fff); border: none; border-radius: 4px; cursor: pointer;">Download</button>
    </div>

  </div>
</div>

<script>
let currentExportFormat = '';

function showExportModal(format) {
  currentExportFormat = format;

  // Update modal title
  document.getElementById('export-modal-title').textContent = `Export Diagram as ${format.toUpperCase()}`;

  // Hide all export options
  document.querySelectorAll('.export-options').forEach(el => {
    el.style.display = 'none';
  });

  // Show relevant export options
  document.getElementById(`export-options-${format}`).style.display = 'block';

  // Show the modal
  document.getElementById('export-modal').style.display = 'block';
}

document.addEventListener('DOMContentLoaded', function() {
  // Close modal when clicking the X
  document.getElementById('close-modal').addEventListener('click', function() {
    document.getElementById('export-modal').style.display = 'none';
  });

  // Close modal when clicking Cancel
  document.getElementById('cancel-export').addEventListener('click', function() {
    document.getElementById('export-modal').style.display = 'none';
  });

  // Handle download button click
  document.getElementById('download-export').addEventListener('click', function() {
    const diagramId = document.getElementById('diagram-select').value;

    if (!diagramId) {
      alert('Please select a diagram to export.');
      return;
    }

    // In a real implementation, this would generate and download the file
    // For this demo, we'll just show a message
    alert(`Exporting ${diagramId} as ${currentExportFormat.toUpperCase()}...\n\nIn a real implementation, this would download the file.`);

    // Close the modal
    document.getElementById('export-modal').style.display = 'none';
  });

  // Close modal when clicking outside of it
  window.addEventListener('click', function(event) {
    const modal = document.getElementById('export-modal');
    if (event.target === modal) {
      modal.style.display = 'none';
    }
  });
});
</script>

### Batch Export

You can also export multiple diagrams at once using the batch export tool. This is useful when you need to include
several diagrams in a presentation or report.

<div style="padding: 15px; background-color: var(--card-bg); border-radius: 5px; border: 1px solid var(--card-border); color: var(--card-text); margin-bottom: 20px;">
  <h4 style="margin-top: 0;">Batch Export Tool</h4>

  <div style="margin-bottom: 15px;">
    <label for="batch-format" style="display: block; margin-bottom: 5px;">Export Format:</label>
    <select id="batch-format" style="width: 100%; padding: 8px; border: 1px solid var(--search-input-border); border-radius: 4px; background-color: var(--search-input-bg); color: var(--search-text);">
      <option value="png">PNG (96 DPI)</option>
      <option value="png-high">PNG (300 DPI)</option>
      <option value="svg">SVG</option>
      <option value="pdf">PDF</option>
    </select>
  </div>

  <div style="margin-bottom: 15px;">
    <label style="display: block; margin-bottom: 5px;">Select Diagrams:</label>
    <div style="max-height: 200px; overflow-y: auto; border: 1px solid var(--search-input-border); border-radius: 4px; padding: 10px; background-color: var(--search-input-bg); color: var(--search-text);">
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" id="select-all-diagrams"> <strong>Select All</strong>
      </label>
      <hr style="border: none; border-top: 1px solid var(--search-input-border); margin: 5px 0;">
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="architecture-overview"> Architecture Overview
      </label>
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="erd-overview"> ERD Overview
      </label>
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="authentication-flow"> Authentication Flow
      </label>
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="class-diagram-detailed"> Class Diagram (Detailed)
      </label>
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="user-registration-sequence"> User Registration Sequence
      </label>
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="team-creation-sequence"> Team Creation Sequence
      </label>
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="post-creation-sequence"> Post Creation Sequence
      </label>
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="todo-state-machine"> Todo State Machine
      </label>
      <label style="display: block; margin-bottom: 5px;">
        <input type="checkbox" class="batch-diagram" value="project-roadmap"> Project Roadmap
      </label>
    </div>
  </div>

<button id="batch-export-button" style="padding: 8px 16px; background-color: var(--search-btn-primary, #0077cc); color: var(--search-btn-primary-text, #fff); border: none; border-radius: 4px; cursor: pointer;">Export
Selected Diagrams</button>

</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Handle Select All checkbox
  document.getElementById('select-all-diagrams').addEventListener('change', function() {
    const isChecked = this.checked;
    document.querySelectorAll('.batch-diagram').forEach(checkbox => {
      checkbox.checked = isChecked;
    });
  });

  // Handle Batch Export button
  document.getElementById('batch-export-button').addEventListener('click', function() {
    const format = document.getElementById('batch-format').value;
    const selectedDiagrams = [];

    document.querySelectorAll('.batch-diagram:checked').forEach(checkbox => {
      selectedDiagrams.push(checkbox.value);
    });

    if (selectedDiagrams.length === 0) {
      alert('Please select at least one diagram to export.');
      return;
    }

    // In a real implementation, this would generate and download the files
    // For this demo, we'll just show a message
    alert(`Exporting ${selectedDiagrams.length} diagrams as ${format.toUpperCase()}...\n\nSelected diagrams:\n${selectedDiagrams.join('\n')}\n\nIn a real implementation, this would download the files as a zip archive.`);
  });
});
</script>

### Command-Line Export

For advanced users or automated workflows, diagrams can also be exported using command-line tools.

#### Mermaid Diagrams

```bash
# Install mermaid-cli if not already installed
npm install -g @mermaid-js/mermaid-cli

# Export a single diagram to PNG
mmdc -i mermaid/light/diagram-name-light.md -o diagram-name.png -w 1200

# Export a single diagram to SVG
mmdc -i mermaid/light/diagram-name-light.md -o diagram-name.svg

# Export a single diagram to PDF
mmdc -i mermaid/light/diagram-name-light.md -o diagram-name.pdf

# Batch export all diagrams in a directory
for file in mermaid/light/*.md; do
  filename=$(basename "$file" .md)
  mmdc -i "$file" -o "exports/${filename}.png" -w 1200
done
```text

#### PlantUML Diagrams

```bash
# Export a single diagram to PNG
java -jar plantuml.jar -tpng plantuml/light/diagram-name-light.puml -o exports

# Export a single diagram to SVG
java -jar plantuml.jar -tsvg plantuml/light/diagram-name-light.puml -o exports

# Export a single diagram to PDF
java -jar plantuml.jar -tpdf plantuml/light/diagram-name-light.puml -o exports

# Batch export all diagrams in a directory
java -jar plantuml.jar -tpng plantuml/light/*.puml -o exports
```

## Conclusion

This diagram index serves as a comprehensive resource for finding, using, and creating diagrams for the Enhanced Laravel
Application documentation. By following the guidelines and best practices outlined in this document, you can ensure that
diagrams are consistent, accessible, and valuable for all users of the documentation.

If you have suggestions for improving this index or the diagramming standards, please contact the documentation team or
submit a pull request with your proposed changes.

## Diagram Request Process

If you need a diagram that doesn't exist in the current documentation, follow this process to request its creation:

### 1. Check Existing Diagrams

Before requesting a new diagram, thoroughly check this index and the source documents to ensure the diagram doesn't
already exist or that a similar diagram couldn't be adapted for your needs.

### 2. Prepare Your Request

When requesting a new diagram, include the following information:

- **Purpose**: What concept or process needs to be visualized?
- **Diagram Type**: What type of diagram would best represent this information?
- **Audience**: Who will be using this diagram?
- **Source Document**: Where will this diagram be referenced?
- **Key Elements**: What are the essential components that should be included?
- **References**: Are there any existing diagrams or documentation that could serve as references?

### 3. Submit Your Request

Submit your diagram request through one of these channels:

- Create an issue in the documentation repository with the label `diagram-request`
- Submit a request in the project management tool with the category "Documentation - Diagram"
- Contact the documentation team directly via the team chat channel

### 4. Review Process

After submission, your request will go through these steps:

1. **Initial Review**: The documentation team will review your request for completeness
2. **Prioritization**: Requests will be prioritized based on project needs and resource availability
3. **Creation**: A technical writer or developer will create the diagram
4. **Review**: The diagram will be reviewed for accuracy and clarity
5. **Publication**: The approved diagram will be added to the documentation and this index

## Version History

<div style="background-color:#f0f0f0; padding:15px; border-radius:5px; border: 1px solid #d0d0d0; margin:10px 0;">
<h4 style="margin-top: 0; color: #111;">Document History</h4>

\n<details>\n<summary>Table Details</summary>\n\n| Version | Date | Changes | Author |
| --- | --- | --- | --- |
| 1.1.0 | 2025-05-20 | Updated formatting for high contrast and accessibility, added navigation and version history | AI Assistant |
| 1.0.0 | 2025-05-16 | Initial version | AI Assistant |
\n</details>\n
</div>

<div style="background-color:#e0f0e0; padding:15px; border-radius:5px; border: 1px solid #c0d0c0; margin:20px 0;">
<h4 style="margin-top: 0; color: #007700;">Navigation</h4>

<div style="display: flex; justify-content: space-between; margin-top: 10px;">
  <div>
    <strong>Previous:</strong> <a href="/_root/docs/E_L_A/illustrations/README.md">Illustrations README</a>
  </div>
  <div>
    <strong>Next:</strong> <a href="/_root/docs/E_L_A/000-index.md">Documentation Index</a>
  </div>
</div>
</div>
