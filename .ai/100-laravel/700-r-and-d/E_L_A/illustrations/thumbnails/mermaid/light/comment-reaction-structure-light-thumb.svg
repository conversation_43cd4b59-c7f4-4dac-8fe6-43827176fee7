<svg aria-roledescription="class" role="graphics-document document" viewBox="0 0 1875.44140625 1572" style="max-width: 1875.44px; background-color: white;" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#666666;stroke:#666666;}#my-svg .marker.cross{stroke:#666666;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg g.classGroup text{fill:#9370DB;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#my-svg g.classGroup text .title{font-weight:bolder;}#my-svg .nodeLabel,#my-svg .edgeLabel{color:#333333;}#my-svg .edgeLabel .label rect{fill:#ECECFF;}#my-svg .label text{fill:#333333;}#my-svg .labelBkg{background:#ECECFF;}#my-svg .edgeLabel .label span{background:#ECECFF;}#my-svg .classTitle{font-weight:bolder;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .divider{stroke:#9370DB;stroke-width:1;}#my-svg g.clickable{cursor:pointer;}#my-svg g.classGroup rect{fill:#ECECFF;stroke:#9370DB;}#my-svg g.classGroup line{stroke:#9370DB;stroke-width:1;}#my-svg .classLabel .box{stroke:none;stroke-width:0;fill:#ECECFF;opacity:0.5;}#my-svg .classLabel .label{fill:#9370DB;font-size:10px;}#my-svg .relation{stroke:#666666;stroke-width:1;fill:none;}#my-svg .dashed-line{stroke-dasharray:3;}#my-svg .dotted-line{stroke-dasharray:1 2;}#my-svg #compositionStart,#my-svg .composition{fill:#666666!important;stroke:#666666!important;stroke-width:1;}#my-svg #compositionEnd,#my-svg .composition{fill:#666666!important;stroke:#666666!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#666666!important;stroke:#666666!important;stroke-width:1;}#my-svg #dependencyStart,#my-svg .dependency{fill:#666666!important;stroke:#666666!important;stroke-width:1;}#my-svg #extensionStart,#my-svg .extension{fill:transparent!important;stroke:#666666!important;stroke-width:1;}#my-svg #extensionEnd,#my-svg .extension{fill:transparent!important;stroke:#666666!important;stroke-width:1;}#my-svg #aggregationStart,#my-svg .aggregation{fill:transparent!important;stroke:#666666!important;stroke-width:1;}#my-svg #aggregationEnd,#my-svg .aggregation{fill:transparent!important;stroke:#666666!important;stroke-width:1;}#my-svg #lollipopStart,#my-svg .lollipop{fill:#ECECFF!important;stroke:#666666!important;stroke-width:1;}#my-svg #lollipopEnd,#my-svg .lollipop{fill:#ECECFF!important;stroke:#666666!important;stroke-width:1;}#my-svg .edgeTerminals{font-size:11px;line-height:initial;}#my-svg .classTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="my-svg_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="my-svg_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="my-svg_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="my-svg_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="my-svg_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="my-svg_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="my-svg_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="my-svg_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="my-svg_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"/></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="my-svg_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"/></marker></defs><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CommentState_Pending_1" d="M1567.516,1330.097L1590.11,1340.914C1612.704,1351.731,1657.891,1373.366,1680.485,1389.849C1703.078,1406.333,1703.078,1417.667,1703.078,1423.333L1703.078,1429"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CommentState_Approved_2" d="M1355.472,1351.173L1349.753,1358.478C1344.035,1365.782,1332.597,1380.391,1326.879,1393.362C1321.16,1406.333,1321.16,1417.667,1321.16,1423.333L1321.16,1429"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CommentState_Rejected_3" d="M1281.922,1301.064L1224.466,1316.72C1167.011,1332.376,1052.099,1363.688,994.643,1383.511C937.188,1403.333,937.188,1411.667,937.188,1415.833L937.188,1420"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CommentState_Deleted_4" d="M1281.496,1284.014L1160.673,1302.511C1039.85,1321.009,798.205,1358.005,677.382,1380.669C556.559,1403.333,556.559,1411.667,556.559,1415.833L556.559,1420"/><path marker-start="url(#my-svg_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Comment_CommentState_5" d="M1161.864,964.702L1205.768,990.085C1249.671,1015.468,1337.478,1066.234,1381.382,1103.284C1425.285,1140.333,1425.285,1163.667,1425.285,1175.333L1425.285,1187"/><path marker-start="url(#my-svg_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Comment_CommentReaction_6" d="M1074.554,1097.246L1075.538,1100.539C1076.522,1103.831,1078.489,1110.415,1079.473,1119.874C1080.457,1129.333,1080.457,1141.667,1080.457,1147.833L1080.457,1154"/><path marker-end="url(#my-svg_class-aggregationEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Comment_User_7" d="M870.602,1027.708L857.08,1042.59C843.559,1057.472,816.516,1087.236,788.265,1114.524C760.014,1141.811,730.555,1166.622,715.825,1179.027L701.096,1191.433"/><path marker-end="url(#my-svg_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_CommentAggregateRoot_Comment_8" d="M766.857,598L777.771,604.167C788.684,610.333,810.511,622.667,827.212,636.753C843.912,650.84,855.487,666.68,861.274,674.6L867.062,682.52"/><path marker-end="url(#my-svg_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_CommentAggregateRoot_CommentReaction_9" d="M485.469,598L485.469,604.167C485.469,610.333,485.469,622.667,485.469,669C485.469,715.333,485.469,795.667,485.469,876C485.469,956.333,485.469,1036.667,562.138,1095.518C638.808,1154.369,792.148,1191.738,868.817,1210.423L945.487,1229.107"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_Commentable_Post_10" d="M1284.525,216.547L1290.622,220.956C1296.72,225.365,1308.915,234.182,1315.012,257.258C1321.109,280.333,1321.109,317.667,1321.109,336.333L1321.109,355"/><path marker-start="url(#my-svg_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-dashed relation" id="id_Commentable_Todo_11" d="M1107.66,223.589L1106.956,226.824C1106.253,230.059,1104.845,236.53,1104.141,258.431C1103.438,280.333,1103.438,317.667,1103.438,336.333L1103.438,355"/><path marker-end="url(#my-svg_class-aggregationEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Post_Comment_12" d="M1321.109,523L1321.109,541.667C1321.109,560.333,1321.109,597.667,1294.347,636.961C1267.586,676.255,1214.062,717.511,1187.3,738.139L1160.538,758.766"/><path marker-end="url(#my-svg_class-aggregationEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Todo_Comment_13" d="M1103.438,523L1103.438,541.667C1103.438,560.333,1103.438,597.667,1102.107,619.709C1100.776,641.751,1098.115,648.503,1096.784,651.878L1095.454,655.254"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(1425.28515625, 1117)" class="edgeLabel"><g transform="translate(-32.375, -12)" class="label"><foreignObject height="24" width="64.75"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has state</p></span></div></foreignObject></g></g><g transform="translate(1080.45703125, 1117)" class="edgeLabel"><g transform="translate(-47.21875, -12)" class="label"><foreignObject height="24" width="94.4375"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has reactions</p></span></div></foreignObject></g></g><g transform="translate(789.47265625, 1117)" class="edgeLabel"><g transform="translate(-43.15625, -12)" class="label"><foreignObject height="24" width="86.3125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>authored by</p></span></div></foreignObject></g></g><g transform="translate(832.337890625, 635)" class="edgeLabel"><g transform="translate(-58.8671875, -12)" class="label"><foreignObject height="24" width="117.734375"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>creates/updates</p></span></div></foreignObject></g></g><g transform="translate(485.46875, 876)" class="edgeLabel"><g transform="translate(-31.0390625, -12)" class="label"><foreignObject height="24" width="62.078125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>manages</p></span></div></foreignObject></g></g><g transform="translate(1321.109375, 243)" class="edgeLabel"><g transform="translate(-41.890625, -12)" class="label"><foreignObject height="24" width="83.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(1103.4375, 243)" class="edgeLabel"><g transform="translate(-41.890625, -12)" class="label"><foreignObject height="24" width="83.78125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>implements</p></span></div></foreignObject></g></g><g transform="translate(1321.109375, 635)" class="edgeLabel"><g transform="translate(-50.90625, -12)" class="label"><foreignObject height="24" width="101.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has comments</p></span></div></foreignObject></g></g><g transform="translate(1103.4375, 635)" class="edgeLabel"><g transform="translate(-50.90625, -12)" class="label"><foreignObject height="24" width="101.8125"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>has comments</p></span></div></foreignObject></g></g><g transform="translate(1153.923586332473, 977.4376861416151)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1060.039072185961, 1101.0620534238903)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(847.7314813519198, 1030.573620916728)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g></g><g transform="translate(1306.1093775000002, 540.5000021428572)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1088.4375, 540.5)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g></g><g transform="translate(1435.285158125, 1164.5000016071428)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(1090.457030625, 1131.4999994642858)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g><g transform="translate(705.3761193542898, 1198.2279025394087)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 9px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">1</span></div></foreignObject></g><g transform="translate(1164.299012594475, 765.9519714233454)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g><g transform="translate(1104.2255392373622, 656.2198709982066)" class="edgeTerminals"><g transform="translate(0, 0)" class="inner"/><foreignObject style="width: 36px; height: 12px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; padding-right: 1px; white-space: nowrap;"><span style=";display: inline-block" class="edgeLabel">0..*</span></div></foreignObject></g></g><g class="nodes"><g transform="translate(1133.021484375, 107)" id="classId-Commentable-0" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-175.55078125 -99 L175.55078125 -99 L175.55078125 99 L-175.55078125 99"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-175.55078125 -99 C-76.9239107271381 -99, 21.702959795723814 -99, 175.55078125 -99 M-175.55078125 -99 C-44.20026454360956 -99, 87.15025216278087 -99, 175.55078125 -99 M175.55078125 -99 C175.55078125 -55.073436165597165, 175.55078125 -11.14687233119433, 175.55078125 99 M175.55078125 -99 C175.55078125 -51.17571984149339, 175.55078125 -3.351439682986779, 175.55078125 99 M175.55078125 99 C53.87432614708402 99, -67.80212895583196 99, -175.55078125 99 M175.55078125 99 C36.09581338079116 99, -103.35915448841769 99, -175.55078125 99 M-175.55078125 99 C-175.55078125 39.06278082080268, -175.55078125 -20.87443835839464, -175.55078125 -99 M-175.55078125 99 C-175.55078125 51.95357427655109, -175.55078125 4.907148553102175, -175.55078125 -99"/></g><g transform="translate(-41.171875, -75)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="82.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 122px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«interface»</p></span></div></foreignObject></g></g><g transform="translate(-51.5390625, -51)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="103.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Commentable</p></span></div></foreignObject></g></g><g transform="translate(-163.55078125, -3)" class="members-group text"/><g transform="translate(-163.55078125, 27)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="231.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 266px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentsAreEnabled() : : bool</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="275.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 309px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentsAreAutoApproved() : : bool</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="275.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 308px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentsAreReactionsOnly() : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-175.55078125 -27 C-64.39527041533638 -27, 46.76024041932723 -27, 175.55078125 -27 M-175.55078125 -27 C-103.9951631875219 -27, -32.43954512504379 -27, 175.55078125 -27"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-175.55078125 -3 C-81.86007050035657 -3, 11.830640249286859 -3, 175.55078125 -3 M-175.55078125 -3 C-37.30113740190416 -3, 100.94850644619169 -3, 175.55078125 -3"/></g></g><g transform="translate(1425.28515625, 1262)" id="classId-CommentState-1" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-125.99609375 -75 L125.99609375 -75 L125.99609375 75 L-125.99609375 75"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.99609375 -75 C-26.459997886475662 -75, 73.07609797704868 -75, 125.99609375 -75 M-125.99609375 -75 C-49.484225667833 -75, 27.027642414333997 -75, 125.99609375 -75 M125.99609375 -75 C125.99609375 -22.107702366434175, 125.99609375 30.78459526713165, 125.99609375 75 M125.99609375 -75 C125.99609375 -16.472887318212436, 125.99609375 42.05422536357513, 125.99609375 75 M125.99609375 75 C47.06873155316882 75, -31.858630643662366 75, -125.99609375 75 M125.99609375 75 C60.20178435841497 75, -5.592525033170062 75, -125.99609375 75 M-125.99609375 75 C-125.99609375 32.87289547398656, -125.99609375 -9.25420905202688, -125.99609375 -75 M-125.99609375 75 C-125.99609375 42.328403808621715, -125.99609375 9.65680761724343, -125.99609375 -75"/></g><g transform="translate(-37.90625, -51)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="75.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 116px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«abstract»</p></span></div></foreignObject></g></g><g transform="translate(-54.9609375, -27)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="109.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 145px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CommentState</p></span></div></foreignObject></g></g><g transform="translate(-113.99609375, 21)" class="members-group text"/><g transform="translate(-113.99609375, 51)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="173.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 212px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+config() : : StateConfig</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.99609375 -3 C-51.28309500035047 -3, 23.429903749299058 -3, 125.99609375 -3 M-125.99609375 -3 C-60.23362325868054 -3, 5.528847232638924 -3, 125.99609375 -3"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-125.99609375 21 C-45.413847546263625 21, 35.16839865747275 21, 125.99609375 21 M-125.99609375 21 C-30.298703333277146 21, 65.39868708344571 21, 125.99609375 21"/></g></g><g transform="translate(1703.078125, 1492)" id="classId-Pending-2" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-164.36328125 -63 L164.36328125 -63 L164.36328125 63 L-164.36328125 63"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-164.36328125 -63 C-52.276924058872396 -63, 59.80943313225521 -63, 164.36328125 -63 M-164.36328125 -63 C-73.95030069409121 -63, 16.462679861817577 -63, 164.36328125 -63 M164.36328125 -63 C164.36328125 -23.24039244555138, 164.36328125 16.519215108897242, 164.36328125 63 M164.36328125 -63 C164.36328125 -35.501643364243485, 164.36328125 -8.00328672848697, 164.36328125 63 M164.36328125 63 C50.25313594836197 63, -63.857009353276055 63, -164.36328125 63 M164.36328125 63 C92.0320978005236 63, 19.700914351047203 63, -164.36328125 63 M-164.36328125 63 C-164.36328125 24.588055956014017, -164.36328125 -13.823888087971966, -164.36328125 -63 M-164.36328125 63 C-164.36328125 28.340116561285527, -164.36328125 -6.319766877428947, -164.36328125 -63"/></g><g transform="translate(0, -39)" class="annotation-group text"/><g transform="translate(-29.4140625, -39)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="58.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Pending</p></span></div></foreignObject></g></g><g transform="translate(-152.36328125, 9)" class="members-group text"/><g transform="translate(-152.36328125, 39)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="275.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canTransitionTo(State $state) : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-164.36328125 -15 C-57.990206701578856 -15, 48.38286784684229 -15, 164.36328125 -15 M-164.36328125 -15 C-93.58092519834517 -15, -22.79856914669034 -15, 164.36328125 -15"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-164.36328125 9 C-88.31050720856328 9, -12.25773316712656 9, 164.36328125 9 M-164.36328125 9 C-37.72123536403595 9, 88.9208105219281 9, 164.36328125 9"/></g></g><g transform="translate(1321.16015625, 1492)" id="classId-Approved-3" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-167.5546875 -63 L167.5546875 -63 L167.5546875 63 L-167.5546875 63"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-167.5546875 -63 C-80.91305522650933 -63, 5.728577046981343 -63, 167.5546875 -63 M-167.5546875 -63 C-97.66062186333001 -63, -27.766556226660015 -63, 167.5546875 -63 M167.5546875 -63 C167.5546875 -15.167792724029702, 167.5546875 32.664414551940595, 167.5546875 63 M167.5546875 -63 C167.5546875 -19.859349718260994, 167.5546875 23.281300563478013, 167.5546875 63 M167.5546875 63 C38.81988861754897 63, -89.91491026490206 63, -167.5546875 63 M167.5546875 63 C81.55247041360614 63, -4.449746672787711 63, -167.5546875 63 M-167.5546875 63 C-167.5546875 16.357167968017464, -167.5546875 -30.285664063965072, -167.5546875 -63 M-167.5546875 63 C-167.5546875 14.822945289454026, -167.5546875 -33.35410942109195, -167.5546875 -63"/></g><g transform="translate(0, -39)" class="annotation-group text"/><g transform="translate(-35.796875, -39)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="71.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 114px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Approved</p></span></div></foreignObject></g></g><g transform="translate(-155.5546875, 9)" class="members-group text"/><g transform="translate(-155.5546875, 39)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="275.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canTransitionTo(State $state) : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-167.5546875 -15 C-51.595080662757255 -15, 64.36452617448549 -15, 167.5546875 -15 M-167.5546875 -15 C-88.75741390279354 -15, -9.960140305587089 -15, 167.5546875 -15"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-167.5546875 9 C-40.06300924653823 9, 87.42866900692354 9, 167.5546875 9 M-167.5546875 9 C-97.2972502279237 9, -27.039812955847395 9, 167.5546875 9"/></g></g><g transform="translate(937.1875, 1492)" id="classId-Rejected-4" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-166.41796875 -72 L166.41796875 -72 L166.41796875 72 L-166.41796875 72"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-166.41796875 -72 C-88.59607205969608 -72, -10.774175369392168 -72, 166.41796875 -72 M-166.41796875 -72 C-70.28709741405603 -72, 25.84377392188793 -72, 166.41796875 -72 M166.41796875 -72 C166.41796875 -29.629639558432245, 166.41796875 12.74072088313551, 166.41796875 72 M166.41796875 -72 C166.41796875 -32.43088290943054, 166.41796875 7.138234181138927, 166.41796875 72 M166.41796875 72 C89.34426658224648 72, 12.270564414492952 72, -166.41796875 72 M166.41796875 72 C54.949319307756824 72, -56.51933013448635 72, -166.41796875 72 M-166.41796875 72 C-166.41796875 41.435743772537336, -166.41796875 10.871487545074665, -166.41796875 -72 M-166.41796875 72 C-166.41796875 28.569910266836075, -166.41796875 -14.86017946632785, -166.41796875 -72"/></g><g transform="translate(0, -48)" class="annotation-group text"/><g transform="translate(-33.5234375, -48)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="67.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 106px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Rejected</p></span></div></foreignObject></g></g><g transform="translate(-154.41796875, 0)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="174.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 211px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+rejectionReason: string</p></span></div></foreignObject></g></g><g transform="translate(-154.41796875, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="275.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canTransitionTo(State $state) : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-166.41796875 -24 C-52.4374361124368 -24, 61.5430965251264 -24, 166.41796875 -24 M-166.41796875 -24 C-67.87461017569986 -24, 30.668748398600286 -24, 166.41796875 -24"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-166.41796875 24 C-65.14950729170677 24, 36.11895416658646 24, 166.41796875 24 M-166.41796875 24 C-93.9708084091671 24, -21.52364806833421 24, 166.41796875 24"/></g></g><g transform="translate(556.55859375, 1492)" id="classId-Deleted-5" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-164.2109375 -72 L164.2109375 -72 L164.2109375 72 L-164.2109375 72"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-164.2109375 -72 C-91.85330139043553 -72, -19.495665280871066 -72, 164.2109375 -72 M-164.2109375 -72 C-81.62131600907126 -72, 0.9683054818574703 -72, 164.2109375 -72 M164.2109375 -72 C164.2109375 -40.62063261603696, 164.2109375 -9.241265232073907, 164.2109375 72 M164.2109375 -72 C164.2109375 -17.121579252771298, 164.2109375 37.756841494457404, 164.2109375 72 M164.2109375 72 C36.965579514502295 72, -90.27977847099541 72, -164.2109375 72 M164.2109375 72 C42.835090819721444 72, -78.54075586055711 72, -164.2109375 72 M-164.2109375 72 C-164.2109375 27.28614174659269, -164.2109375 -17.42771650681462, -164.2109375 -72 M-164.2109375 72 C-164.2109375 16.316572244898218, -164.2109375 -39.366855510203564, -164.2109375 -72"/></g><g transform="translate(0, -48)" class="annotation-group text"/><g transform="translate(-29.109375, -48)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="58.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Deleted</p></span></div></foreignObject></g></g><g transform="translate(-152.2109375, 0)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="158.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 198px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deletedAt: DateTime</p></span></div></foreignObject></g></g><g transform="translate(-152.2109375, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="275.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+canTransitionTo(State $state) : : bool</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-164.2109375 -24 C-88.50682525373071 -24, -12.80271300746142 -24, 164.2109375 -24 M-164.2109375 -24 C-74.00423524644377 -24, 16.20246700711246 -24, 164.2109375 -24"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-164.2109375 24 C-38.88600778086139 24, 86.43892193827722 24, 164.2109375 24 M-164.2109375 24 C-82.96216148518951 24, -1.7133854703790234 24, 164.2109375 24"/></g></g><g transform="translate(1008.44140625, 876)" id="classId-Comment-6" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-137.83984375 -204 L137.83984375 -204 L137.83984375 204 L-137.83984375 204"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-137.83984375 -204 C-48.7576502654302 -204, 40.3245432191396 -204, 137.83984375 -204 M-137.83984375 -204 C-75.74381618528828 -204, -13.647788620576563 -204, 137.83984375 -204 M137.83984375 -204 C137.83984375 -92.93096975420197, 137.83984375 18.138060491596065, 137.83984375 204 M137.83984375 -204 C137.83984375 -97.88257528825172, 137.83984375 8.23484942349657, 137.83984375 204 M137.83984375 204 C46.24127160145845 204, -45.3573005470831 204, -137.83984375 204 M137.83984375 204 C81.05712665318597 204, 24.274409556371936 204, -137.83984375 204 M-137.83984375 204 C-137.83984375 120.21059035759525, -137.83984375 36.4211807151905, -137.83984375 -204 M-137.83984375 204 C-137.83984375 105.28454882546708, -137.83984375 6.569097650934168, -137.83984375 -204"/></g><g transform="translate(0, -180)" class="annotation-group text"/><g transform="translate(-35.6640625, -180)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="71.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 113px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Comment</p></span></div></foreignObject></g></g><g transform="translate(-125.83984375, -132)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="114.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+content: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="111.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+user_id: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="197.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 230px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentable_type: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="178.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+commentable_id: string</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="164"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+parent_id: string|null</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="161.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 196px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+state: CommentState</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="212.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 243px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+approved_at: DateTime|null</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="207.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 234px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+rejected_at: DateTime|null</p></span></div></foreignObject></g><g transform="translate(0,204)" style="" class="label"><foreignObject height="24" width="216.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 242px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+rejection_reason: string|null</p></span></div></foreignObject></g></g><g transform="translate(-125.83984375, 132)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="133.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+approve() : : void</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="212.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 241px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+reject(string reason) : : void</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="122.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 162px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+delete() : : void</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-137.83984375 -156 C-68.23394134326978 -156, 1.3719610634604464 -156, 137.83984375 -156 M-137.83984375 -156 C-80.39853844285776 -156, -22.957233135715526 -156, 137.83984375 -156"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-137.83984375 108 C-35.967080785099284 108, 65.90568217980143 108, 137.83984375 108 M-137.83984375 108 C-42.14426636800084 108, 53.551311013998316 108, 137.83984375 108"/></g></g><g transform="translate(1080.45703125, 1262)" id="classId-CommentReaction-7" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-129.140625 -108 L129.140625 -108 L129.140625 108 L-129.140625 108"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-129.140625 -108 C-77.32492996343049 -108, -25.509234926860984 -108, 129.140625 -108 M-129.140625 -108 C-46.840814692973694 -108, 35.45899561405261 -108, 129.140625 -108 M129.140625 -108 C129.140625 -44.34115877056334, 129.140625 19.31768245887332, 129.140625 108 M129.140625 -108 C129.140625 -48.75536404535657, 129.140625 10.489271909286856, 129.140625 108 M129.140625 108 C49.722694941284686 108, -29.695235117430627 108, -129.140625 108 M129.140625 108 C63.62876187868878 108, -1.8831012426224447 108, -129.140625 108 M-129.140625 108 C-129.140625 63.30214701344013, -129.140625 18.604294026880254, -129.140625 -108 M-129.140625 108 C-129.140625 47.75314284977129, -129.140625 -12.493714300457427, -129.140625 -108"/></g><g transform="translate(0, -84)" class="annotation-group text"/><g transform="translate(-68.3125, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="136.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CommentReaction</p></span></div></foreignObject></g></g><g transform="translate(-117.140625, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="148.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 188px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+comment_id: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="111.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+user_id: string</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="91.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+type: string</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="165.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 201px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+created_at: DateTime</p></span></div></foreignObject></g></g><g transform="translate(-117.140625, 108)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-129.140625 -60 C-47.72271018355475 -60, 33.695204632890494 -60, 129.140625 -60 M-129.140625 -60 C-32.82152397858573 -60, 63.49757704282854 -60, 129.140625 -60"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-129.140625 84 C-42.95802981346088 84, 43.224565373078235 84, 129.140625 84 M-129.140625 84 C-72.25425727466533 84, -15.36788954933067 84, 129.140625 84"/></g></g><g transform="translate(485.46875, 439)" id="classId-CommentAggregateRoot-8" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-477.46875 -159 L477.46875 -159 L477.46875 159 L-477.46875 159"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-477.46875 -159 C-119.82476350828165 -159, 237.8192229834367 -159, 477.46875 -159 M-477.46875 -159 C-145.6825316849551 -159, 186.10368663008978 -159, 477.46875 -159 M477.46875 -159 C477.46875 -85.10793650900199, 477.46875 -11.215873018003975, 477.46875 159 M477.46875 -159 C477.46875 -63.70335766162303, 477.46875 31.59328467675394, 477.46875 159 M477.46875 159 C233.19694897254126 159, -11.074852054917471 159, -477.46875 159 M477.46875 159 C137.43859462810173 159, -202.59156074379655 159, -477.46875 159 M-477.46875 159 C-477.46875 62.84569311209454, -477.46875 -33.308613775810926, -477.46875 -159 M-477.46875 159 C-477.46875 65.83392128795212, -477.46875 -27.332157424095755, -477.46875 -159"/></g><g transform="translate(0, -135)" class="annotation-group text"/><g transform="translate(-89.9375, -135)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="179.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 211px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CommentAggregateRoot</p></span></div></foreignObject></g></g><g transform="translate(-465.46875, -87)" class="members-group text"/><g transform="translate(-465.46875, -57)" class="methods-group text"><g transform="translate(0,-24)" style="" class="label"><foreignObject height="48" width="841"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 841px; text-align: center; width: 841px;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+createComment(string content, string userId, string commentableType, string commentableId, string|null parentId) : : self</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="291.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 313px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+updateComment(string content) : : self</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="198.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 234px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+approveComment() : : self</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="276.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 301px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+rejectComment(string reason) : : self</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="186.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 221px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+deleteComment() : : self</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="336.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 356px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+addReaction(string type, string userId) : : self</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="364.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 381px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+removeReaction(string type, string userId) : : self</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="387.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 414px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+configureReactionsOnly(bool isReactionsOnly) : : self</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-477.46875 -111 C-193.6385640479885 -111, 90.19162190402301 -111, 477.46875 -111 M-477.46875 -111 C-220.65801424656615 -111, 36.15272150686769 -111, 477.46875 -111"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-477.46875 -87 C-182.41122091444237 -87, 112.64630817111527 -87, 477.46875 -87 M-477.46875 -87 C-181.90273947620688 -87, 113.66327104758625 -87, 477.46875 -87"/></g></g><g transform="translate(617.30859375, 1262)" id="classId-User-9" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-70.01953125 -84 L70.01953125 -84 L70.01953125 84 L-70.01953125 84"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-70.01953125 -84 C-25.158298710049834 -84, 19.70293382990033 -84, 70.01953125 -84 M-70.01953125 -84 C-21.0962830453808 -84, 27.826965159238398 -84, 70.01953125 -84 M70.01953125 -84 C70.01953125 -26.462772371118206, 70.01953125 31.07445525776359, 70.01953125 84 M70.01953125 -84 C70.01953125 -47.027186912925195, 70.01953125 -10.05437382585039, 70.01953125 84 M70.01953125 84 C15.217193870217578 84, -39.585143509564844 84, -70.01953125 84 M70.01953125 84 C40.56508333638236 84, 11.110635422764709 84, -70.01953125 84 M-70.01953125 84 C-70.01953125 36.184306914555, -70.01953125 -11.631386170889996, -70.01953125 -84 M-70.01953125 84 C-70.01953125 37.01094383048763, -70.01953125 -9.978112339024733, -70.01953125 -84"/></g><g transform="translate(0, -60)" class="annotation-group text"/><g transform="translate(-16.8828125, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="33.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 80px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>User</p></span></div></foreignObject></g></g><g transform="translate(-58.01953125, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="98.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="99.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 144px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+email: string</p></span></div></foreignObject></g></g><g transform="translate(-58.01953125, 84)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-70.01953125 -36 C-27.99308979438839 -36, 14.033351661223222 -36, 70.01953125 -36 M-70.01953125 -36 C-23.477966856656614 -36, 23.06359753668677 -36, 70.01953125 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-70.01953125 60 C-19.484525520798435 60, 31.05048020840313 60, 70.01953125 60 M-70.01953125 60 C-26.355672008442383 60, 17.308187233115234 60, 70.01953125 60"/></g></g><g transform="translate(1321.109375, 439)" id="classId-Post-10" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-77.171875 -84 L77.171875 -84 L77.171875 84 L-77.171875 84"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-77.171875 -84 C-26.00803318803424 -84, 25.15580862393152 -84, 77.171875 -84 M-77.171875 -84 C-25.715558221738753 -84, 25.740758556522493 -84, 77.171875 -84 M77.171875 -84 C77.171875 -33.13595108085783, 77.171875 17.72809783828434, 77.171875 84 M77.171875 -84 C77.171875 -28.05445851481383, 77.171875 27.891082970372338, 77.171875 84 M77.171875 84 C31.601282168803593 84, -13.969310662392814 84, -77.171875 84 M77.171875 84 C18.43828907806946 84, -40.29529684386108 84, -77.171875 84 M-77.171875 84 C-77.171875 46.62852631515348, -77.171875 9.257052630306958, -77.171875 -84 M-77.171875 84 C-77.171875 21.64806903966982, -77.171875 -40.70386192066036, -77.171875 -84"/></g><g transform="translate(0, -60)" class="annotation-group text"/><g transform="translate(-15.46875, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="30.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 78px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Post</p></span></div></foreignObject></g></g><g transform="translate(-65.171875, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+title: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="114.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+content: string</p></span></div></foreignObject></g></g><g transform="translate(-65.171875, 84)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-77.171875 -36 C-32.67844185180494 -36, 11.814991296390119 -36, 77.171875 -36 M-77.171875 -36 C-24.274581275439026 -36, 28.62271244912195 -36, 77.171875 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-77.171875 60 C-28.450541929059796 60, 20.27079114188041 60, 77.171875 60 M-77.171875 60 C-44.837117831900045 60, -12.50236066380009 60, 77.171875 60"/></g></g><g transform="translate(1103.4375, 439)" id="classId-Todo-11" class="node default"><g class="basic label-container"><path style="" fill="#ECECFF" stroke-width="0" stroke="none" d="M-90.5 -84 L90.5 -84 L90.5 84 L-90.5 84"/><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-90.5 -84 C-25.969820269088075 -84, 38.56035946182385 -84, 90.5 -84 M-90.5 -84 C-39.723886594753495 -84, 11.05222681049301 -84, 90.5 -84 M90.5 -84 C90.5 -44.91052621313292, 90.5 -5.821052426265837, 90.5 84 M90.5 -84 C90.5 -33.50223775968495, 90.5 16.995524480630095, 90.5 84 M90.5 84 C43.16092348209714 84, -4.178153035805721 84, -90.5 84 M90.5 84 C32.8025965084538 84, -24.894806983092394 84, -90.5 84 M-90.5 84 C-90.5 20.564353470791815, -90.5 -42.87129305841637, -90.5 -84 M-90.5 84 C-90.5 25.157029416164853, -90.5 -33.68594116767029, -90.5 -84"/></g><g transform="translate(0, -60)" class="annotation-group text"/><g transform="translate(-17.5625, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="35.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 83px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Todo</p></span></div></foreignObject></g></g><g transform="translate(-78.5, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="72.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 121px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: string</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="90.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+title: string</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="139.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 179px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+description: string</p></span></div></foreignObject></g></g><g transform="translate(-78.5, 84)" class="methods-group text"/><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-90.5 -36 C-33.52808597379735 -36, 23.443828052405294 -36, 90.5 -36 M-90.5 -36 C-47.00678526171281 -36, -3.5135705234256136 -36, 90.5 -36"/></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#9370DB" d="M-90.5 60 C-38.68513614152557 60, 13.129727716948864 60, 90.5 60 M-90.5 60 C-29.64796631797163 60, 31.20406736405674 60, 90.5 60"/></g></g></g></g></g></svg>