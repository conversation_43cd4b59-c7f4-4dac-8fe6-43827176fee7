<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="300" height="200" fill="#f8f9fa" rx="5" ry="5" />

  <!-- Title -->
  <text x="150" y="30" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#333">Status Implementation</text>

  <!-- State Diagram Elements -->
  <circle cx="75" cy="80" r="15" fill="#000" />
  <circle cx="75" cy="80" r="12" fill="#fff" />

  <ellipse cx="150" cy="80" rx="40" ry="25" fill="#c92a2a" />
  <text x="150" y="85" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#fff">State 1</text>

  <ellipse cx="150" cy="150" rx="40" ry="25" fill="#c92a2a" />
  <text x="150" y="155" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#fff">State 2</text>

  <ellipse cx="225" cy="80" rx="40" ry="25" fill="#c92a2a" />
  <text x="225" y="85" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#fff">State 3</text>

  <line x1="90" y1="80" x2="110" y2="80" stroke="#333" stroke-width="1.5" />
  <polygon points="110,80 100,75 100,85" fill="#333" />

  <line x1="150" y1="105" x2="150" y2="125" stroke="#333" stroke-width="1.5" />
  <polygon points="150,125 145,115 155,115" fill="#333" />

  <line x1="190" y1="80" x2="185" y2="80" stroke="#333" stroke-width="1.5" />
  <polygon points="185,80 195,75 195,85" fill="#333" />
</svg>