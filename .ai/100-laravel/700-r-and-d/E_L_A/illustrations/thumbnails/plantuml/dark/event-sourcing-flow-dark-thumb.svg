<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="300" height="200" fill="#2d333b" rx="5" ry="5" />

  <!-- Title -->
  <text x="150" y="30" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#adbac7">Event Sourcing Flow</text>

  <!-- Sequence Diagram Elements -->
  <!-- Actor: User -->
  <circle cx="75" cy="60" r="10" fill="#e67700" />
  <line x1="75" y1="70" x2="75" y2="100" stroke="#e67700" stroke-width="2" />
  <line x1="60" y1="85" x2="90" y2="85" stroke="#e67700" stroke-width="2" />
  <line x1="75" y1="100" x2="60" y2="120" stroke="#e67700" stroke-width="2" />
  <line x1="75" y1="100" x2="90" y2="120" stroke="#e67700" stroke-width="2" />
  <text x="75" y="140" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#adbac7">User</text>

  <!-- Actor: System -->
  <rect x="210" y="50" width="30" height="20" fill="#e67700" />
  <line x1="225" y1="70" x2="225" y2="120" stroke="#e67700" stroke-width="2" />
  <text x="225" y="140" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#adbac7">System</text>

  <!-- Sequence Arrows -->
  <line x1="75" y1="85" x2="215" y2="85" stroke="#adbac7" stroke-width="1.5" />
  <polygon points="215,85 205,80 205,90" fill="#adbac7" />
  <text x="145" y="80" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#adbac7">Request</text>

  <line x1="225" y1="110" x2="85" y2="110" stroke="#adbac7" stroke-width="1.5" stroke-dasharray="5,3" />
  <polygon points="85,110 95,105 95,115" fill="#adbac7" />
  <text x="155" y="105" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#adbac7">Response</text>

  <!-- Lifelines -->
  <line x1="75" y1="150" x2="75" y2="180" stroke="#adbac7" stroke-width="1" stroke-dasharray="4,2" />
  <line x1="225" y1="150" x2="225" y2="180" stroke="#adbac7" stroke-width="1" stroke-dasharray="4,2" />
</svg>