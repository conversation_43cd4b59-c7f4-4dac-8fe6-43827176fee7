# Laravel Ray

## 1. Overview

Ray is a powerful debugging tool by <PERSON><PERSON> that allows you to send debugging information to a desktop app, making it easier to analyze and explore the data.

### 1.1. Package Information

- **Package Name**: spatie/laravel-ray
- **Version**: ^1.40.2
- **GitHub**: [https://github.com/spatie/laravel-ray](https://github.com/spatie/laravel-ray)
- **Documentation**: [https://spatie.be/docs/ray/v1/introduction](https://spatie.be/docs/ray/v1/introduction)

## 2. Key Features

- Debug using a dedicated desktop application
- Display debugging information outside of the browser
- Colorize output for better visualization
- Show queries, requests, and views
- Advanced features like pause execution and count invocations
- Organize debug calls with labels and colors

## 3. Usage Examples

### 3.1. Basic Debugging

```php
<?php

declare(strict_types=1);

// Send any variable to Ray
ray('Hello world');

// Send multiple variables
ray('<PERSON>', '<PERSON><PERSON>', ['age' => 30]);

// Color output for better visibility
ray('Warning!')->red();
ray('Success!')->green();
ray('Info')->blue();
ray('Notice')->purple();
ray('Caution')->orange();
ray('Exception')->red();

// Display variable type
ray('string')->showType();
```

### 3.2. Advanced Features

```php
<?php

declare(strict_types=1);

// Show caller
ray()->caller();

// Pause execution (when Ray desktop app is open)
ray('Execution paused...')->pause();

// Count invocations
ray()->count();

// Clear screen
ray()->clearScreen();

// Display class information
ray(new User)->showClass();

// Tracking method calls
ray()->showQueries();

// View data
ray()->showViews();
```

## 4. Configuration

The Ray configuration file is located at `config/ray.php`:

```php
<?php

declare(strict_types=1);

return [
    /*
     * When enabled, all things logged to the application log
     * will also be sent to Ray.
     */
    'send_log_calls_to_ray' => env('SEND_LOG_CALLS_TO_RAY', true),

    /*
     * When enabled, all things passed to dump() or dd()
     * will also be sent to Ray.
     */
    'send_dumps_to_ray' => env('SEND_DUMPS_TO_RAY', true),

    /*
     * The host used to communicate with the Ray app.
     */
    'host' => env('RAY_HOST', 'localhost'),

    /*
     * The port number used to communicate with the Ray app.
     */
    'port' => env('RAY_PORT', 23517),

    /*
     * Absolute paths to files you want to exclude from sending to Ray.
     */
    'exclude_paths' => [
        'horizon/vendor',
    ],

    /*
     * When this setting is enabled, the package will not communicate
     * with the Ray app.
     */
    'enable' => env('RAY_ENABLED', true),
];
```

## 5. Using Ray in Testing

Ray is particularly useful in testing:

```php
<?php

declare(strict_types=1);

test('user can register', function () {
    // Catch all queries
    ray()->showQueries();
    
    // Register a user
    $response = $this->post('/register', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);
    
    // Check what was inserted
    ray()->showQueries()->blue();
    
    $response->assertRedirect('/home');
});
```

## 6. Installation of Ray Desktop App

To use Ray, you need to install the desktop application:

1. Download from [myray.app](https://myray.app/)
2. Install on your system
3. Run the app while developing

## 7. Best Practices

### 7.1. Use in Local Environment Only

Disable Ray in production environments:

```env
// In .env.production
RAY_ENABLED=false
```

### 7.2. Using with Tailwind CLI

When using Ray with Tailwind CLI, you might need to adjust:

```php
// In AppServiceProvider.php
if (app()->environment('local')) {
    ray()->showCache();
    ray()->showQueries();
}
```
