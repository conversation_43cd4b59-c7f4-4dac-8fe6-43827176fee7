# Testing Examples Index

## 1. Overview

This directory contains practical examples for various testing packages and approaches used in the project. Examples demonstrate how to effectively test different aspects of a Laravel 12 application with PHP 8.4.

## 2. Framework Examples

- [PHPUnit Examples](phpunit-examples.md)
- [Pest Examples](pest-examples.md)
- [Parallelization Examples](parallelization-examples.md)

## 3. Testing Types

- [Unit Testing Examples](unit-testing-examples.md)
- [Feature Testing Examples](feature-testing-examples.md)
- [Integration Testing Examples](integration-testing-examples.md)
- [Browser Testing Examples](browser-testing-examples.md)

## 4. Common Testing Scenarios

- [API Testing Examples](api-testing-examples.md)
- [Database Testing Examples](database-testing-examples.md)
- [Authentication Testing Examples](auth-testing-examples.md)
- [Middleware Testing Examples](middleware-testing-examples.md)

## 5. Advanced Testing Techniques

- [Mocking Examples](mocking-examples.md)
- [Test Data Factory Examples](factory-examples.md)
- [Test Coverage Analysis](coverage-examples.md)
- [Time Manipulation Examples](time-examples.md)

Each example includes complete code samples that can be copied and adapted for your specific testing needs.
