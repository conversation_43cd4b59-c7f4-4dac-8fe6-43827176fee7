<?php

declare(strict_types=1);

use Rector\Config\RectorConfig;
use Rector\Set\ValueObject\LevelSetList;
use Rector\Set\ValueObject\SetList;
use <PERSON>\TypeDeclaration\Rector\Property\TypedPropertyFromStrictConstructorRector;
use Driftingly\RectorLaravel\Set\LaravelSetList;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->paths([
        __DIR__.'/app',
        __DIR__.'/config',
        __DIR__.'/database',
        __DIR__.'/routes',
        __DIR__.'/tests',
    ]);

    // PHP 8.4 features
    $rectorConfig->sets([
        LevelSetList::UP_TO_PHP_84,
        SetList::CODE_QUALITY,
        SetList::DEAD_CODE,
        SetList::EARLY_RETURN,
        SetList::TYPE_DECLARATION,
        LaravelSetList::LARAVEL_100,
    ]);

    // Skip specific paths
    $rectorConfig->skip([
        __DIR__.'/vendor',
        __DIR__.'/storage',
        __DIR__.'/bootstrap/cache',

        // Skip specific rules for specific paths
        TypedPropertyFromStrictConstructorRector::class => [
            __DIR__.'/app/Legacy',
        ],
    ]);

    // Enable parallel processing
    $rectorConfig->parallel();

    // Auto-import names
    $rectorConfig->importNames();
};
