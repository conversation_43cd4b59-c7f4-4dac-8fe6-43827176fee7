# Configuration Examples Index

## 1. Overview

This directory contains reference configuration files for all development packages used in the project. These examples provide a starting point for implementing and customizing each tool according to project requirements.

## 2. Static Analysis Configurations

- [PHPStan Configuration](phpstan.neon.dist)
- [Larastan Configuration](larastan.neon.dist)
- [PHP Mess Detector Configuration](phpmd.xml)
- [PHP Insights Configuration](phpinsights.php)

## 3. Code Style Configurations

- [Laravel Pint Configuration](pint.json)
- [PHP_CodeSniffer Configuration](phpcs.xml)
- [PHP-CS-Fixer Configuration](php-cs-fixer.php)
- [EditorConfig](editorconfig)

## 4. Testing Configurations

- [PHPUnit Configuration](phpunit.xml.dist)
- [Pest Configuration](pest.php)
- [Laravel Dusk Configuration](dusk.php)
- [PHPBench Configuration](phpbench.json)

## 5. CI/CD Configurations

- [GitHub Actions Workflow](github-workflow-ci.yml)
- [GitLab CI Configuration](gitlab-ci.yml)
- [Bitbucket Pipelines](bitbucket-pipelines.yml)

## 6. Security Configurations

- [Security Checker Configuration](security-checker.yml)
- [PHP Security Analysis Configuration](security-analysis.yml)

## 7. Development Environment Configurations

- [Docker Compose Configuration](docker-compose.yml)
- [Laravel Sail Configuration](sail.yml)
- [Laravel Telescope Configuration](telescope.php)

## 8. Code Generation Configurations

- [IDE Helper Configuration](ide-helper.php)
- [OpenAPI Configuration](openapi.yml)
- [Swagger Configuration](swagger.yml)

## 9. Using These Configurations

For implementation instructions, refer to the specific package documentation in the parent directories. Each configuration file includes comments explaining key settings and customization options.
