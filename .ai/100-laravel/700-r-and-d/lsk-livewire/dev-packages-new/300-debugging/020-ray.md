# Laravel Ray

## 1. Overview

Laravel Ray is a debugging tool by <PERSON><PERSON> that allows you to debug your Laravel applications by sending data to a beautiful desktop application. It provides a more powerful alternative to `dd()` and `dump()` functions, allowing you to continue execution while inspecting values.

### 1.1. Package Information

- **Package Name**: spatie/laravel-ray
- **Version**: ^1.40.2
- **GitHub**: [https://github.com/spatie/laravel-ray](https://github.com/spatie/laravel-ray)
- **Documentation**: [https://spatie.be/docs/ray/v1/introduction](https://spatie.be/docs/ray/v1/introduction)

## 2. Key Features

- Debug without interrupting execution
- Beautiful desktop application
- Color coding for different types of data
- Pause execution with breakpoints
- Filter data by origin
- Measure performance with timers
- Track exceptions
- Inspect queries, requests, and views
- Customizable layout
- Support for multiple projects
- Caller detection
- Remote debugging

## 3. Installation

### 3.1. Package Installation

```bash
composer require --dev spatie/laravel-ray
```

### 3.2. Desktop App Installation

Download the Ray desktop application from [myray.app](https://myray.app/).

### 3.3. Configuration

Publish the configuration file:

```bash
php artisan vendor:publish --provider="Spatie\LaravelRay\RayServiceProvider"
```

This creates a `config/ray.php` file.

## 4. Configuration

### 4.1. Basic Configuration

The main configuration options in `config/ray.php`:

```php
<?php

return [
    // Enable or disable Ray
    'enable' => env('RAY_ENABLED', true),
    
    // Host where the Ray app is running
    'host' => env('RAY_HOST', 'localhost'),
    
    // Port number Ray is listening on
    'port' => env('RAY_PORT', 23517),
    
    // Automatically send exceptions to Ray
    'send_exceptions_to_ray' => env('SEND_EXCEPTIONS_TO_RAY', true),
    
    // Automatically send log calls to Ray
    'send_logs_to_ray' => env('SEND_LOGS_TO_RAY', true),
    
    // Automatically send dumps to Ray
    'send_dumps_to_ray' => env('SEND_DUMPS_TO_RAY', true),
    
    // Automatically send jobs to Ray
    'send_jobs_to_ray' => env('SEND_JOBS_TO_RAY', false),
    
    // Automatically send cache to Ray
    'send_cache_to_ray' => env('SEND_CACHE_TO_RAY', false),
    
    // Automatically send http client requests to Ray
    'send_http_client_requests_to_ray' => env('SEND_HTTP_CLIENT_REQUESTS_TO_RAY', false),
    
    // Automatically send views to Ray
    'send_views_to_ray' => env('SEND_VIEWS_TO_RAY', false),
    
    // Automatically send queries to Ray
    'send_queries_to_ray' => env('SEND_QUERIES_TO_RAY', false),
    
    // Automatically send model events to Ray
    'send_model_events_to_ray' => env('SEND_MODEL_EVENTS_TO_RAY', false),
];
```

### 4.2. Environment Configuration

In your `.env` file:

```
# Enable or disable Ray
RAY_ENABLED=true

# Configure which data to send
SEND_EXCEPTIONS_TO_RAY=true
SEND_LOGS_TO_RAY=true
SEND_DUMPS_TO_RAY=true
SEND_JOBS_TO_RAY=false
SEND_CACHE_TO_RAY=false
SEND_HTTP_CLIENT_REQUESTS_TO_RAY=false
SEND_VIEWS_TO_RAY=false
SEND_QUERIES_TO_RAY=false
SEND_MODEL_EVENTS_TO_RAY=false
```

## 5. Usage

### 5.1. Basic Usage

Send data to Ray:

```php
// Send a string
ray('Hello world');

// Send multiple values
ray('Hello', 'world');

// Send variables
$user = User::find(1);
ray($user);

// Chain methods
ray('Hello world')
    ->green()
    ->large()
    ->label('Greeting');
```

### 5.2. Color Coding

Apply colors to distinguish between different types of data:

```php
ray('Success message')->green();
ray('Warning message')->orange();
ray('Error message')->red();
ray('Info message')->blue();
ray('Debug message')->purple();
```

### 5.3. Sizing

Change the size of displayed data:

```php
ray('Small text')->small();
ray('Normal text');
ray('Large text')->large();
```

### 5.4. Labels

Add labels to your data:

```php
ray('Hello world')->label('Greeting');
```

### 5.5. Clearing the Screen

Clear the Ray screen:

```php
ray()->clearScreen();
```

### 5.6. Caller Information

Show where the ray call was made:

```php
ray()->caller();
```

### 5.7. Pausing Execution

Pause execution until you press continue in the Ray app:

```php
ray()->pause();
```

### 5.8. Counting

Count the number of times a piece of code is called:

```php
ray()->count();
```

### 5.9. Measuring Performance

Measure the time between calls:

```php
ray()->measure();
// Some code...
ray()->measure();
```

### 5.10. Showing Queries

Show database queries:

```php
ray()->showQueries();
User::all();
ray()->stopShowingQueries();
```

### 5.11. Showing Events

Show events:

```php
ray()->showEvents();
event(new OrderShipped());
ray()->stopShowingEvents();
```

### 5.12. Showing Jobs

Show jobs:

```php
ray()->showJobs();
dispatch(new ProcessOrder());
ray()->stopShowingJobs();
```

### 5.13. Showing Cache

Show cache operations:

```php
ray()->showCache();
Cache::put('key', 'value');
ray()->stopShowingCache();
```

## 6. Integration with Laravel 12 and PHP 8.4

Laravel Ray is fully compatible with Laravel 12 and PHP 8.4. It includes support for:

- Livewire components
- Volt components
- Laravel's new Folio pages
- PHP 8.4 features

## 7. Advanced Usage

### 7.1. Conditional Ray

Only send to Ray when a condition is met:

```php
ray()->showIf($condition);
```

### 7.2. Remote Debugging

Debug on a remote server:

```php
// config/ray.php
'host' => 'your-remote-ip',
```

### 7.3. Custom Payloads

Create custom payloads:

```php
use Spatie\Ray\Payloads\Payload;

class CustomPayload extends Payload
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function getType(): string
    {
        return 'custom';
    }

    public function getContent(): array
    {
        return [
            'data' => $this->data,
        ];
    }
}

// Use the custom payload
ray()->sendCustomPayload(new CustomPayload('Custom data'));
```

## 8. Best Practices

1. **Remove Ray Calls**: Remove or comment out Ray calls before committing code
2. **Use Macros**: Create macros for common debugging patterns
3. **Selective Enabling**: Only enable the features you need
4. **Use in Tests**: Ray works great in tests too
5. **Combine with Other Tools**: Use Ray alongside Laravel Debugbar and Telescope

## 9. Troubleshooting

### 9.1. Ray App Not Receiving Data

If the Ray app isn't receiving data:

1. Check that the Ray app is running
2. Verify the host and port in `config/ray.php`
3. Check firewall settings
4. Ensure Ray is enabled in your environment

### 9.2. Performance Impact

Ray can impact performance, especially when sending large amounts of data:

1. Only enable the features you need
2. Be selective about what you send to Ray
3. Disable Ray in production
