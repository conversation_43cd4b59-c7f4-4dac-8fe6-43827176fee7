# 3.5 Laravel Fortify 2FA Implementation - User Model Transformation

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 20 minutes

## 3.5.1 Executive Summary

This document provides comprehensive instructions for transforming the User model to support unified Laravel Fortify authentication while preserving existing Filament functionality during the transition period. The transformation enables seamless integration with Fortify's 2FA system while maintaining backward compatibility.

## 3.5.2 Current User Model Analysis

### ******* Existing User Model Structure

**Current Implementation** (from system analysis):

<augment_code_snippet path="app/Models/User.php" mode="EXCERPT">
````php
class User extends Authenticatable implements 
    FilamentUser, 
    HasA<PERSON>Authentication, 
    HasAppAuthenticationRecovery,
    HasAvatar, 
    HasEmailAuthentication, 
    MustVerifyEmail
{
    use HasFactory, Notifiable, SoftDeletes, HasSlug, LogsActivity, Userstamps;
````
</augment_code_snippet>

**Current Interface Dependencies**:

| Interface | Purpose | Fortify Replacement Strategy |
|-----------|---------|------------------------------|
| `FilamentUser` | Filament panel access | ✅ Keep (for panel integration) |
| `HasAppAuthentication` | Filament TOTP 2FA | 🔄 Replace with Fortify |
| `HasAppAuthenticationRecovery` | Filament recovery codes | 🔄 Replace with Fortify |
| `HasAvatar` | Filament user avatars | ✅ Keep (UI feature) |
| `HasEmailAuthentication` | Filament email 2FA | 🔄 Remove (transition period) |
| `MustVerifyEmail` | Email verification | ✅ Keep (Fortify compatible) |

## 3.5.3 User Model Transformation Implementation

### ******* Step 1: Add Fortify Imports and Trait

**Update User Model Imports**:

```php
<?php
// app/Models/User.php

namespace App\Models;

// Existing imports (keep all)
use Filament\Auth\MultiFactor\App\Contracts\HasAppAuthentication;
use Filament\Auth\MultiFactor\App\Contracts\HasAppAuthenticationRecovery;
use Filament\Auth\MultiFactor\Email\Contracts\HasEmailAuthentication;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Symfony\Component\Uid\Ulid;
use Wildside\Userstamps\Userstamps;

// Add Fortify imports
use Laravel\Fortify\TwoFactorAuthenticatable;
```

### 3.5.3.2 Step 2: Update Class Declaration

**Transition Phase Implementation** (preserves Filament interfaces during migration):

```php
class User extends Authenticatable implements 
    FilamentUser,                    // Keep for Filament panel access
    HasAppAuthentication,            // Keep during transition period
    HasAppAuthenticationRecovery,    // Keep during transition period
    HasAvatar,                       // Keep for Filament UI
    HasEmailAuthentication,          // Keep during transition period
    MustVerifyEmail                  // Keep for email verification
{
    use HasFactory, 
        Notifiable, 
        SoftDeletes, 
        HasSlug, 
        LogsActivity, 
        Userstamps,
        TwoFactorAuthenticatable;    // Add Fortify trait
```

**Final Phase Implementation** (after migration complete):

```php
class User extends Authenticatable implements 
    FilamentUser,                    // Keep for Filament panel access
    HasAvatar,                       // Keep for Filament UI
    MustVerifyEmail                  // Keep for email verification
{
    use HasFactory, 
        Notifiable, 
        SoftDeletes, 
        HasSlug, 
        LogsActivity, 
        Userstamps,
        TwoFactorAuthenticatable;    // Primary 2FA system
        
    // Remove Filament 2FA interfaces after migration:
    // HasAppAuthentication, 
    // HasAppAuthenticationRecovery,
    // HasEmailAuthentication,
}
```

### 3.5.3.3 Step 3: Update Model Properties

**Enhanced Fillable Fields**:

```php
/**
 * The attributes that are mass assignable.
 */
protected $fillable = [
    'name',
    'email', 
    'password',
    'slug',
    'public_id',
    
    // Keep Filament field during transition
    'has_email_authentication',
];
```

**Enhanced Hidden Fields**:

```php
/**
 * The attributes that should be hidden for serialization.
 */
protected $hidden = [
    'password',
    'remember_token',
    
    // Filament 2FA fields (preserve during transition)
    'app_authentication_secret',
    'app_authentication_recovery_codes',
    
    // Fortify 2FA fields (primary system)
    'two_factor_secret',
    'two_factor_recovery_codes',
];
```

**Enhanced Casts**:

```php
/**
 * Get the attributes that should be cast.
 */
protected function casts(): array
{
    return [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        
        // Filament 2FA casts (preserve during transition)
        'app_authentication_secret' => 'encrypted',
        'app_authentication_recovery_codes' => 'encrypted:array',
        'has_email_authentication' => 'boolean',
        
        // Fortify 2FA casts (primary system)
        'two_factor_confirmed_at' => 'datetime',
    ];
}
```

## 3.5.4 Enhanced User Model Methods

### 3.5.4.1 Fortify 2FA Integration Methods

**Add Fortify-specific methods to User model**:

```php
/**
 * Determine if the user has enabled two-factor authentication for Fortify.
 */
public function hasEnabledTwoFactorAuthentication(): bool
{
    return !is_null($this->two_factor_secret) && 
           !is_null($this->two_factor_confirmed_at);
}

/**
 * Get the QR code SVG for Fortify 2FA setup.
 */
public function twoFactorQrCodeSvg(): string
{
    $google2fa = app(\PragmaRX\Google2FA\Google2FA::class);
    
    return $google2fa->getQRCodeInline(
        config('app.name'),
        $this->email,
        decrypt($this->two_factor_secret)
    );
}

/**
 * Get the two-factor authentication recovery codes.
 */
public function recoveryCodes(): array
{
    return json_decode(decrypt($this->two_factor_recovery_codes), true) ?? [];
}

/**
 * Replace the given recovery code with a new one in the recovery codes.
 */
public function replaceRecoveryCode(string $code): void
{
    $this->forceFill([
        'two_factor_recovery_codes' => encrypt(str_replace(
            $code,
            \Illuminate\Support\Str::random(10).'-'.\Illuminate\Support\Str::random(10),
            decrypt($this->two_factor_recovery_codes)
        )),
    ])->save();
}

/**
 * Confirm the user's two-factor authentication setup.
 */
public function confirmTwoFactorAuth(string $code): bool
{
    $google2fa = app(\PragmaRX\Google2FA\Google2FA::class);
    
    if ($google2fa->verifyKey(decrypt($this->two_factor_secret), $code)) {
        $this->forceFill([
            'two_factor_confirmed_at' => now(),
        ])->save();
        
        return true;
    }
    
    return false;
}
```

### 3.5.4.2 Migration Support Methods

**Add methods to support data migration**:

```php
/**
 * Validate Fortify migration for this user.
 */
public function validateFortifyMigration(): array
{
    $validation = [
        'has_filament_2fa' => !is_null($this->app_authentication_secret),
        'has_fortify_2fa' => !is_null($this->two_factor_secret),
        'fortify_confirmed' => !is_null($this->two_factor_confirmed_at),
        'data_consistency' => true,
        'migration_needed' => false,
    ];
    
    // Check if migration is needed
    if ($validation['has_filament_2fa'] && !$validation['has_fortify_2fa']) {
        $validation['migration_needed'] = true;
        $validation['issue'] = 'Filament 2FA exists but Fortify 2FA missing';
    }
    
    // Check data consistency
    if ($validation['has_fortify_2fa'] && !$validation['fortify_confirmed']) {
        $validation['data_consistency'] = false;
        $validation['issue'] = 'Fortify 2FA exists but not confirmed';
    }
    
    return $validation;
}

/**
 * Migrate Filament 2FA data to Fortify fields.
 */
public function migrateToFortify2FA(): bool
{
    if (is_null($this->app_authentication_secret)) {
        return false; // No Filament 2FA to migrate
    }
    
    if (!is_null($this->two_factor_secret)) {
        return true; // Already migrated
    }
    
    try {
        $this->forceFill([
            'two_factor_secret' => $this->app_authentication_secret,
            'two_factor_recovery_codes' => $this->app_authentication_recovery_codes,
            'two_factor_confirmed_at' => now(), // Mark as confirmed since it was active in Filament
        ])->save();
        
        \Illuminate\Support\Facades\Log::info('User 2FA migrated to Fortify', [
            'user_id' => $this->id,
            'email' => $this->email,
        ]);
        
        return true;
    } catch (Exception $e) {
        \Illuminate\Support\Facades\Log::error('Failed to migrate user 2FA data', [
            'user_id' => $this->id,
            'error' => $e->getMessage()
        ]);
        
        return false;
    }
}

/**
 * Check if user can access Filament panel with Fortify authentication.
 */
public function canAccessPanel(Panel $panel): bool
{
    // Require Fortify 2FA for admin panel access
    if (!$this->hasEnabledTwoFactorAuthentication()) {
        return false;
    }
    
    // Add any additional panel-specific authorization logic here
    // For example, check user roles, permissions, etc.
    
    return true;
}
```

### 3.5.4.3 Transition Period Support

**Methods to support dual system during transition**:

```php
/**
 * Check if user has any form of 2FA enabled (Filament or Fortify).
 */
public function hasAnyTwoFactorAuthentication(): bool
{
    return $this->hasFilament2FA() || $this->hasEnabledTwoFactorAuthentication();
}

/**
 * Check if user has Filament 2FA enabled.
 */
public function hasFilament2FA(): bool
{
    return !is_null($this->app_authentication_secret);
}

/**
 * Get the primary 2FA system for this user.
 */
public function getPrimary2FASystem(): string
{
    if ($this->hasEnabledTwoFactorAuthentication()) {
        return 'fortify';
    }
    
    if ($this->hasFilament2FA()) {
        return 'filament';
    }
    
    return 'none';
}

/**
 * Get 2FA status summary for admin interface.
 */
public function get2FAStatus(): array
{
    return [
        'primary_system' => $this->getPrimary2FASystem(),
        'fortify_enabled' => $this->hasEnabledTwoFactorAuthentication(),
        'filament_enabled' => $this->hasFilament2FA(),
        'migration_needed' => $this->hasFilament2FA() && !$this->hasEnabledTwoFactorAuthentication(),
        'confirmed_at' => $this->two_factor_confirmed_at,
    ];
}
```

## 3.5.5 Model Factory Updates

### 3.5.5.1 Enhanced User Factory

**Update UserFactory for testing**:

```php
<?php
// database/factories/UserFactory.php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user has Filament 2FA enabled.
     */
    public function withFilament2FA(): static
    {
        return $this->state(fn (array $attributes) => [
            'app_authentication_secret' => encrypt('JBSWY3DPEHPK3PXP'),
            'app_authentication_recovery_codes' => encrypt([
                'code1-code1',
                'code2-code2',
                'code3-code3',
            ]),
            'has_email_authentication' => true,
        ]);
    }

    /**
     * Indicate that the user has Fortify 2FA enabled.
     */
    public function withFortify2FA(): static
    {
        return $this->state(fn (array $attributes) => [
            'two_factor_secret' => encrypt('JBSWY3DPEHPK3PXP'),
            'two_factor_recovery_codes' => encrypt(json_encode([
                'fortify1-code1',
                'fortify2-code2',
                'fortify3-code3',
            ])),
            'two_factor_confirmed_at' => now(),
        ]);
    }

    /**
     * Indicate that the user has both Filament and Fortify 2FA (migration scenario).
     */
    public function withBoth2FA(): static
    {
        return $this->withFilament2FA()->withFortify2FA();
    }
}
```

## 3.5.6 Model Validation and Testing

### ******* Model Validation Command

**Create validation command for User model**:

```bash
# Create validation command
php artisan make:command ValidateUserModel
```

```php
<?php
// app/Console/Commands/ValidateUserModel.php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class ValidateUserModel extends Command
{
    protected $signature = 'user:validate-model';
    protected $description = 'Validate User model Fortify integration';

    public function handle(): int
    {
        $this->info('🔍 Validating User model Fortify integration...');
        
        // Test model instantiation
        $user = new User();
        
        // Validate interfaces
        $this->validateInterfaces($user);
        
        // Validate traits
        $this->validateTraits($user);
        
        // Validate methods
        $this->validateMethods($user);
        
        $this->info('✅ User model validation completed successfully!');
        return 0;
    }
    
    private function validateInterfaces(User $user): void
    {
        $this->line('📋 Checking interfaces...');
        
        $requiredInterfaces = [
            \Filament\Models\Contracts\FilamentUser::class,
            \Filament\Models\Contracts\HasAvatar::class,
            \Illuminate\Contracts\Auth\MustVerifyEmail::class,
        ];
        
        foreach ($requiredInterfaces as $interface) {
            if ($user instanceof $interface) {
                $this->line("  ✅ Implements: " . class_basename($interface));
            } else {
                $this->error("  ❌ Missing interface: " . class_basename($interface));
            }
        }
    }
    
    private function validateTraits(User $user): void
    {
        $this->line('🔧 Checking traits...');
        
        $traits = class_uses_recursive(User::class);
        
        $requiredTraits = [
            \Laravel\Fortify\TwoFactorAuthenticatable::class,
            \Spatie\Activitylog\Traits\LogsActivity::class,
            \Wildside\Userstamps\Userstamps::class,
        ];
        
        foreach ($requiredTraits as $trait) {
            if (in_array($trait, $traits)) {
                $this->line("  ✅ Uses trait: " . class_basename($trait));
            } else {
                $this->error("  ❌ Missing trait: " . class_basename($trait));
            }
        }
    }
    
    private function validateMethods(User $user): void
    {
        $this->line('⚙️  Checking methods...');
        
        $requiredMethods = [
            'hasEnabledTwoFactorAuthentication',
            'twoFactorQrCodeSvg',
            'recoveryCodes',
            'confirmTwoFactorAuth',
            'validateFortifyMigration',
            'migrateToFortify2FA',
        ];
        
        foreach ($requiredMethods as $method) {
            if (method_exists($user, $method)) {
                $this->line("  ✅ Method exists: {$method}");
            } else {
                $this->error("  ❌ Missing method: {$method}");
            }
        }
    }
}
```

---

**Navigation Footer**

← [Previous: Implementation Guide](030-implementation-guide-unified.md) | [Next: Filament Integration →](040-filament-integration.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/035-user-model-transformation.md`
- **Document ID**: LF-2FA-005-UNIFIED
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
