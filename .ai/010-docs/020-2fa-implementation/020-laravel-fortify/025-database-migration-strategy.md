# 2.5 Laravel Fortify 2FA Implementation - Database Migration Strategy

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 20 minutes

## 2.5.1 Executive Summary

This document provides comprehensive database migration strategy for transitioning from Filament's built-in 2FA to Laravel Fortify as the primary authentication system. The strategy ensures zero data loss while enabling seamless migration of existing user 2FA configurations to the unified Fortify system.

### ******* Migration Strategy Overview

| Migration Phase | Description | Risk Level | Data Safety | Rollback Complexity |
|----------------|-------------|------------|-------------|-------------------|
| **Phase 1** | Add Fortify fields | 🟢 Low | ✅ Full preservation | Simple |
| **Phase 2** | Migrate existing data | 🟡 Medium | ✅ Validated migration | Moderate |
| **Phase 3** | Validate and activate | 🟡 Medium | ✅ Integrity checks | Moderate |
| **Phase 4** | Cleanup (optional)** | 🟢 Low | ✅ Backup preserved | Simple |

## 2.5.2 Current Database Schema Analysis

### ******* Existing Filament 2FA Fields

**Current User Table Structure** (verified from system analysis):

```sql
-- Existing Filament 2FA fields in users table
id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
name VARCHAR(255) NOT NULL,
email VARCHAR(255) UNIQUE NOT NULL,
email_verified_at TIMESTAMP NULL,
password VARCHAR(255) NOT NULL,
remember_token VARCHAR(100) NULL,
slug VARCHAR(255) UNIQUE NULL,
public_id VARCHAR(26) UNIQUE NOT NULL,
created_at TIMESTAMP NULL,
updated_at TIMESTAMP NULL,
deleted_at TIMESTAMP NULL,

-- Filament 2FA fields (current system)
app_authentication_secret TEXT NULL,
app_authentication_recovery_codes TEXT NULL,
has_email_authentication BOOLEAN DEFAULT FALSE,

-- Audit fields (from Userstamps)
created_by BIGINT UNSIGNED NULL,
updated_by BIGINT UNSIGNED NULL,
deleted_by BIGINT UNSIGNED NULL
```

**Current Field Analysis**:

| Field | Data Type | Encryption | Usage | Migration Priority |
|-------|-----------|------------|-------|-------------------|
| `app_authentication_secret` | TEXT | ✅ Encrypted | TOTP secret | 🔴 Critical |
| `app_authentication_recovery_codes` | TEXT | ✅ Encrypted array | Recovery codes | 🔴 Critical |
| `has_email_authentication` | BOOLEAN | ❌ Plain | Email 2FA flag | 🟡 Medium |

### 2.5.2.2 Target Fortify Schema Requirements

**Required Fortify 2FA Fields**:

```sql
-- Primary Fortify 2FA fields (target system)
two_factor_secret TEXT NULL COMMENT 'Encrypted TOTP secret for Laravel Fortify 2FA',
two_factor_recovery_codes TEXT NULL COMMENT 'Encrypted recovery codes for Laravel Fortify 2FA',
two_factor_confirmed_at TIMESTAMP NULL COMMENT 'Timestamp when Fortify 2FA was confirmed and activated'
```

**Field Mapping Strategy**:

| Source (Filament) | Target (Fortify) | Migration Logic |
|------------------|------------------|-----------------|
| `app_authentication_secret` | `two_factor_secret` | Direct copy with validation |
| `app_authentication_recovery_codes` | `two_factor_recovery_codes` | Direct copy with validation |
| `has_email_authentication` | N/A | Preserve for transition period |
| N/A | `two_factor_confirmed_at` | Set to `now()` if secret exists |

## 2.5.3 Migration Implementation Strategy

### 2.5.3.1 Phase 1: Schema Extension Migration

**Migration File**: `database/migrations/2025_07_01_120000_add_fortify_two_factor_fields_to_users_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add Laravel Fortify 2FA columns
            $table->text('two_factor_secret')
                  ->nullable()
                  ->after('remember_token')
                  ->comment('Encrypted TOTP secret for Laravel Fortify 2FA');
                  
            $table->text('two_factor_recovery_codes')
                  ->nullable()
                  ->after('two_factor_secret')
                  ->comment('Encrypted recovery codes for Laravel Fortify 2FA');
                  
            $table->timestamp('two_factor_confirmed_at')
                  ->nullable()
                  ->after('two_factor_recovery_codes')
                  ->comment('Timestamp when Fortify 2FA was confirmed and activated');
        });
        
        Log::info('Fortify 2FA schema extension completed', [
            'timestamp' => now(),
            'migration' => class_basename(static::class)
        ]);
    }

    /**
     * Reverse the migrations with safety validation.
     */
    public function down(): void
    {
        // Safety check: Ensure no critical Fortify data exists
        $fortifyUsersCount = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->orWhereNotNull('two_factor_confirmed_at')
            ->count();
        
        if ($fortifyUsersCount > 0) {
            throw new Exception(
                "Cannot rollback: {$fortifyUsersCount} users have Fortify 2FA data. " .
                "Please migrate data back to Filament fields before rolling back."
            );
        }
        
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'two_factor_confirmed_at',
                'two_factor_recovery_codes', 
                'two_factor_secret'
            ]);
        });
        
        Log::info('Fortify 2FA schema rollback completed', [
            'timestamp' => now(),
            'preserved_filament_users' => DB::table('users')->whereNotNull('app_authentication_secret')->count()
        ]);
    }
};
```

### 2.5.3.2 Phase 2: Data Migration Implementation

**Migration File**: `database/migrations/2025_07_01_120001_migrate_filament_to_fortify_2fa_data.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations - Migrate Filament 2FA data to Fortify fields.
     */
    public function up(): void
    {
        $migratedCount = 0;
        $errorCount = 0;
        
        // Process users in chunks to handle large datasets
        DB::table('users')
            ->whereNotNull('app_authentication_secret')
            ->chunk(100, function ($users) use (&$migratedCount, &$errorCount) {
                foreach ($users as $user) {
                    try {
                        // Validate Filament data before migration
                        if (empty($user->app_authentication_secret)) {
                            continue;
                        }
                        
                        // Prepare migration data
                        $migrationData = [
                            'two_factor_secret' => $user->app_authentication_secret,
                            'two_factor_recovery_codes' => $user->app_authentication_recovery_codes,
                            'two_factor_confirmed_at' => now(), // Mark as confirmed since it was active in Filament
                            'updated_at' => now(),
                        ];
                        
                        // Perform migration
                        DB::table('users')
                            ->where('id', $user->id)
                            ->update($migrationData);
                        
                        $migratedCount++;
                        
                    } catch (Exception $e) {
                        $errorCount++;
                        Log::error('Failed to migrate user 2FA data', [
                            'user_id' => $user->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });
        
        Log::info('Filament to Fortify 2FA data migration completed', [
            'migrated_users' => $migratedCount,
            'errors' => $errorCount,
            'timestamp' => now()
        ]);
        
        if ($errorCount > 0) {
            throw new Exception("Migration completed with {$errorCount} errors. Check logs for details.");
        }
    }

    /**
     * Reverse the migrations - Restore Filament data from Fortify fields.
     */
    public function down(): void
    {
        $restoredCount = 0;
        
        // Only restore if Filament fields are empty and Fortify fields have data
        DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->whereNull('app_authentication_secret')
            ->chunk(100, function ($users) use (&$restoredCount) {
                foreach ($users as $user) {
                    try {
                        DB::table('users')
                            ->where('id', $user->id)
                            ->update([
                                'app_authentication_secret' => $user->two_factor_secret,
                                'app_authentication_recovery_codes' => $user->two_factor_recovery_codes,
                                'has_email_authentication' => !is_null($user->two_factor_confirmed_at),
                                'updated_at' => now(),
                            ]);
                        
                        $restoredCount++;
                        
                    } catch (Exception $e) {
                        Log::error('Failed to restore user 2FA data', [
                            'user_id' => $user->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });
        
        Log::info('Fortify to Filament 2FA data restoration completed', [
            'restored_users' => $restoredCount,
            'timestamp' => now()
        ]);
    }
};
```

### 2.5.3.3 Phase 3: Performance Optimization Migration

**Migration File**: `database/migrations/2025_07_01_120002_add_fortify_2fa_indexes.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Performance indexes for Fortify 2FA queries
            $table->index('two_factor_confirmed_at', 'users_fortify_2fa_confirmed_index');
            $table->index(['email', 'two_factor_confirmed_at'], 'users_email_fortify_2fa_index');
            $table->index(['two_factor_secret', 'two_factor_confirmed_at'], 'users_fortify_2fa_status_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_fortify_2fa_status_index');
            $table->dropIndex('users_email_fortify_2fa_index');
            $table->dropIndex('users_fortify_2fa_confirmed_index');
        });
    }
};
```

## 2.5.4 Data Validation and Integrity Checks

### ******* Migration Validation Command

**Artisan Command**: `app/Console/Commands/ValidateFortifyMigration.php`

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ValidateFortifyMigration extends Command
{
    protected $signature = 'fortify:validate-migration';
    protected $description = 'Validate Fortify 2FA migration integrity and data consistency';

    public function handle(): int
    {
        $this->info('🔍 Validating Fortify 2FA migration...');
        
        $validationResults = [
            'schema' => $this->validateSchema(),
            'data_migration' => $this->validateDataMigration(),
            'data_integrity' => $this->validateDataIntegrity(),
            'performance' => $this->validatePerformance(),
        ];
        
        $allPassed = collect($validationResults)->every(fn($result) => $result);
        
        if ($allPassed) {
            $this->info('✅ All validation checks passed successfully!');
            return 0;
        } else {
            $this->error('❌ Some validation checks failed. Please review the output above.');
            return 1;
        }
    }
    
    private function validateSchema(): bool
    {
        $this->line('📋 Checking schema...');
        
        $requiredColumns = ['two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at'];
        $existingColumns = Schema::getColumnListing('users');
        
        foreach ($requiredColumns as $column) {
            if (!in_array($column, $existingColumns)) {
                $this->error("  ❌ Missing Fortify column: {$column}");
                return false;
            }
            $this->line("  ✅ Fortify column exists: {$column}");
        }
        
        // Verify Filament columns are preserved
        $filamentColumns = ['app_authentication_secret', 'app_authentication_recovery_codes', 'has_email_authentication'];
        foreach ($filamentColumns as $column) {
            if (!in_array($column, $existingColumns)) {
                $this->warn("  ⚠️  Filament column missing: {$column}");
            } else {
                $this->line("  ✅ Filament column preserved: {$column}");
            }
        }
        
        return true;
    }
    
    private function validateDataMigration(): bool
    {
        $this->line('📊 Checking data migration...');
        
        // Count users with Filament 2FA
        $filamentUsers = DB::table('users')
            ->whereNotNull('app_authentication_secret')
            ->count();
        
        // Count users with Fortify 2FA
        $fortifyUsers = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->count();
        
        $this->line("  📈 Filament 2FA users: {$filamentUsers}");
        $this->line("  📈 Fortify 2FA users: {$fortifyUsers}");
        
        if ($filamentUsers > 0 && $fortifyUsers === 0) {
            $this->error("  ❌ Migration not completed: Filament users exist but no Fortify users found");
            return false;
        }
        
        if ($filamentUsers > 0 && $fortifyUsers !== $filamentUsers) {
            $this->warn("  ⚠️  Partial migration: {$fortifyUsers}/{$filamentUsers} users migrated");
        } else {
            $this->line("  ✅ Migration completed successfully");
        }
        
        return true;
    }
    
    private function validateDataIntegrity(): bool
    {
        $this->line('🔒 Checking data integrity...');
        
        // Check for users with Fortify 2FA but no confirmation timestamp
        $unconfirmedFortify = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->whereNull('two_factor_confirmed_at')
            ->count();
            
        if ($unconfirmedFortify > 0) {
            $this->warn("  ⚠️  Found {$unconfirmedFortify} users with Fortify 2FA secrets but no confirmation timestamp");
        } else {
            $this->line("  ✅ All Fortify 2FA users have confirmation timestamps");
        }
        
        // Check for orphaned confirmation timestamps
        $orphanedConfirmations = DB::table('users')
            ->whereNotNull('two_factor_confirmed_at')
            ->whereNull('two_factor_secret')
            ->count();
            
        if ($orphanedConfirmations > 0) {
            $this->error("  ❌ Found {$orphanedConfirmations} users with confirmation timestamps but no 2FA secrets");
            return false;
        } else {
            $this->line("  ✅ No orphaned confirmation timestamps found");
        }
        
        return true;
    }
    
    private function validatePerformance(): bool
    {
        $this->line('⚡ Checking performance...');
        
        $start = microtime(true);
        
        // Test common Fortify 2FA queries
        DB::table('users')->where('two_factor_confirmed_at', '!=', null)->count();
        DB::table('users')->where('email', '<EMAIL>')->where('two_factor_confirmed_at', '!=', null)->first();
        
        $duration = (microtime(true) - $start) * 1000; // Convert to milliseconds
        
        if ($duration > 100) {
            $this->warn("  ⚠️  Query performance: {$duration}ms (consider index optimization)");
        } else {
            $this->line("  ✅ Query performance: {$duration}ms");
        }
        
        return true;
    }
}
```

### 2.5.4.2 Data Consistency Verification

**User Model Methods for Validation**:

```php
// Add to User model for migration validation
public function validateFortifyMigration(): array
{
    $validation = [
        'has_filament_2fa' => !is_null($this->app_authentication_secret),
        'has_fortify_2fa' => !is_null($this->two_factor_secret),
        'fortify_confirmed' => !is_null($this->two_factor_confirmed_at),
        'data_consistency' => true,
    ];
    
    // Check data consistency
    if ($validation['has_filament_2fa'] && !$validation['has_fortify_2fa']) {
        $validation['data_consistency'] = false;
        $validation['issue'] = 'Filament 2FA exists but Fortify 2FA missing';
    }
    
    if ($validation['has_fortify_2fa'] && !$validation['fortify_confirmed']) {
        $validation['data_consistency'] = false;
        $validation['issue'] = 'Fortify 2FA exists but not confirmed';
    }
    
    return $validation;
}

public function migrateTo2FA(): bool
{
    if (is_null($this->app_authentication_secret)) {
        return false; // No Filament 2FA to migrate
    }
    
    if (!is_null($this->two_factor_secret)) {
        return true; // Already migrated
    }
    
    try {
        $this->update([
            'two_factor_secret' => $this->app_authentication_secret,
            'two_factor_recovery_codes' => $this->app_authentication_recovery_codes,
            'two_factor_confirmed_at' => now(),
        ]);
        
        return true;
    } catch (Exception $e) {
        Log::error('Failed to migrate user 2FA data', [
            'user_id' => $this->id,
            'error' => $e->getMessage()
        ]);
        
        return false;
    }
}
```

## 2.5.5 Rollback Strategy and Safety Measures

### ******* Comprehensive Rollback Procedures

**Rollback Command**: `app/Console/Commands/RollbackFortifyMigration.php`

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RollbackFortifyMigration extends Command
{
    protected $signature = 'fortify:rollback-migration {--force : Force rollback without confirmation}';
    protected $description = 'Safely rollback Fortify 2FA migration to Filament system';

    public function handle(): int
    {
        if (!$this->option('force')) {
            if (!$this->confirm('This will rollback Fortify 2FA migration. Are you sure?')) {
                $this->info('Rollback cancelled.');
                return 0;
            }
        }
        
        $this->info('🔄 Starting Fortify migration rollback...');
        
        // Step 1: Validate rollback safety
        if (!$this->validateRollbackSafety()) {
            return 1;
        }
        
        // Step 2: Restore Filament data from Fortify fields
        $this->restoreFilamentData();
        
        // Step 3: Clear Fortify data
        $this->clearFortifyData();
        
        // Step 4: Run database rollback
        $this->call('migrate:rollback', ['--step' => 3]);
        
        $this->info('✅ Fortify migration rollback completed successfully!');
        return 0;
    }
    
    private function validateRollbackSafety(): bool
    {
        // Check if any users are actively using Fortify 2FA
        $activeUsers = DB::table('users')
            ->whereNotNull('two_factor_confirmed_at')
            ->where('two_factor_confirmed_at', '>', now()->subDays(30))
            ->count();
        
        if ($activeUsers > 0) {
            $this->error("❌ Cannot rollback: {$activeUsers} users have recently used Fortify 2FA");
            return false;
        }
        
        return true;
    }
    
    private function restoreFilamentData(): void
    {
        $restoredCount = 0;
        
        DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->chunk(100, function ($users) use (&$restoredCount) {
                foreach ($users as $user) {
                    DB::table('users')
                        ->where('id', $user->id)
                        ->update([
                            'app_authentication_secret' => $user->two_factor_secret,
                            'app_authentication_recovery_codes' => $user->two_factor_recovery_codes,
                            'has_email_authentication' => !is_null($user->two_factor_confirmed_at),
                        ]);
                    
                    $restoredCount++;
                }
            });
        
        $this->line("📊 Restored Filament 2FA data for {$restoredCount} users");
    }
    
    private function clearFortifyData(): void
    {
        DB::table('users')->update([
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
            'two_factor_confirmed_at' => null,
        ]);
        
        $this->line("🧹 Cleared Fortify 2FA data");
    }
}
```

---

**Navigation Footer**

← [Previous: Gap Analysis & Migration Planning](020-gap-analysis-migration-planning.md) | [Next: Implementation Guide →](030-implementation-guide-unified.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/025-database-migration-strategy.md`
- **Document ID**: LF-2FA-003-UNIFIED
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
