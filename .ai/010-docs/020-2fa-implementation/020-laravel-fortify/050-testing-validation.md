# 5.0 Laravel Fortify 2FA Implementation - Testing & Validation

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 25 minutes

## 5.1 Executive Summary

This document provides comprehensive testing and validation procedures for the Laravel Fortify 2FA implementation, ensuring dual 2FA system integrity, security compliance, and seamless user experience. All tests are designed to validate both Filament and Fortify 2FA systems operating independently.

### 5.1.1 Testing Strategy Overview

| Test Category | Coverage Target | Focus Areas | Priority |
|---------------|----------------|-------------|----------|
| **Unit Tests** | 95% | 2FA setup, verification, recovery | 🔴 Critical |
| **Feature Tests** | 90% | Authentication flows, middleware | 🔴 Critical |
| **Integration Tests** | 85% | Dual 2FA system interaction | 🟡 High |
| **Browser Tests** | 80% | UI components, user workflows | 🟡 High |
| **Security Tests** | 100% | Encryption, rate limiting, CSRF | 🔴 Critical |

## 5.2 Unit Testing Implementation

### 5.2.1 Two-Factor Authentication Unit Tests

**File**: `tests/Unit/TwoFactorAuthenticationTest.php`

```php
<?php

namespace Tests\Unit;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use Laravel\Fortify\Actions\DisableTwoFactorAuthentication;
use Laravel\Fortify\Actions\GenerateNewRecoveryCodes;
use PragmaRX\Google2FA\Google2FA;
use Tests\TestCase;

class TwoFactorAuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_enable_two_factor_authentication(): void
    {
        $user = User::factory()->create();

        $this->assertNull($user->two_factor_secret);
        $this->assertNull($user->two_factor_recovery_codes);
        $this->assertNull($user->two_factor_confirmed_at);

        app(EnableTwoFactorAuthentication::class)($user);

        $user->refresh();

        $this->assertNotNull($user->two_factor_secret);
        $this->assertNotNull($user->two_factor_recovery_codes);
        $this->assertNull($user->two_factor_confirmed_at); // Not confirmed yet
    }

    public function test_user_can_confirm_two_factor_authentication(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $google2fa = app(Google2FA::class);
        $secret = decrypt($user->two_factor_secret);
        $validCode = $google2fa->getCurrentOtp($secret);

        $this->assertTrue($user->confirmTwoFactorAuth($validCode));
        
        $user->refresh();
        $this->assertNotNull($user->two_factor_confirmed_at);
    }

    public function test_user_cannot_confirm_with_invalid_code(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $this->assertFalse($user->confirmTwoFactorAuth('invalid-code'));
        $this->assertFalse($user->confirmTwoFactorAuth('123456'));
        $this->assertFalse($user->confirmTwoFactorAuth(''));
        
        $user->refresh();
        $this->assertNull($user->two_factor_confirmed_at);
    }

    public function test_user_can_disable_two_factor_authentication(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);
        
        // Confirm 2FA first
        $google2fa = app(Google2FA::class);
        $secret = decrypt($user->two_factor_secret);
        $validCode = $google2fa->getCurrentOtp($secret);
        $user->confirmTwoFactorAuth($validCode);

        $this->assertTrue($user->hasEnabledTwoFactorAuthentication());

        app(DisableTwoFactorAuthentication::class)($user);

        $user->refresh();
        $this->assertNull($user->two_factor_secret);
        $this->assertNull($user->two_factor_recovery_codes);
        $this->assertNull($user->two_factor_confirmed_at);
        $this->assertFalse($user->hasEnabledTwoFactorAuthentication());
    }

    public function test_user_can_regenerate_recovery_codes(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $originalCodes = $user->two_factor_recovery_codes;

        app(GenerateNewRecoveryCodes::class)($user);

        $user->refresh();
        $this->assertNotEquals($originalCodes, $user->two_factor_recovery_codes);
        $this->assertNotNull($user->two_factor_recovery_codes);
    }

    public function test_recovery_codes_are_properly_formatted(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $recoveryCodes = json_decode(decrypt($user->two_factor_recovery_codes), true);

        $this->assertIsArray($recoveryCodes);
        $this->assertCount(8, $recoveryCodes); // Default count from config
        
        foreach ($recoveryCodes as $code) {
            $this->assertIsString($code);
            $this->assertMatchesRegularExpression('/^[a-z0-9]{5}-[a-z0-9]{5}$/', $code);
        }
    }

    public function test_filament_and_fortify_2fa_coexist(): void
    {
        $user = User::factory()->create([
            'app_authentication_secret' => encrypt('filament-secret'),
            'app_authentication_recovery_codes' => encrypt(['filament-code1', 'filament-code2']),
            'has_email_authentication' => true,
        ]);

        // Enable Fortify 2FA
        app(EnableTwoFactorAuthentication::class)($user);

        $user->refresh();

        // Verify both systems have separate data
        $this->assertNotNull($user->app_authentication_secret);
        $this->assertNotNull($user->app_authentication_recovery_codes);
        $this->assertTrue($user->has_email_authentication);
        
        $this->assertNotNull($user->two_factor_secret);
        $this->assertNotNull($user->two_factor_recovery_codes);
        
        // Verify secrets are different
        $this->assertNotEquals(
            decrypt($user->app_authentication_secret),
            decrypt($user->two_factor_secret)
        );
    }
}
```

### 5.2.2 User Model Enhancement Tests

**File**: `tests/Unit/UserModelTest.php`

```php
<?php

namespace Tests\Unit;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use Tests\TestCase;

class UserModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_implements_required_interfaces(): void
    {
        $user = new User();

        $this->assertInstanceOf(\Filament\Models\Contracts\FilamentUser::class, $user);
        $this->assertInstanceOf(\Laravel\Fortify\TwoFactorAuthenticatable::class, $user);
        $this->assertInstanceOf(\Illuminate\Contracts\Auth\MustVerifyEmail::class, $user);
    }

    public function test_user_has_required_fillable_fields(): void
    {
        $fillable = (new User())->getFillable();

        $requiredFields = ['name', 'email', 'password', 'slug', 'public_id', 'has_email_authentication'];
        
        foreach ($requiredFields as $field) {
            $this->assertContains($field, $fillable);
        }
    }

    public function test_user_has_required_hidden_fields(): void
    {
        $hidden = (new User())->getHidden();

        $requiredHidden = [
            'password',
            'remember_token',
            'app_authentication_secret',
            'app_authentication_recovery_codes',
            'two_factor_secret',
            'two_factor_recovery_codes',
        ];
        
        foreach ($requiredHidden as $field) {
            $this->assertContains($field, $hidden);
        }
    }

    public function test_user_has_enabled_two_factor_authentication_method(): void
    {
        $user = User::factory()->create();

        $this->assertFalse($user->hasEnabledTwoFactorAuthentication());

        app(EnableTwoFactorAuthentication::class)($user);
        $this->assertFalse($user->hasEnabledTwoFactorAuthentication()); // Not confirmed yet

        // Simulate confirmation
        $user->forceFill(['two_factor_confirmed_at' => now()])->save();
        $this->assertTrue($user->hasEnabledTwoFactorAuthentication());
    }

    public function test_user_generates_qr_code_svg(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $qrCodeSvg = $user->twoFactorQrCodeSvg();

        $this->assertIsString($qrCodeSvg);
        $this->assertStringContainsString('<svg', $qrCodeSvg);
        $this->assertStringContainsString($user->email, $qrCodeSvg);
    }
}
```

## 5.3 Feature Testing Implementation

### 5.3.1 Authentication Flow Tests

**File**: `tests/Feature/TwoFactorAuthenticationFlowTest.php`

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use PragmaRX\Google2FA\Google2FA;
use Tests\TestCase;

class TwoFactorAuthenticationFlowTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_access_two_factor_setup_page(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->get('/user/two-factor-authentication');

        $response->assertStatus(200);
        $response->assertSee('Two-Factor Authentication');
    }

    public function test_user_can_enable_two_factor_authentication_via_http(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->post('/user/two-factor-authentication');

        $response->assertRedirect();
        
        $user->refresh();
        $this->assertNotNull($user->two_factor_secret);
        $this->assertNotNull($user->two_factor_recovery_codes);
    }

    public function test_user_can_confirm_two_factor_authentication_via_http(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $google2fa = app(Google2FA::class);
        $secret = decrypt($user->two_factor_secret);
        $validCode = $google2fa->getCurrentOtp($secret);

        $response = $this->actingAs($user)
            ->post('/user/confirmed-two-factor-authentication', [
                'code' => $validCode,
            ]);

        $response->assertRedirect();
        
        $user->refresh();
        $this->assertNotNull($user->two_factor_confirmed_at);
    }

    public function test_user_cannot_confirm_with_invalid_code_via_http(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $response = $this->actingAs($user)
            ->post('/user/confirmed-two-factor-authentication', [
                'code' => 'invalid',
            ]);

        $response->assertSessionHasErrors(['code']);
        
        $user->refresh();
        $this->assertNull($user->two_factor_confirmed_at);
    }

    public function test_user_can_disable_two_factor_authentication_via_http(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);
        
        // Confirm 2FA
        $google2fa = app(Google2FA::class);
        $secret = decrypt($user->two_factor_secret);
        $validCode = $google2fa->getCurrentOtp($secret);
        $user->confirmTwoFactorAuth($validCode);

        $response = $this->actingAs($user)
            ->delete('/user/two-factor-authentication');

        $response->assertRedirect();
        
        $user->refresh();
        $this->assertNull($user->two_factor_secret);
        $this->assertNull($user->two_factor_recovery_codes);
        $this->assertNull($user->two_factor_confirmed_at);
    }

    public function test_user_can_view_qr_code(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $response = $this->actingAs($user)
            ->get('/user/two-factor-qr-code');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'image/svg+xml');
    }

    public function test_user_can_view_recovery_codes(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $response = $this->actingAs($user)
            ->get('/user/two-factor-recovery-codes');

        $response->assertStatus(200);
        $response->assertJsonStructure(['recovery_codes']);
    }

    public function test_user_can_regenerate_recovery_codes(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);

        $originalCodes = $user->two_factor_recovery_codes;

        $response = $this->actingAs($user)
            ->post('/user/two-factor-recovery-codes');

        $response->assertStatus(200);
        
        $user->refresh();
        $this->assertNotEquals($originalCodes, $user->two_factor_recovery_codes);
    }
}
```

### 5.3.2 Middleware Testing

**File**: `tests/Feature/TwoFactorMiddlewareTest.php`

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use PragmaRX\Google2FA\Google2FA;
use Tests\TestCase;

class TwoFactorMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    public function test_middleware_allows_access_to_admin_routes_without_fortify_2fa(): void
    {
        $user = User::factory()->create();

        // User has no Fortify 2FA enabled
        $this->assertFalse($user->hasEnabledTwoFactorAuthentication());

        $response = $this->actingAs($user)
            ->get('/admin');

        // Should be allowed (Filament handles its own 2FA)
        $response->assertStatus(200);
    }

    public function test_middleware_redirects_to_setup_for_protected_routes_without_2fa(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->get('/dashboard');

        $response->assertRedirect('/profile');
        $response->assertSessionHas('status', 'two-factor-authentication-required');
    }

    public function test_middleware_allows_access_to_protected_routes_with_2fa(): void
    {
        $user = User::factory()->create();
        app(EnableTwoFactorAuthentication::class)($user);
        
        // Confirm 2FA
        $google2fa = app(Google2FA::class);
        $secret = decrypt($user->two_factor_secret);
        $validCode = $google2fa->getCurrentOtp($secret);
        $user->confirmTwoFactorAuth($validCode);

        $response = $this->actingAs($user)
            ->get('/dashboard');

        $response->assertStatus(200);
    }

    public function test_middleware_allows_access_to_2fa_setup_routes(): void
    {
        $user = User::factory()->create();

        $setupRoutes = [
            '/user/two-factor-authentication',
            '/profile',
        ];

        foreach ($setupRoutes as $route) {
            $response = $this->actingAs($user)->get($route);
            $response->assertStatus(200);
        }
    }

    public function test_middleware_skips_public_routes(): void
    {
        $publicRoutes = [
            '/',
            '/login',
            '/register',
        ];

        foreach ($publicRoutes as $route) {
            $response = $this->get($route);
            $response->assertStatus(200);
        }
    }
}
```

## 5.4 Integration Testing

### 5.4.1 Dual 2FA System Integration Tests

**File**: `tests/Feature/DualTwoFactorSystemTest.php`

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use Tests\TestCase;

class DualTwoFactorSystemTest extends TestCase
{
    use RefreshDatabase;

    public function test_filament_and_fortify_2fa_operate_independently(): void
    {
        $user = User::factory()->create([
            'app_authentication_secret' => encrypt('filament-secret'),
            'app_authentication_recovery_codes' => encrypt(['filament-code1', 'filament-code2']),
            'has_email_authentication' => true,
        ]);

        // Enable Fortify 2FA
        app(EnableTwoFactorAuthentication::class)($user);

        $user->refresh();

        // Verify both systems maintain separate data
        $this->assertNotNull($user->app_authentication_secret);
        $this->assertNotNull($user->two_factor_secret);
        
        // Verify they use different secrets
        $filamentSecret = decrypt($user->app_authentication_secret);
        $fortifySecret = decrypt($user->two_factor_secret);
        $this->assertNotEquals($filamentSecret, $fortifySecret);

        // Verify Filament 2FA status is independent
        $this->assertTrue($user->has_email_authentication);
        
        // Verify Fortify 2FA status is independent
        $this->assertFalse($user->hasEnabledTwoFactorAuthentication()); // Not confirmed yet
    }

    public function test_disabling_one_system_does_not_affect_the_other(): void
    {
        $user = User::factory()->create([
            'app_authentication_secret' => encrypt('filament-secret'),
            'app_authentication_recovery_codes' => encrypt(['filament-code1', 'filament-code2']),
            'has_email_authentication' => true,
        ]);

        // Enable and confirm Fortify 2FA
        app(EnableTwoFactorAuthentication::class)($user);
        $user->forceFill(['two_factor_confirmed_at' => now()])->save();

        $this->assertTrue($user->hasEnabledTwoFactorAuthentication());

        // Disable Fortify 2FA
        $response = $this->actingAs($user)
            ->delete('/user/two-factor-authentication');

        $response->assertRedirect();
        
        $user->refresh();

        // Verify Fortify 2FA is disabled
        $this->assertNull($user->two_factor_secret);
        $this->assertFalse($user->hasEnabledTwoFactorAuthentication());

        // Verify Filament 2FA is still enabled
        $this->assertNotNull($user->app_authentication_secret);
        $this->assertTrue($user->has_email_authentication);
    }

    public function test_user_can_have_different_2fa_states_per_context(): void
    {
        $user = User::factory()->create();

        // Enable only Filament 2FA
        $user->forceFill([
            'app_authentication_secret' => encrypt('filament-secret'),
            'has_email_authentication' => true,
        ])->save();

        // User should be able to access admin (Filament handles 2FA)
        $response = $this->actingAs($user)->get('/admin');
        $response->assertStatus(200);

        // User should be redirected for web app (no Fortify 2FA)
        $response = $this->actingAs($user)->get('/dashboard');
        $response->assertRedirect('/profile');

        // Now enable Fortify 2FA
        app(EnableTwoFactorAuthentication::class)($user);
        $user->forceFill(['two_factor_confirmed_at' => now()])->save();

        // User should now be able to access both contexts
        $response = $this->actingAs($user)->get('/admin');
        $response->assertStatus(200);

        $response = $this->actingAs($user)->get('/dashboard');
        $response->assertStatus(200);
    }
}
```

## 5.5 Database Migration Testing

### 5.5.1 Migration Integrity Tests

**File**: `tests/Feature/Database/FortifyMigrationTest.php`

```php
<?php

namespace Tests\Feature\Database;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class FortifyMigrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_migration_adds_required_columns(): void
    {
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_secret'));
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_recovery_codes'));
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_confirmed_at'));
    }

    public function test_migration_preserves_existing_filament_columns(): void
    {
        $this->assertTrue(Schema::hasColumn('users', 'app_authentication_secret'));
        $this->assertTrue(Schema::hasColumn('users', 'app_authentication_recovery_codes'));
        $this->assertTrue(Schema::hasColumn('users', 'has_email_authentication'));
    }

    public function test_existing_filament_data_is_preserved(): void
    {
        // Create user with Filament 2FA data
        $user = User::factory()->create([
            'app_authentication_secret' => encrypt('test-secret'),
            'app_authentication_recovery_codes' => encrypt(['code1', 'code2']),
            'has_email_authentication' => true,
        ]);

        // Verify data is preserved
        $user->refresh();
        $this->assertEquals('test-secret', decrypt($user->app_authentication_secret));
        $this->assertEquals(['code1', 'code2'], decrypt($user->app_authentication_recovery_codes));
        $this->assertTrue($user->has_email_authentication);
    }

    public function test_rollback_protection_with_active_fortify_users(): void
    {
        $user = User::factory()->create([
            'two_factor_secret' => 'test-secret',
            'two_factor_confirmed_at' => now()
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Cannot rollback');

        Artisan::call('migrate:rollback', ['--step' => 2]);
    }

    public function test_migration_validation_command(): void
    {
        Artisan::call('fortify:validate-migration');
        
        $output = Artisan::output();
        $this->assertStringContainsString('All validation checks passed', $output);
    }
}
```

---

**Navigation Footer**

← [Previous: Livewire/Volt Components](040-livewire-volt-components.md) | [Next: Troubleshooting Guide →](060-troubleshooting-guide.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/050-testing-validation.md`
- **Document ID**: LF-2FA-007
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
