# 2.5 Laravel Fortify 2FA Implementation - Database Migration Planning

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 18 minutes

## 2.5.1 Executive Summary

This document provides comprehensive database migration planning for Laravel Fortify 2FA implementation, ensuring seamless integration with existing Filament 2FA fields while maintaining data integrity and providing robust rollback procedures. The strategy focuses on additive changes that preserve all existing functionality.

### ******* Migration Strategy Overview

| Migration Phase | Description | Risk Level | Rollback Complexity | Estimated Time |
|----------------|-------------|------------|-------------------|----------------|
| **Phase 1** | Add Fortify 2FA columns | 🟢 Low | Simple | 2 minutes |
| **Phase 2** | Create performance indexes | 🟢 Low | Simple | 1 minute |
| **Phase 3** | Validation and testing | 🟡 Medium | N/A | 5 minutes |

## 2.5.2 Current Database Schema Analysis

### ******* Existing User Table Structure

**Current 2FA-Related Fields** (verified from User model analysis):

```sql
-- Existing Filament 2FA fields in users table
id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
name VARCHAR(255) NOT NULL,
email VARCHAR(255) UNIQUE NOT NULL,
email_verified_at TIMESTAMP NULL,
password VARCHAR(255) NOT NULL,
remember_token VARCHAR(100) NULL,
slug VARCHAR(255) UNIQUE NULL,
public_id VARCHAR(26) UNIQUE NOT NULL,
created_at TIMESTAMP NULL,
updated_at TIMESTAMP NULL,
deleted_at TIMESTAMP NULL,

-- Filament 2FA fields (encrypted)
app_authentication_secret TEXT NULL,
app_authentication_recovery_codes TEXT NULL,
has_email_authentication BOOLEAN DEFAULT FALSE,

-- Audit fields (from Userstamps)
created_by BIGINT UNSIGNED NULL,
updated_by BIGINT UNSIGNED NULL,
deleted_by BIGINT UNSIGNED NULL
```

**Current Constraints and Indexes**:

```sql
-- Existing indexes (standard Laravel user table)
UNIQUE KEY users_email_unique (email),
UNIQUE KEY users_slug_unique (slug),
UNIQUE KEY users_public_id_unique (public_id),
KEY users_created_by_foreign (created_by),
KEY users_updated_by_foreign (updated_by),
KEY users_deleted_by_foreign (deleted_by)
```

### 2.5.2.2 Field Usage Analysis

**Current Field Utilization**:

| Field | Data Type | Encryption | Usage Context | Conflict Risk |
|-------|-----------|------------|---------------|---------------|
| `app_authentication_secret` | TEXT | ✅ Encrypted | Filament admin only | ✅ None |
| `app_authentication_recovery_codes` | TEXT | ✅ Encrypted array | Filament admin only | ✅ None |
| `has_email_authentication` | BOOLEAN | ❌ Plain | Filament admin only | ✅ None |

## 2.5.3 Required Schema Additions

### 2.5.3.1 Fortify 2FA Fields Specification

**New Fields for Laravel Fortify**:

```sql
-- Required additions for Laravel Fortify 2FA
two_factor_secret TEXT NULL COMMENT 'Encrypted TOTP secret for Laravel Fortify 2FA',
two_factor_recovery_codes TEXT NULL COMMENT 'Encrypted recovery codes for Laravel Fortify 2FA',
two_factor_confirmed_at TIMESTAMP NULL COMMENT 'Timestamp when Fortify 2FA was confirmed and activated'
```

**Field Specifications**:

| Field | Type | Nullable | Default | Purpose |
|-------|------|----------|---------|---------|
| `two_factor_secret` | TEXT | ✅ Yes | NULL | Stores encrypted TOTP secret |
| `two_factor_recovery_codes` | TEXT | ✅ Yes | NULL | Stores encrypted recovery codes |
| `two_factor_confirmed_at` | TIMESTAMP | ✅ Yes | NULL | Tracks 2FA activation timestamp |

### 2.5.3.2 Migration Implementation

**Primary Migration File**:

```php
<?php
// database/migrations/2025_07_01_120000_add_fortify_two_factor_columns_to_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add Laravel Fortify 2FA columns
            $table->text('two_factor_secret')
                  ->nullable()
                  ->after('remember_token')
                  ->comment('Encrypted TOTP secret for Laravel Fortify 2FA');
                  
            $table->text('two_factor_recovery_codes')
                  ->nullable()
                  ->after('two_factor_secret')
                  ->comment('Encrypted recovery codes for Laravel Fortify 2FA');
                  
            $table->timestamp('two_factor_confirmed_at')
                  ->nullable()
                  ->after('two_factor_recovery_codes')
                  ->comment('Timestamp when Fortify 2FA was confirmed and activated');
        });
        
        // Log successful migration
        Log::info('Fortify 2FA columns added to users table', [
            'timestamp' => now(),
            'migration' => '2025_07_01_120000_add_fortify_two_factor_columns_to_users_table'
        ]);
    }

    /**
     * Reverse the migrations with safety checks.
     */
    public function down(): void
    {
        // Safety check: Ensure no users have Fortify 2FA enabled
        $usersWithFortify2FA = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->orWhereNotNull('two_factor_confirmed_at')
            ->count();
        
        if ($usersWithFortify2FA > 0) {
            throw new Exception(
                "Cannot rollback migration: {$usersWithFortify2FA} users have Fortify 2FA data. " .
                "Please disable 2FA for all users before rolling back."
            );
        }
        
        // Proceed with safe rollback
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'two_factor_confirmed_at',
                'two_factor_recovery_codes', 
                'two_factor_secret'
            ]);
        });
        
        // Log successful rollback
        Log::info('Fortify 2FA columns removed from users table', [
            'timestamp' => now(),
            'affected_users' => 0
        ]);
    }
};
```

### 2.5.3.3 Performance Index Migration

**Index Optimization Migration**:

```php
<?php
// database/migrations/2025_07_01_120001_add_two_factor_indexes_to_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Index for 2FA status queries (most common query)
            $table->index('two_factor_confirmed_at', 'users_2fa_confirmed_index');
            
            // Composite index for authentication queries
            $table->index(['email', 'two_factor_confirmed_at'], 'users_email_2fa_index');
            
            // Index for cleanup and maintenance operations
            $table->index(['two_factor_secret', 'two_factor_confirmed_at'], 'users_2fa_status_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes in reverse order for clean rollback
            $table->dropIndex('users_2fa_status_index');
            $table->dropIndex('users_email_2fa_index');
            $table->dropIndex('users_2fa_confirmed_index');
        });
    }
};
```

## 2.5.4 Rollback Strategy and Safety Measures

### ******* Pre-Migration Backup Strategy

**Database Backup Commands**:

```bash
# For SQLite database (current setup)
cp database/database.sqlite database/database.sqlite.backup.$(date +%Y%m%d_%H%M%S)

# Verify backup integrity
ls -la database/database.sqlite*

# Test backup restoration (optional)
# cp database/database.sqlite.backup.YYYYMMDD_HHMMSS database/database.sqlite.test
```

**Backup Validation Script**:

```php
<?php
// database/scripts/validate_backup.php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class BackupValidator
{
    public static function validateBackup(string $backupPath): array
    {
        $results = [
            'backup_exists' => file_exists($backupPath),
            'backup_size' => file_exists($backupPath) ? filesize($backupPath) : 0,
            'original_size' => filesize(database_path('database.sqlite')),
        ];
        
        $results['size_match'] = abs($results['backup_size'] - $results['original_size']) < 1024; // Allow 1KB difference
        
        return $results;
    }
}
```

### ******* Resilient Rollback Implementation

**Enhanced Rollback Validation**:

```php
// Enhanced rollback method with comprehensive safety checks
public function down(): void
{
    // Step 1: Check for active Fortify 2FA users
    $fortifyUsers = DB::table('users')
        ->whereNotNull('two_factor_secret')
        ->orWhereNotNull('two_factor_confirmed_at')
        ->select('id', 'email', 'two_factor_confirmed_at')
        ->get();
    
    if ($fortifyUsers->count() > 0) {
        $userEmails = $fortifyUsers->pluck('email')->take(5)->implode(', ');
        throw new Exception(
            "Cannot rollback: {$fortifyUsers->count()} users have Fortify 2FA enabled. " .
            "Example users: {$userEmails}. " .
            "Please disable 2FA for all users before rolling back."
        );
    }
    
    // Step 2: Verify Filament 2FA data integrity
    $filamentUsers = DB::table('users')
        ->whereNotNull('app_authentication_secret')
        ->count();
    
    Log::info('Rollback validation passed', [
        'fortify_users' => 0,
        'filament_users' => $filamentUsers,
        'timestamp' => now()
    ]);
    
    // Step 3: Proceed with safe rollback
    Schema::table('users', function (Blueprint $table) {
        // Drop columns in reverse order of creation
        $table->dropColumn([
            'two_factor_confirmed_at',
            'two_factor_recovery_codes', 
            'two_factor_secret'
        ]);
    });
    
    // Step 4: Verify rollback success
    $remainingColumns = Schema::getColumnListing('users');
    $fortifyColumns = ['two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at'];
    $remainingFortifyColumns = array_intersect($remainingColumns, $fortifyColumns);
    
    if (!empty($remainingFortifyColumns)) {
        throw new Exception(
            "Rollback incomplete: Columns still exist: " . implode(', ', $remainingFortifyColumns)
        );
    }
    
    Log::info('Fortify 2FA migration rolled back successfully', [
        'timestamp' => now(),
        'preserved_filament_users' => $filamentUsers
    ]);
}
```

## 2.5.5 Migration Validation and Testing

### ******* Post-Migration Validation Command

**Artisan Command for Validation**:

```php
<?php
// app/Console/Commands/ValidateFortifyMigration.php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class ValidateFortifyMigration extends Command
{
    protected $signature = 'fortify:validate-migration';
    protected $description = 'Validate Fortify 2FA migration integrity and performance';

    public function handle(): int
    {
        $this->info('🔍 Validating Fortify 2FA migration...');
        
        $validationResults = [
            'schema' => $this->validateSchema(),
            'indexes' => $this->validateIndexes(),
            'data_integrity' => $this->validateDataIntegrity(),
            'performance' => $this->validatePerformance(),
        ];
        
        $allPassed = collect($validationResults)->every(fn($result) => $result);
        
        if ($allPassed) {
            $this->info('✅ All validation checks passed successfully!');
            return 0;
        } else {
            $this->error('❌ Some validation checks failed. Please review the output above.');
            return 1;
        }
    }
    
    private function validateSchema(): bool
    {
        $this->line('📋 Checking schema...');
        
        $requiredColumns = ['two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at'];
        $existingColumns = Schema::getColumnListing('users');
        
        foreach ($requiredColumns as $column) {
            if (!in_array($column, $existingColumns)) {
                $this->error("  ❌ Missing column: {$column}");
                return false;
            }
            $this->line("  ✅ Column exists: {$column}");
        }
        
        return true;
    }
    
    private function validateIndexes(): bool
    {
        $this->line('🔍 Checking indexes...');
        
        // For SQLite, check indexes differently
        $indexes = DB::select("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='users'");
        $indexNames = collect($indexes)->pluck('name')->toArray();
        
        $requiredIndexes = [
            'users_2fa_confirmed_index',
            'users_email_2fa_index',
            'users_2fa_status_index'
        ];
        
        foreach ($requiredIndexes as $index) {
            if (!in_array($index, $indexNames)) {
                $this->warn("  ⚠️  Missing index: {$index}");
            } else {
                $this->line("  ✅ Index exists: {$index}");
            }
        }
        
        return true;
    }
    
    private function validateDataIntegrity(): bool
    {
        $this->line('🔒 Checking data integrity...');
        
        // Check for orphaned 2FA data
        $orphanedSecrets = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->whereNull('two_factor_confirmed_at')
            ->count();
            
        if ($orphanedSecrets > 0) {
            $this->warn("  ⚠️  Found {$orphanedSecrets} users with 2FA secrets but no confirmation timestamp");
        } else {
            $this->line("  ✅ No orphaned 2FA secrets found");
        }
        
        // Check for confirmed 2FA without secrets
        $confirmedWithoutSecrets = DB::table('users')
            ->whereNotNull('two_factor_confirmed_at')
            ->whereNull('two_factor_secret')
            ->count();
            
        if ($confirmedWithoutSecrets > 0) {
            $this->error("  ❌ Found {$confirmedWithoutSecrets} users with 2FA confirmation but no secret");
            return false;
        } else {
            $this->line("  ✅ No confirmed 2FA without secrets found");
        }
        
        // Verify Filament 2FA data is preserved
        $filamentUsers = DB::table('users')
            ->whereNotNull('app_authentication_secret')
            ->count();
            
        $this->line("  ✅ Filament 2FA data preserved for {$filamentUsers} users");
        
        return true;
    }
    
    private function validatePerformance(): bool
    {
        $this->line('⚡ Checking performance...');
        
        $start = microtime(true);
        
        // Test common 2FA queries
        DB::table('users')->where('two_factor_confirmed_at', '!=', null)->count();
        DB::table('users')->where('email', '<EMAIL>')->where('two_factor_confirmed_at', '!=', null)->first();
        
        $duration = (microtime(true) - $start) * 1000; // Convert to milliseconds
        
        if ($duration > 100) {
            $this->warn("  ⚠️  Query performance: {$duration}ms (consider index optimization)");
        } else {
            $this->line("  ✅ Query performance: {$duration}ms");
        }
        
        return true;
    }
}
```

### 2.5.5.2 Migration Testing Strategy

**Test Scenarios**:

```php
// tests/Feature/Database/FortifyMigrationTest.php
class FortifyMigrationTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_migration_adds_required_columns(): void
    {
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_secret'));
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_recovery_codes'));
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_confirmed_at'));
    }
    
    public function test_existing_filament_data_preserved(): void
    {
        // Create user with Filament 2FA
        $user = User::factory()->create([
            'app_authentication_secret' => encrypt('test-secret'),
            'app_authentication_recovery_codes' => encrypt(['code1', 'code2']),
            'has_email_authentication' => true,
        ]);
        
        // Verify data is preserved after migration
        $user->refresh();
        $this->assertNotNull($user->app_authentication_secret);
        $this->assertNotNull($user->app_authentication_recovery_codes);
        $this->assertTrue($user->has_email_authentication);
    }
    
    public function test_rollback_protection_with_active_users(): void
    {
        $user = User::factory()->create([
            'two_factor_secret' => 'test-secret',
            'two_factor_confirmed_at' => now()
        ]);
        
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Cannot rollback');
        
        Artisan::call('migrate:rollback', ['--step' => 2]);
    }
}
```

---

**Navigation Footer**

← [Previous: Gap Analysis & Requirements](020-gap-analysis-requirements.md) | [Next: Service Provider Architecture →](030-service-provider-architecture.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/025-database-migration-planning.md`
- **Document ID**: LF-2FA-003
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
