# 4.0 Laravel Fortify 2FA Implementation - Livewire/Volt Components

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 28 minutes

## 4.1 Executive Summary

This document provides comprehensive implementation of Livewire/Volt functional components for Laravel Fortify 2FA, creating a modern, SPA-like user experience with WCAG AA accessibility compliance. All components use Volt's functional API for streamlined development and maintenance.

### 4.1.1 Component Architecture Overview

| Component | Purpose | Complexity | WCAG AA Compliance |
|-----------|---------|------------|-------------------|
| **two-factor-setup** | Main 2FA setup interface | 🟡 Medium | ✅ Full compliance |
| **two-factor-challenge** | Login verification | 🟢 Simple | ✅ Full compliance |
| **recovery-codes** | Recovery code management | 🟡 Medium | ✅ Full compliance |
| **two-factor-status** | Current status display | 🟢 Simple | ✅ Full compliance |

## 4.2 Two-Factor Setup Component

### 4.2.1 Main Setup Component

**File**: `resources/views/livewire/two-factor-setup.blade.php`

```php
<?php

use function Livewire\Volt\{state, mount, computed, on};
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use Laravel\Fortify\Actions\GenerateNewRecoveryCodes;
use Laravel\Fortify\Actions\DisableTwoFactorAuthentication;
use Illuminate\Support\Facades\Auth;

// Component state
state([
    'showingQrCode' => false,
    'showingConfirmation' => false,
    'showingRecoveryCodes' => false,
    'code' => '',
    'isEnabled' => false,
    'confirmingDisable' => false,
]);

// Mount component with current 2FA status
mount(function () {
    $this->isEnabled = Auth::user()->hasEnabledTwoFactorAuthentication();
});

// Enable two-factor authentication
$enableTwoFactorAuthentication = function () {
    $this->resetErrorBag();
    
    try {
        app(EnableTwoFactorAuthentication::class)(Auth::user());
        
        $this->showingQrCode = true;
        $this->showingConfirmation = true;
        $this->showingRecoveryCodes = false;
        
        $this->dispatch('two-factor-enabled');
    } catch (Exception $e) {
        $this->addError('enable', 'Failed to enable two-factor authentication. Please try again.');
    }
};

// Confirm two-factor authentication
$confirmTwoFactorAuthentication = function () {
    $this->resetErrorBag();
    
    if (empty($this->code)) {
        $this->addError('code', 'Please enter the verification code from your authenticator app.');
        return;
    }
    
    $user = Auth::user();
    
    if ($user->confirmTwoFactorAuth($this->code)) {
        $this->isEnabled = true;
        $this->showingQrCode = false;
        $this->showingConfirmation = false;
        $this->showingRecoveryCodes = true;
        $this->code = '';
        
        session()->flash('status', 'Two-factor authentication has been enabled successfully.');
        $this->dispatch('two-factor-confirmed');
    } else {
        $this->addError('code', 'The provided two factor authentication code was invalid.');
    }
};

// Disable two-factor authentication
$disableTwoFactorAuthentication = function () {
    $this->resetErrorBag();
    
    try {
        app(DisableTwoFactorAuthentication::class)(Auth::user());
        
        $this->isEnabled = false;
        $this->showingQrCode = false;
        $this->showingConfirmation = false;
        $this->showingRecoveryCodes = false;
        $this->confirmingDisable = false;
        
        session()->flash('status', 'Two-factor authentication has been disabled.');
        $this->dispatch('two-factor-disabled');
    } catch (Exception $e) {
        $this->addError('disable', 'Failed to disable two-factor authentication. Please try again.');
    }
};

// Generate new recovery codes
$regenerateRecoveryCodes = function () {
    app(GenerateNewRecoveryCodes::class)(Auth::user());
    $this->showingRecoveryCodes = true;
    session()->flash('status', 'New recovery codes have been generated.');
};

// Computed properties
$qrCodeSvg = computed(function () {
    return Auth::user()->twoFactorQrCodeSvg();
});

$recoveryCodes = computed(function () {
    return json_decode(decrypt(Auth::user()->two_factor_recovery_codes), true);
});

?>

<div class="max-w-xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900" id="two-factor-heading">
                Two-Factor Authentication
            </h2>
            <p class="mt-1 text-sm text-gray-600">
                Add additional security to your account using two-factor authentication.
            </p>
        </div>

        <div class="px-6 py-6" role="main" aria-labelledby="two-factor-heading">
            @if (session('status'))
                <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md" role="alert">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">
                                {{ session('status') }}
                            </p>
                        </div>
                    </div>
                </div>
            @endif

            @if (!$isEnabled)
                <!-- Enable 2FA Section -->
                <div class="space-y-4">
                    <p class="text-sm text-gray-600">
                        When two-factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.
                    </p>

                    @if (!$showingQrCode)
                        <div>
                            <button 
                                type="button" 
                                wire:click="enableTwoFactorAuthentication"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                aria-describedby="enable-2fa-description"
                            >
                                Enable Two-Factor Authentication
                            </button>
                            <p id="enable-2fa-description" class="mt-2 text-xs text-gray-500">
                                Click to begin the setup process for two-factor authentication.
                            </p>
                        </div>
                    @endif

                    @error('enable')
                        <div class="text-sm text-red-600" role="alert">{{ $message }}</div>
                    @enderror
                </div>
            @endif

            @if ($showingQrCode)
                <!-- QR Code Section -->
                <div class="space-y-4">
                    <p class="text-sm text-gray-600">
                        Two-factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.
                    </p>

                    <div class="flex justify-center p-4 bg-gray-50 rounded-lg">
                        <div class="text-center">
                            <div class="inline-block p-4 bg-white rounded-lg shadow-sm">
                                {!! $this->qrCodeSvg !!}
                            </div>
                            <p class="mt-2 text-xs text-gray-500">
                                Scan this QR code with your authenticator app
                            </p>
                        </div>
                    </div>

                    @if ($showingConfirmation)
                        <!-- Confirmation Section -->
                        <div class="space-y-4 border-t pt-4">
                            <div>
                                <label for="confirmation-code" class="block text-sm font-medium text-gray-700">
                                    Confirmation Code
                                </label>
                                <div class="mt-1">
                                    <input 
                                        type="text" 
                                        id="confirmation-code"
                                        wire:model="code"
                                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        placeholder="Enter the 6-digit code from your app"
                                        maxlength="6"
                                        pattern="[0-9]{6}"
                                        autocomplete="one-time-code"
                                        aria-describedby="code-help"
                                    >
                                </div>
                                <p id="code-help" class="mt-2 text-xs text-gray-500">
                                    Enter the 6-digit code shown in your authenticator app to confirm setup.
                                </p>
                                @error('code')
                                    <p class="mt-2 text-sm text-red-600" role="alert">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex space-x-3">
                                <button 
                                    type="button" 
                                    wire:click="confirmTwoFactorAuthentication"
                                    class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                >
                                    Confirm & Enable
                                </button>
                                <button 
                                    type="button" 
                                    wire:click="$set('showingQrCode', false)"
                                    class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            @if ($isEnabled && !$showingQrCode)
                <!-- Enabled State Section -->
                <div class="space-y-4">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span class="text-sm font-medium text-green-800">
                            Two-factor authentication is enabled.
                        </span>
                    </div>

                    <p class="text-sm text-gray-600">
                        Your account is protected with two-factor authentication. Store your recovery codes in a secure location.
                    </p>

                    <div class="flex flex-wrap gap-3">
                        <button 
                            type="button" 
                            wire:click="$set('showingRecoveryCodes', true)"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Show Recovery Codes
                        </button>
                        <button 
                            type="button" 
                            wire:click="regenerateRecoveryCodes"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Regenerate Recovery Codes
                        </button>
                        <button 
                            type="button" 
                            wire:click="$set('confirmingDisable', true)"
                            class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                            Disable
                        </button>
                    </div>

                    @error('disable')
                        <div class="text-sm text-red-600" role="alert">{{ $message }}</div>
                    @enderror
                </div>
            @endif

            @if ($showingRecoveryCodes)
                <!-- Recovery Codes Section -->
                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Recovery Codes
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p class="mb-3">
                                    Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two-factor authentication device is lost.
                                </p>
                                <div class="grid grid-cols-2 gap-2 font-mono text-sm bg-white p-3 rounded border">
                                    @if($this->recoveryCodes)
                                        @foreach ($this->recoveryCodes as $code)
                                            <div class="text-center py-1">{{ $code }}</div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            @if ($confirmingDisable)
                <!-- Disable Confirmation Modal -->
                <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
                    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                <div class="sm:flex sm:items-start">
                                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                        </svg>
                                    </div>
                                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                            Disable Two-Factor Authentication
                                        </h3>
                                        <div class="mt-2">
                                            <p class="text-sm text-gray-500">
                                                Are you sure you want to disable two-factor authentication? This will make your account less secure.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                                <button 
                                    type="button" 
                                    wire:click="disableTwoFactorAuthentication"
                                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                                >
                                    Disable
                                </button>
                                <button 
                                    type="button" 
                                    wire:click="$set('confirmingDisable', false)"
                                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
```

---

**Navigation Footer**

← [Previous: Configuration Files](035-configuration-files.md) | [Next: Testing & Validation →](050-testing-validation.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/040-livewire-volt-components.md`
- **Document ID**: LF-2FA-006
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
