# 4.5 Laravel Fortify 2FA Implementation - Volt + Flux UI Components

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 30 minutes

## 4.5.1 Executive Summary

This document provides comprehensive implementation of Livewire/Volt functional components with Flux/Flux-Pro UI library for Laravel Fortify authentication. All components are designed with WCAG AA accessibility compliance, modern SPA-like experience, and seamless integration with the unified Fortify authentication system.

### 4.5.1.1 Component Architecture Overview

| Component | Purpose | Complexity | Flux Integration | WCAG AA |
|-----------|---------|------------|------------------|---------|
| **Login** | Fortify login interface | 🟢 Simple | ✅ Full | ✅ Compliant |
| **Two-Factor Challenge** | 2FA verification during login | 🟡 Medium | ✅ Full | ✅ Compliant |
| **Two-Factor Setup** | 2FA configuration interface | 🔴 Complex | ✅ Full | ✅ Compliant |
| **Recovery Codes** | Recovery code management | 🟡 Medium | ✅ Full | ✅ Compliant |
| **Register** | User registration | 🟢 Simple | ✅ Full | ✅ Compliant |

## 4.5.2 Login Component Implementation

### 4.5.2.1 Fortify Login with Flux UI

**File**: `resources/views/livewire/auth/login.blade.php`

```php
<?php

use function Livewire\Volt\{state, rules, mount};
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

state([
    'email' => '',
    'password' => '',
    'remember' => false,
]);

rules([
    'email' => 'required|email',
    'password' => 'required',
]);

$login = function () {
    $this->validate();
    
    if (!Auth::attempt($this->only(['email', 'password']), $this->remember)) {
        throw ValidationException::withMessages([
            'email' => __('auth.failed'),
        ]);
    }
    
    request()->session()->regenerate();
    
    return redirect()->intended('/dashboard');
};

?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <flux:heading size="xl" class="text-center text-gray-900">
                Sign in to your account
            </flux:heading>
            <flux:subheading class="mt-2 text-center text-gray-600">
                Access your dashboard and admin panel
            </flux:subheading>
        </div>
        
        <flux:card class="mt-8">
            <form wire:submit="login" class="space-y-6">
                <flux:field>
                    <flux:label for="email">Email address</flux:label>
                    <flux:input 
                        id="email" 
                        type="email" 
                        wire:model="email" 
                        required 
                        autocomplete="email"
                        placeholder="Enter your email address"
                        class="mt-1"
                    />
                    <flux:error name="email" />
                </flux:field>
                
                <flux:field>
                    <flux:label for="password">Password</flux:label>
                    <flux:input 
                        id="password" 
                        type="password" 
                        wire:model="password" 
                        required 
                        autocomplete="current-password"
                        placeholder="Enter your password"
                        class="mt-1"
                    />
                    <flux:error name="password" />
                </flux:field>
                
                <flux:field>
                    <flux:checkbox wire:model="remember" id="remember">
                        Remember me for 30 days
                    </flux:checkbox>
                </flux:field>
                
                <div>
                    <flux:button 
                        type="submit" 
                        variant="primary" 
                        size="lg"
                        class="w-full"
                        :loading="$wire.loading"
                    >
                        Sign in
                    </flux:button>
                </div>
            </form>
            
            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300" />
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">Need help?</span>
                    </div>
                </div>
                
                <div class="mt-6 grid grid-cols-2 gap-3">
                    <flux:link 
                        href="{{ route('password.request') }}" 
                        variant="subtle"
                        class="text-center"
                    >
                        Forgot password?
                    </flux:link>
                    <flux:link 
                        href="{{ route('register') }}" 
                        variant="subtle"
                        class="text-center"
                    >
                        Create account
                    </flux:link>
                </div>
            </div>
        </flux:card>
    </div>
</div>
```

## 4.5.3 Two-Factor Challenge Component

### 4.5.3.1 2FA Verification During Login

**File**: `resources/views/livewire/auth/two-factor-challenge.blade.php`

```php
<?php

use function Livewire\Volt\{state, rules, mount};
use Illuminate\Support\Facades\Auth;
use Laravel\Fortify\Actions\RedirectIfTwoFactorAuthenticatable;

state([
    'code' => '',
    'recovery_code' => '',
    'recovery' => false,
]);

rules([
    'code' => 'nullable|string|size:6',
    'recovery_code' => 'nullable|string',
]);

$toggleRecovery = function () {
    $this->recovery = !$this->recovery;
    $this->reset(['code', 'recovery_code']);
};

$authenticate = function () {
    $this->validate();
    
    $user = Auth::user();
    
    if ($this->recovery) {
        // Verify recovery code
        if ($user->recoveryCodes() && in_array($this->recovery_code, $user->recoveryCodes())) {
            $user->replaceRecoveryCode($this->recovery_code);
            return redirect()->intended('/dashboard');
        }
        
        $this->addError('recovery_code', 'The provided recovery code was invalid.');
    } else {
        // Verify TOTP code
        $google2fa = app(\PragmaRX\Google2FA\Google2FA::class);
        
        if ($google2fa->verifyKey(decrypt($user->two_factor_secret), $this->code)) {
            return redirect()->intended('/dashboard');
        }
        
        $this->addError('code', 'The provided two factor authentication code was invalid.');
    }
};

?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <flux:heading size="xl" class="text-center text-gray-900">
                Two-Factor Authentication
            </flux:heading>
            <flux:subheading class="mt-2 text-center text-gray-600">
                @if ($recovery)
                    Enter one of your recovery codes to continue
                @else
                    Enter the code from your authenticator app
                @endif
            </flux:subheading>
        </div>
        
        <flux:card class="mt-8">
            <form wire:submit="authenticate" class="space-y-6">
                @if (!$recovery)
                    <flux:field>
                        <flux:label for="code">Authentication Code</flux:label>
                        <flux:input 
                            id="code" 
                            type="text" 
                            wire:model="code" 
                            required 
                            maxlength="6"
                            pattern="[0-9]{6}"
                            autocomplete="one-time-code"
                            placeholder="000000"
                            class="mt-1 text-center text-2xl tracking-widest"
                            autofocus
                        />
                        <flux:description>
                            Enter the 6-digit code from your authenticator app
                        </flux:description>
                        <flux:error name="code" />
                    </flux:field>
                @else
                    <flux:field>
                        <flux:label for="recovery_code">Recovery Code</flux:label>
                        <flux:input 
                            id="recovery_code" 
                            type="text" 
                            wire:model="recovery_code" 
                            required 
                            autocomplete="one-time-code"
                            placeholder="xxxxx-xxxxx"
                            class="mt-1 text-center font-mono"
                            autofocus
                        />
                        <flux:description>
                            Enter one of your recovery codes
                        </flux:description>
                        <flux:error name="recovery_code" />
                    </flux:field>
                @endif
                
                <div>
                    <flux:button 
                        type="submit" 
                        variant="primary" 
                        size="lg"
                        class="w-full"
                        :loading="$wire.loading"
                    >
                        @if ($recovery)
                            Verify Recovery Code
                        @else
                            Verify Code
                        @endif
                    </flux:button>
                </div>
            </form>
            
            <div class="mt-6 text-center">
                <flux:button 
                    wire:click="toggleRecovery" 
                    variant="ghost" 
                    size="sm"
                >
                    @if ($recovery)
                        Use authenticator app instead
                    @else
                        Use a recovery code instead
                    @endif
                </flux:button>
            </div>
        </flux:card>
    </div>
</div>
```

## 4.5.4 Two-Factor Setup Component

### ******* Comprehensive 2FA Configuration Interface

**File**: `resources/views/livewire/auth/two-factor/setup.blade.php`

```php
<?php

use function Livewire\Volt\{state, mount, computed, on};
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use Laravel\Fortify\Actions\GenerateNewRecoveryCodes;
use Laravel\Fortify\Actions\DisableTwoFactorAuthentication;
use Illuminate\Support\Facades\Auth;

state([
    'showingQrCode' => false,
    'showingConfirmation' => false,
    'showingRecoveryCodes' => false,
    'code' => '',
    'isEnabled' => false,
    'confirmingDisable' => false,
]);

mount(function () {
    $this->isEnabled = Auth::user()->hasEnabledTwoFactorAuthentication();
});

$enableTwoFactorAuthentication = function () {
    $this->resetErrorBag();
    
    try {
        app(EnableTwoFactorAuthentication::class)(Auth::user());
        
        $this->showingQrCode = true;
        $this->showingConfirmation = true;
        $this->showingRecoveryCodes = false;
        
        $this->dispatch('two-factor-enabled');
    } catch (Exception $e) {
        $this->addError('enable', 'Failed to enable two-factor authentication. Please try again.');
    }
};

$confirmTwoFactorAuthentication = function () {
    $this->resetErrorBag();
    
    if (empty($this->code)) {
        $this->addError('code', 'Please enter the verification code from your authenticator app.');
        return;
    }
    
    $user = Auth::user();
    
    if ($user->confirmTwoFactorAuth($this->code)) {
        $this->isEnabled = true;
        $this->showingQrCode = false;
        $this->showingConfirmation = false;
        $this->showingRecoveryCodes = true;
        $this->code = '';
        
        session()->flash('status', 'Two-factor authentication has been enabled successfully.');
        $this->dispatch('two-factor-confirmed');
    } else {
        $this->addError('code', 'The provided two factor authentication code was invalid.');
    }
};

$disableTwoFactorAuthentication = function () {
    $this->resetErrorBag();
    
    try {
        app(DisableTwoFactorAuthentication::class)(Auth::user());
        
        $this->isEnabled = false;
        $this->showingQrCode = false;
        $this->showingConfirmation = false;
        $this->showingRecoveryCodes = false;
        $this->confirmingDisable = false;
        
        session()->flash('status', 'Two-factor authentication has been disabled.');
        $this->dispatch('two-factor-disabled');
    } catch (Exception $e) {
        $this->addError('disable', 'Failed to disable two-factor authentication. Please try again.');
    }
};

$regenerateRecoveryCodes = function () {
    app(GenerateNewRecoveryCodes::class)(Auth::user());
    $this->showingRecoveryCodes = true;
    session()->flash('status', 'New recovery codes have been generated.');
};

$qrCodeSvg = computed(function () {
    return Auth::user()->twoFactorQrCodeSvg();
});

$recoveryCodes = computed(function () {
    return Auth::user()->recoveryCodes();
});

?>

<div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
    <div class="space-y-8">
        <!-- Header -->
        <div>
            <flux:heading size="xl">Two-Factor Authentication</flux:heading>
            <flux:subheading class="mt-2">
                Add additional security to your account using two-factor authentication.
            </flux:subheading>
        </div>

        <!-- Status Messages -->
        @if (session('status'))
            <flux:banner variant="success" class="mb-6">
                {{ session('status') }}
            </flux:banner>
        @endif

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Setup/Status Card -->
            <flux:card>
                <flux:card.header>
                    <flux:heading size="lg">
                        @if ($isEnabled)
                            Two-Factor Authentication Enabled
                        @else
                            Enable Two-Factor Authentication
                        @endif
                    </flux:heading>
                </flux:card.header>

                <div class="space-y-6">
                    @if (!$isEnabled)
                        <!-- Enable 2FA Section -->
                        <div class="space-y-4">
                            <flux:description>
                                When two-factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.
                            </flux:description>

                            @if (!$showingQrCode)
                                <div>
                                    <flux:button 
                                        wire:click="enableTwoFactorAuthentication"
                                        variant="primary"
                                        size="lg"
                                        :loading="$wire.loading"
                                    >
                                        Enable Two-Factor Authentication
                                    </flux:button>
                                </div>
                            @endif

                            @error('enable')
                                <flux:error>{{ $message }}</flux:error>
                            @enderror
                        </div>
                    @endif

                    @if ($isEnabled && !$showingQrCode)
                        <!-- Enabled State Section -->
                        <div class="space-y-4">
                            <flux:banner variant="success">
                                <flux:icon.check-circle class="h-5 w-5" />
                                Two-factor authentication is enabled and protecting your account.
                            </flux:banner>

                            <flux:description>
                                Your account is protected with two-factor authentication. Store your recovery codes in a secure location.
                            </flux:description>

                            <div class="flex flex-wrap gap-3">
                                <flux:button 
                                    wire:click="$set('showingRecoveryCodes', true)"
                                    variant="outline"
                                >
                                    Show Recovery Codes
                                </flux:button>
                                <flux:button 
                                    wire:click="regenerateRecoveryCodes"
                                    variant="outline"
                                >
                                    Regenerate Recovery Codes
                                </flux:button>
                                <flux:button 
                                    wire:click="$set('confirmingDisable', true)"
                                    variant="danger"
                                >
                                    Disable
                                </flux:button>
                            </div>

                            @error('disable')
                                <flux:error>{{ $message }}</flux:error>
                            @enderror
                        </div>
                    @endif
                </div>
            </flux:card>

            <!-- QR Code/Recovery Codes Card -->
            @if ($showingQrCode || $showingRecoveryCodes)
                <flux:card>
                    @if ($showingQrCode)
                        <flux:card.header>
                            <flux:heading size="lg">Scan QR Code</flux:heading>
                        </flux:card.header>

                        <div class="space-y-6">
                            <flux:description>
                                Two-factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.
                            </flux:description>

                            <div class="flex justify-center p-6 bg-gray-50 rounded-lg">
                                <div class="text-center">
                                    <div class="inline-block p-4 bg-white rounded-lg shadow-sm">
                                        {!! $this->qrCodeSvg !!}
                                    </div>
                                    <flux:description class="mt-2">
                                        Scan this QR code with your authenticator app
                                    </flux:description>
                                </div>
                            </div>

                            @if ($showingConfirmation)
                                <!-- Confirmation Section -->
                                <div class="space-y-4 border-t pt-6">
                                    <flux:field>
                                        <flux:label for="confirmation-code">
                                            Confirmation Code
                                        </flux:label>
                                        <flux:input 
                                            id="confirmation-code"
                                            wire:model="code"
                                            type="text"
                                            maxlength="6"
                                            pattern="[0-9]{6}"
                                            autocomplete="one-time-code"
                                            placeholder="000000"
                                            class="text-center text-xl tracking-widest"
                                        />
                                        <flux:description>
                                            Enter the 6-digit code shown in your authenticator app to confirm setup.
                                        </flux:description>
                                        <flux:error name="code" />
                                    </flux:field>

                                    <div class="flex space-x-3">
                                        <flux:button 
                                            wire:click="confirmTwoFactorAuthentication"
                                            variant="primary"
                                            :loading="$wire.loading"
                                        >
                                            Confirm & Enable
                                        </flux:button>
                                        <flux:button 
                                            wire:click="$set('showingQrCode', false)"
                                            variant="outline"
                                        >
                                            Cancel
                                        </flux:button>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endif

                    @if ($showingRecoveryCodes)
                        <flux:card.header>
                            <flux:heading size="lg">Recovery Codes</flux:heading>
                        </flux:card.header>

                        <div class="space-y-4">
                            <flux:banner variant="warning">
                                <flux:icon.exclamation-triangle class="h-5 w-5" />
                                Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two-factor authentication device is lost.
                            </flux:banner>

                            <div class="grid grid-cols-2 gap-2 p-4 bg-gray-50 rounded-lg font-mono text-sm">
                                @foreach ($this->recoveryCodes as $code)
                                    <div class="text-center py-2 px-3 bg-white rounded border">
                                        {{ $code }}
                                    </div>
                                @endforeach
                            </div>

                            <flux:button 
                                wire:click="$set('showingRecoveryCodes', false)"
                                variant="outline"
                                size="sm"
                            >
                                Hide Recovery Codes
                            </flux:button>
                        </div>
                    @endif
                </flux:card>
            @endif
        </div>
    </div>

    <!-- Disable Confirmation Modal -->
    @if ($confirmingDisable)
        <flux:modal wire:model="confirmingDisable" variant="danger">
            <flux:modal.header>
                <flux:heading size="lg">Disable Two-Factor Authentication</flux:heading>
            </flux:modal.header>

            <flux:modal.body>
                <flux:description>
                    Are you sure you want to disable two-factor authentication? This will make your account less secure.
                </flux:description>
            </flux:modal.body>

            <flux:modal.footer>
                <flux:button 
                    wire:click="disableTwoFactorAuthentication"
                    variant="danger"
                    :loading="$wire.loading"
                >
                    Disable
                </flux:button>
                <flux:button 
                    wire:click="$set('confirmingDisable', false)"
                    variant="outline"
                >
                    Cancel
                </flux:button>
            </flux:modal.footer>
        </flux:modal>
    @endif
</div>
```

---

**Navigation Footer**

← [Previous: Filament Integration](040-filament-integration.md) | [Next: Testing & Validation →](050-testing-validation-unified.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/045-volt-flux-ui-components.md`
- **Document ID**: LF-2FA-007-UNIFIED
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
