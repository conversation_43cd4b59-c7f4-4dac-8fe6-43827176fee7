# 2.7 Laravel Fortify 2FA Implementation - Database Migration Planning

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 15 minutes

## 2.7.1 Executive Summary

This document provides comprehensive database migration planning for Laravel Fortify 2FA implementation, ensuring seamless integration with existing Filament 2FA fields while maintaining data integrity and providing robust rollback procedures.

### ******* Migration Strategy Overview

| Migration Phase | Description | Risk Level | Rollback Complexity |
|----------------|-------------|------------|-------------------|
| **Phase 1** | Add Fortify 2FA columns | 🟢 Low | Simple |
| **Phase 2** | Create indexes for performance | 🟢 Low | Simple |
| **Phase 3** | Data validation and cleanup | 🟡 Medium | Moderate |

## 2.7.2 Current Database Schema Analysis

### ******* Existing User Table Structure

**Current 2FA-Related Fields** (from User model analysis):

```sql
-- Existing Filament 2FA fields in users table
app_authentication_secret TEXT ENCRYPTED,
app_authentication_recovery_codes TEXT ENCRYPTED,
has_email_authentication BOOLEAN DEFAULT FALSE,
```

**Current Table Constraints**:
- Primary Key: `id` (auto-increment)
- Unique Constraints: `email`, `slug`, `public_id`
- Indexes: Standard Laravel user table indexes
- Foreign Keys: None for 2FA fields

### ******* Field Compatibility Analysis

| Field Purpose | Filament Implementation | Fortify Requirement | Conflict Risk |
|---------------|------------------------|-------------------|---------------|
| **TOTP Secret** | `app_authentication_secret` | `two_factor_secret` | ✅ No conflict |
| **Recovery Codes** | `app_authentication_recovery_codes` | `two_factor_recovery_codes` | ✅ No conflict |
| **2FA Status** | Implicit (secret exists) | `two_factor_confirmed_at` | ✅ No conflict |
| **Email 2FA** | `has_email_authentication` | Not used | ✅ No conflict |

## 2.7.3 Migration Implementation Plan

### 2.7.3.1 Migration File Structure

**Migration File Naming Convention**:
```bash
# Primary migration file
2025_07_01_120000_add_fortify_two_factor_columns_to_users_table.php

# Index optimization migration (separate for rollback safety)
2025_07_01_120001_add_two_factor_indexes_to_users_table.php
```

### 2.7.3.2 Primary Migration Implementation

**File**: `database/migrations/2025_07_01_120000_add_fortify_two_factor_columns_to_users_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Laravel Fortify 2FA columns
            $table->text('two_factor_secret')
                  ->nullable()
                  ->after('remember_token')
                  ->comment('Encrypted TOTP secret for Laravel Fortify 2FA');
                  
            $table->text('two_factor_recovery_codes')
                  ->nullable()
                  ->after('two_factor_secret')
                  ->comment('Encrypted recovery codes for Laravel Fortify 2FA');
                  
            $table->timestamp('two_factor_confirmed_at')
                  ->nullable()
                  ->after('two_factor_recovery_codes')
                  ->comment('Timestamp when 2FA was confirmed and activated');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop columns in reverse order for clean rollback
            $table->dropColumn([
                'two_factor_confirmed_at',
                'two_factor_recovery_codes', 
                'two_factor_secret'
            ]);
        });
    }
};
```

### 2.7.3.3 Index Optimization Migration

**File**: `database/migrations/2025_07_01_120001_add_two_factor_indexes_to_users_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Index for 2FA status queries
            $table->index('two_factor_confirmed_at', 'users_2fa_confirmed_index');
            
            // Composite index for authentication queries
            $table->index(['email', 'two_factor_confirmed_at'], 'users_email_2fa_index');
            
            // Index for cleanup operations (nullable fields)
            $table->index(['two_factor_secret', 'two_factor_confirmed_at'], 'users_2fa_status_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes by name for precise rollback
            $table->dropIndex('users_2fa_status_index');
            $table->dropIndex('users_email_2fa_index');
            $table->dropIndex('users_2fa_confirmed_index');
        });
    }
};
```

## 2.7.4 Rollback Strategy and Procedures

### ******* Rollback Safety Measures

**Pre-Migration Backup Strategy**:

```bash
# Create database backup before migration
php artisan db:backup --table=users --timestamp
# or for SQLite
cp database/database.sqlite database/database.sqlite.backup.$(date +%Y%m%d_%H%M%S)
```

**Rollback Validation Script**:

```php
<?php
// database/rollback_validation.php

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class TwoFactorMigrationRollback
{
    public static function validateRollback(): array
    {
        $results = [];
        
        // Check if Fortify columns exist
        $results['fortify_columns_exist'] = Schema::hasColumns('users', [
            'two_factor_secret',
            'two_factor_recovery_codes', 
            'two_factor_confirmed_at'
        ]);
        
        // Check if Filament columns still exist
        $results['filament_columns_exist'] = Schema::hasColumns('users', [
            'app_authentication_secret',
            'app_authentication_recovery_codes',
            'has_email_authentication'
        ]);
        
        // Count users with 2FA data
        $results['users_with_fortify_2fa'] = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->count();
            
        $results['users_with_filament_2fa'] = DB::table('users')
            ->whereNotNull('app_authentication_secret')
            ->count();
        
        return $results;
    }
    
    public static function safeRollback(): bool
    {
        $validation = self::validateRollback();
        
        // Only proceed with rollback if no users have Fortify 2FA enabled
        if ($validation['users_with_fortify_2fa'] > 0) {
            throw new Exception(
                "Cannot rollback: {$validation['users_with_fortify_2fa']} users have Fortify 2FA enabled"
            );
        }
        
        return true;
    }
}
```

### 2.7.4.2 Resilient Rollback Implementation

**Enhanced Migration with Rollback Protection**:

```php
public function down(): void
{
    // Validate rollback safety before proceeding
    $usersWithFortify2FA = DB::table('users')
        ->whereNotNull('two_factor_secret')
        ->orWhereNotNull('two_factor_confirmed_at')
        ->count();
    
    if ($usersWithFortify2FA > 0) {
        throw new Exception(
            "Cannot rollback migration: {$usersWithFortify2FA} users have Fortify 2FA data. " .
            "Please disable 2FA for all users before rolling back."
        );
    }
    
    // Proceed with safe rollback
    Schema::table('users', function (Blueprint $table) {
        $table->dropColumn([
            'two_factor_confirmed_at',
            'two_factor_recovery_codes', 
            'two_factor_secret'
        ]);
    });
    
    // Log successful rollback
    Log::info('Fortify 2FA migration rolled back successfully', [
        'timestamp' => now(),
        'affected_users' => 0
    ]);
}
```

## 2.7.5 Data Migration and Validation

### ******* Post-Migration Validation

**Validation Artisan Command**:

```php
<?php
// app/Console/Commands/ValidateTwoFactorMigration.php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class ValidateTwoFactorMigration extends Command
{
    protected $signature = 'fortify:validate-migration';
    protected $description = 'Validate Fortify 2FA migration integrity';

    public function handle(): int
    {
        $this->info('Validating Fortify 2FA migration...');
        
        // Check schema
        $this->validateSchema();
        
        // Check indexes
        $this->validateIndexes();
        
        // Check data integrity
        $this->validateDataIntegrity();
        
        $this->info('✅ Migration validation completed successfully');
        return 0;
    }
    
    private function validateSchema(): void
    {
        $requiredColumns = [
            'two_factor_secret',
            'two_factor_recovery_codes',
            'two_factor_confirmed_at'
        ];
        
        foreach ($requiredColumns as $column) {
            if (!Schema::hasColumn('users', $column)) {
                $this->error("❌ Missing column: {$column}");
                exit(1);
            }
            $this->line("✅ Column exists: {$column}");
        }
    }
    
    private function validateIndexes(): void
    {
        $indexes = DB::select("PRAGMA index_list(users)");
        $indexNames = collect($indexes)->pluck('name')->toArray();
        
        $requiredIndexes = [
            'users_2fa_confirmed_index',
            'users_email_2fa_index',
            'users_2fa_status_index'
        ];
        
        foreach ($requiredIndexes as $index) {
            if (!in_array($index, $indexNames)) {
                $this->warn("⚠️  Missing index: {$index}");
            } else {
                $this->line("✅ Index exists: {$index}");
            }
        }
    }
    
    private function validateDataIntegrity(): void
    {
        // Check for orphaned 2FA data
        $orphanedSecrets = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->whereNull('two_factor_confirmed_at')
            ->count();
            
        if ($orphanedSecrets > 0) {
            $this->warn("⚠️  Found {$orphanedSecrets} users with 2FA secrets but no confirmation timestamp");
        }
        
        // Check for confirmed 2FA without secrets
        $confirmedWithoutSecrets = DB::table('users')
            ->whereNotNull('two_factor_confirmed_at')
            ->whereNull('two_factor_secret')
            ->count();
            
        if ($confirmedWithoutSecrets > 0) {
            $this->error("❌ Found {$confirmedWithoutSecrets} users with 2FA confirmation but no secret");
        }
        
        $this->line("✅ Data integrity validation completed");
    }
}
```

## 2.7.6 Performance Impact Analysis

### 2.7.6.1 Migration Performance Metrics

**Expected Migration Times**:

| Operation | Small DB (<1K users) | Medium DB (<10K users) | Large DB (>10K users) |
|-----------|---------------------|----------------------|----------------------|
| **Add Columns** | < 1 second | < 5 seconds | < 30 seconds |
| **Create Indexes** | < 1 second | < 10 seconds | < 60 seconds |
| **Rollback** | < 1 second | < 5 seconds | < 30 seconds |

### 2.7.6.2 Post-Migration Performance

**Query Performance Impact**:

```sql
-- Before migration (Filament only)
SELECT * FROM users WHERE app_authentication_secret IS NOT NULL;

-- After migration (dual 2FA support)
SELECT * FROM users 
WHERE app_authentication_secret IS NOT NULL 
   OR two_factor_confirmed_at IS NOT NULL;

-- Optimized with indexes
SELECT * FROM users 
WHERE two_factor_confirmed_at IS NOT NULL
UNION
SELECT * FROM users 
WHERE app_authentication_secret IS NOT NULL;
```

## 2.7.7 Testing Strategy

### 2.7.7.1 Migration Testing Plan

**Test Scenarios**:

1. **Fresh Installation Test**
   - Clean database migration
   - Verify all columns and indexes created
   - Validate rollback functionality

2. **Existing Data Test**
   - Database with existing Filament 2FA users
   - Verify no data corruption
   - Test dual 2FA functionality

3. **Rollback Test**
   - Enable Fortify 2FA for test users
   - Attempt rollback (should fail safely)
   - Disable 2FA and successful rollback

**Test Implementation**:

```php
// tests/Feature/Database/TwoFactorMigrationTest.php
class TwoFactorMigrationTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_migration_adds_required_columns(): void
    {
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_secret'));
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_recovery_codes'));
        $this->assertTrue(Schema::hasColumn('users', 'two_factor_confirmed_at'));
    }
    
    public function test_rollback_removes_columns(): void
    {
        Artisan::call('migrate:rollback', ['--step' => 2]);
        
        $this->assertFalse(Schema::hasColumn('users', 'two_factor_secret'));
        $this->assertFalse(Schema::hasColumn('users', 'two_factor_recovery_codes'));
        $this->assertFalse(Schema::hasColumn('users', 'two_factor_confirmed_at'));
    }
    
    public function test_rollback_protection_with_active_users(): void
    {
        $user = User::factory()->create([
            'two_factor_secret' => 'test-secret',
            'two_factor_confirmed_at' => now()
        ]);
        
        $this->expectException(Exception::class);
        Artisan::call('migrate:rollback', ['--step' => 2]);
    }
}
```

---

**Navigation Footer**

← [Previous: Architecture Planning](025-architecture-planning.md) | [Next: Implementation Documentation →](030-implementation-documentation.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/027-database-migration-planning.md`
- **Document ID**: LF-2FA-004
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
