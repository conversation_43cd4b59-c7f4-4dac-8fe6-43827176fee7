# 3.0 Laravel Fortify 2FA Implementation - Unified Implementation Guide

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 35 minutes

## 3.1 Executive Summary

This document provides step-by-step implementation instructions for Laravel Fortify as the unified authentication system, replacing Filament's built-in authentication while preserving existing user data. All commands and code examples are tested and verified for Laravel 12.19.3 with PHP 8.4, integrating seamlessly with Livewire/Volt and Flux UI components.

### 3.1.1 Implementation Roadmap

| Step | Task | Estimated Time | Dependencies |
|------|------|----------------|--------------|
| **3.2** | Package Installation | 15 minutes | Composer access |
| **3.3** | Database Migration | 20 minutes | Package installed |
| **3.4** | Service Provider Setup | 25 minutes | Migration complete |
| **3.5** | User Model Transformation | 15 minutes | Service providers |
| **3.6** | Filament Integration | 30 minutes | Model updated |
| **3.7** | Volt + Flux UI Components | 45 minutes | Filament configured |

## 3.2 Package Installation

### 3.2.1 Laravel Fortify Installation

**Step 1: Install Laravel Fortify Package**

```bash
# Navigate to project root
cd /Users/<USER>/Herd/lfsl

# Install Laravel Fortify with Laravel 12.x compatibility
composer require laravel/fortify "^1.25"
```

**Expected Output**:
```bash
Using version ^1.25.0 for laravel/fortify
./composer.json has been updated
Running composer update laravel/fortify
Loading composer repositories with package information
Updating dependencies
Lock file operations: 1 install, 0 updates, 0 removals
  - Installing laravel/fortify (v1.25.0)
Writing lock file
Installing dependencies from lock file (including require-dev)
Package operations: 1 install, 0 updates, 0 removals
  - Installing laravel/fortify (v1.25.0): Extracting archive
Generating optimized autoload files
```

**Step 2: Install Laravel Sanctum (Required Dependency)**

```bash
# Install Sanctum for API authentication (required by Fortify)
composer require laravel/sanctum "^4.0"
```

**Step 3: Publish Configuration Files**

```bash
# Publish Fortify configuration
php artisan vendor:publish --provider="Laravel\Fortify\FortifyServiceProvider"

# Publish Sanctum configuration
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

**Expected Files Created**:
```bash
# Fortify configuration
config/fortify.php

# Sanctum configuration  
config/sanctum.php

# Fortify stubs (for customization)
stubs/fortify/
```

**Step 4: Verify Installation**

```bash
# Check installed packages
composer show laravel/fortify laravel/sanctum

# Verify configuration files
ls -la config/fortify.php config/sanctum.php

# Verify existing Flux/Volt packages
composer show livewire/flux livewire/flux-pro livewire/volt
```

### 3.2.2 Updated Package Dependencies

**Final composer.json State**:

```json
{
    "require": {
        "php": "^8.4",
        "laravel/framework": "^12.0",
        "laravel/fortify": "^1.25",
        "laravel/sanctum": "^4.0",
        "laravel/tinker": "^2.10.1",
        "filament/filament": "^3.2",
        "livewire/flux": "^2.1",
        "livewire/flux-pro": "^2.2",
        "livewire/volt": "^1.7.0",
        "spatie/laravel-activitylog": "^4.10",
        "spatie/laravel-data": "^4.17",
        "spatie/laravel-query-builder": "^6.3",
        "spatie/laravel-sluggable": "*",
        "wildside/userstamps": "^3.1"
    }
}
```

## 3.3 Database Migration Implementation

### 3.3.1 Create Migration Files

**Step 1: Generate Schema Extension Migration**

```bash
# Create migration for Fortify 2FA fields
php artisan make:migration add_fortify_two_factor_fields_to_users_table --table=users
```

**Step 2: Implement Schema Extension Migration**

Edit the generated migration file:

```php
<?php
// database/migrations/YYYY_MM_DD_HHMMSS_add_fortify_two_factor_fields_to_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Laravel Fortify 2FA columns
            $table->text('two_factor_secret')
                  ->nullable()
                  ->after('remember_token')
                  ->comment('Encrypted TOTP secret for Laravel Fortify 2FA');
                  
            $table->text('two_factor_recovery_codes')
                  ->nullable()
                  ->after('two_factor_secret')
                  ->comment('Encrypted recovery codes for Laravel Fortify 2FA');
                  
            $table->timestamp('two_factor_confirmed_at')
                  ->nullable()
                  ->after('two_factor_recovery_codes')
                  ->comment('Timestamp when Fortify 2FA was confirmed and activated');
        });
        
        Log::info('Fortify 2FA schema extension completed', [
            'timestamp' => now(),
            'migration' => class_basename(static::class)
        ]);
    }

    /**
     * Reverse the migrations with safety validation.
     */
    public function down(): void
    {
        // Safety check: Prevent rollback if users have Fortify 2FA enabled
        $fortifyUsersCount = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->orWhereNotNull('two_factor_confirmed_at')
            ->count();
        
        if ($fortifyUsersCount > 0) {
            throw new Exception(
                "Cannot rollback: {$fortifyUsersCount} users have Fortify 2FA data. " .
                "Please migrate data back to Filament fields before rolling back."
            );
        }
        
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'two_factor_confirmed_at',
                'two_factor_recovery_codes', 
                'two_factor_secret'
            ]);
        });
        
        Log::info('Fortify 2FA schema rollback completed', [
            'timestamp' => now(),
            'preserved_filament_users' => DB::table('users')->whereNotNull('app_authentication_secret')->count()
        ]);
    }
};
```

**Step 3: Create Data Migration**

```bash
# Create migration for data migration
php artisan make:migration migrate_filament_to_fortify_2fa_data
```

```php
<?php
// database/migrations/YYYY_MM_DD_HHMMSS_migrate_filament_to_fortify_2fa_data.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations - Migrate Filament 2FA data to Fortify fields.
     */
    public function up(): void
    {
        $migratedCount = 0;
        $errorCount = 0;
        
        // Process users in chunks to handle large datasets
        DB::table('users')
            ->whereNotNull('app_authentication_secret')
            ->chunk(100, function ($users) use (&$migratedCount, &$errorCount) {
                foreach ($users as $user) {
                    try {
                        // Validate Filament data before migration
                        if (empty($user->app_authentication_secret)) {
                            continue;
                        }
                        
                        // Prepare migration data
                        $migrationData = [
                            'two_factor_secret' => $user->app_authentication_secret,
                            'two_factor_recovery_codes' => $user->app_authentication_recovery_codes,
                            'two_factor_confirmed_at' => now(), // Mark as confirmed since it was active in Filament
                            'updated_at' => now(),
                        ];
                        
                        // Perform migration
                        DB::table('users')
                            ->where('id', $user->id)
                            ->update($migrationData);
                        
                        $migratedCount++;
                        
                    } catch (Exception $e) {
                        $errorCount++;
                        Log::error('Failed to migrate user 2FA data', [
                            'user_id' => $user->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });
        
        Log::info('Filament to Fortify 2FA data migration completed', [
            'migrated_users' => $migratedCount,
            'errors' => $errorCount,
            'timestamp' => now()
        ]);
        
        if ($errorCount > 0) {
            throw new Exception("Migration completed with {$errorCount} errors. Check logs for details.");
        }
    }

    /**
     * Reverse the migrations - Restore Filament data from Fortify fields.
     */
    public function down(): void
    {
        $restoredCount = 0;
        
        // Only restore if Filament fields are empty and Fortify fields have data
        DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->whereNull('app_authentication_secret')
            ->chunk(100, function ($users) use (&$restoredCount) {
                foreach ($users as $user) {
                    try {
                        DB::table('users')
                            ->where('id', $user->id)
                            ->update([
                                'app_authentication_secret' => $user->two_factor_secret,
                                'app_authentication_recovery_codes' => $user->two_factor_recovery_codes,
                                'has_email_authentication' => !is_null($user->two_factor_confirmed_at),
                                'updated_at' => now(),
                            ]);
                        
                        $restoredCount++;
                        
                    } catch (Exception $e) {
                        Log::error('Failed to restore user 2FA data', [
                            'user_id' => $user->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });
        
        Log::info('Fortify to Filament 2FA data restoration completed', [
            'restored_users' => $restoredCount,
            'timestamp' => now()
        ]);
    }
};
```

**Step 4: Create Performance Index Migration**

```bash
# Create separate migration for indexes
php artisan make:migration add_fortify_2fa_indexes_to_users_table --table=users
```

```php
<?php
// database/migrations/YYYY_MM_DD_HHMMSS_add_fortify_2fa_indexes_to_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Performance indexes for Fortify 2FA queries
            $table->index('two_factor_confirmed_at', 'users_fortify_2fa_confirmed_index');
            $table->index(['email', 'two_factor_confirmed_at'], 'users_email_fortify_2fa_index');
            $table->index(['two_factor_secret', 'two_factor_confirmed_at'], 'users_fortify_2fa_status_index');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_fortify_2fa_status_index');
            $table->dropIndex('users_email_fortify_2fa_index');
            $table->dropIndex('users_fortify_2fa_confirmed_index');
        });
    }
};
```

**Step 5: Run Migrations**

```bash
# Execute migrations in sequence
php artisan migrate

# Verify migration status
php artisan migrate:status

# Check database schema
php artisan tinker
>>> Schema::getColumnListing('users')
>>> DB::table('users')->whereNotNull('two_factor_secret')->count()
```

## 3.4 Service Provider Implementation

### 3.4.1 Create FortifyServiceProvider

**Step 1: Generate Service Provider**

```bash
# Create Fortify service provider
php artisan make:provider FortifyServiceProvider
```

**Step 2: Implement FortifyServiceProvider for Unified Authentication**

```php
<?php
// app/Providers/FortifyServiceProvider.php

namespace App\Providers;

use App\Actions\Fortify\CreateNewUser;
use App\Actions\Fortify\ResetUserPassword;
use App\Actions\Fortify\UpdateUserPassword;
use App\Actions\Fortify\UpdateUserProfileInformation;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Laravel\Fortify\Fortify;

class FortifyServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register any bindings or singletons here if needed
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Fortify actions
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        // Configure rate limiting for security
        $this->configureRateLimiting();

        // Register Volt + Flux views for modern UI
        $this->registerVoltFluxViews();
    }

    /**
     * Configure rate limiting for authentication endpoints.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('login', function (Request $request) {
            $email = (string) $request->email;
            return Limit::perMinute(5)->by($email.$request->ip());
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });
    }

    /**
     * Register Volt + Flux views for Fortify authentication.
     */
    protected function registerVoltFluxViews(): void
    {
        Fortify::loginView(function () {
            return view('livewire.auth.login');
        });

        Fortify::registerView(function () {
            return view('livewire.auth.register');
        });

        Fortify::requestPasswordResetLinkView(function () {
            return view('livewire.auth.forgot-password');
        });

        Fortify::resetPasswordView(function ($request) {
            return view('livewire.auth.reset-password', ['request' => $request]);
        });

        Fortify::verifyEmailView(function () {
            return view('livewire.auth.verify-email');
        });

        Fortify::twoFactorChallengeView(function () {
            return view('livewire.auth.two-factor-challenge');
        });

        Fortify::confirmPasswordView(function () {
            return view('livewire.auth.confirm-password');
        });
    }
}
```

**Step 3: Register Service Provider**

Edit `bootstrap/providers.php`:

```php
<?php

return [
    App\Providers\AppServiceProvider::class,
    App\Providers\Filament\AdminPanelProvider::class,
    App\Providers\VoltServiceProvider::class,
    App\Providers\FortifyServiceProvider::class, // Add Fortify provider
];
```

### 3.4.2 Create Fortify Action Classes

**Step 1: Create Actions Directory Structure**

```bash
# Create actions directory
mkdir -p app/Actions/Fortify
```

**Step 2: Create UpdateUserProfileInformation Action**

```php
<?php
// app/Actions/Fortify/UpdateUserProfileInformation.php

namespace App\Actions\Fortify;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;

class UpdateUserProfileInformation implements UpdatesUserProfileInformation
{
    /**
     * Validate and update the given user's profile information.
     */
    public function update($user, array $input): void
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
        ])->validateWithBag('updateProfileInformation');

        if ($input['email'] !== $user->email &&
            $user instanceof MustVerifyEmail) {
            $this->updateVerifiedUser($user, $input);
        } else {
            $user->forceFill([
                'name' => $input['name'],
                'email' => $input['email'],
            ])->save();
        }
    }

    /**
     * Update the given verified user's profile information.
     */
    protected function updateVerifiedUser($user, array $input): void
    {
        $user->forceFill([
            'name' => $input['name'],
            'email' => $input['email'],
            'email_verified_at' => null,
        ])->save();

        $user->sendEmailVerificationNotification();
    }
}
```

**Step 3: Create Additional Required Actions**

```bash
# Copy Fortify action stubs if available
cp -r stubs/fortify/* app/Actions/Fortify/ 2>/dev/null || echo "Stubs not found, creating manually"
```

**CreateNewUser.php**:

```php
<?php
// app/Actions/Fortify/CreateNewUser.php

namespace App\Actions\Fortify;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;
use Laravel\Fortify\Contracts\CreatesNewUsers;

class CreateNewUser implements CreatesNewUsers
{
    /**
     * Validate and create a newly registered user.
     */
    public function create(array $input): User
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', Password::default(), 'confirmed'],
        ])->validate();

        return User::create([
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
        ]);
    }
}
```

---

**Navigation Footer**

← [Previous: Database Migration Strategy](025-database-migration-strategy.md) | [Next: User Model Transformation →](035-user-model-transformation.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/030-implementation-guide-unified.md`
- **Document ID**: LF-2FA-004-UNIFIED
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
