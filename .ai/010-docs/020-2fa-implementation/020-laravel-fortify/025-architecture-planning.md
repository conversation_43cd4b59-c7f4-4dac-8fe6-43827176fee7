# 2.5 Laravel Fortify 2FA Implementation - Architecture Planning

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 18 minutes

## 2.5.1 Executive Summary

This document provides detailed architecture planning for integrating Laravel Fortify 2FA with the existing Filament 4.0-beta11 system, ensuring seamless coexistence and optimal user experience across different application contexts.

### ******* Architecture Overview

```mermaid
graph TB
    subgraph "User Authentication Layer"
        A[User Login Request] --> B{Authentication Context}
        B -->|Admin Panel| C[Filament Auth Pipeline]
        B -->|Web Application| D[Fortify Auth Pipeline]
    end
    
    subgraph "Filament 2FA System"
        C --> E[Filament 2FA Middleware]
        E --> F[Google2FA Verification]
        F --> G[Admin Dashboard Access]
    end
    
    subgraph "Fortify 2FA System"
        D --> H[Fortify 2FA Middleware]
        H --> I[TOTP Verification]
        I --> J[Web App Features]
    end
    
    subgraph "Shared Components"
        K[User Model] --> L[Filament 2FA Fields]
        K --> M[Fortify 2FA Fields]
        N[Database Layer] --> L
        N --> M
    end
    
    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style J fill:#c8e6c9
    style K fill:#fff3e0
```

## 2.5.2 Component Architecture Design

### ******* Authentication Context Separation

**Design Principle**: Complete isolation between Filament and Fortify 2FA systems to prevent conflicts and ensure independent operation.

| Component | Filament Context | Fortify Context | Isolation Method |
|-----------|------------------|-----------------|------------------|
| **Routes** | `/admin/*` | `/app/*`, `/user/*` | Route prefixes |
| **Middleware** | `filament.auth` | `auth`, `verified` | Different middleware stacks |
| **Guards** | `filament` | `web` | Separate authentication guards |
| **Sessions** | `filament_session` | `laravel_session` | Session key prefixes |

### ******* Database Schema Architecture

**Strategy**: Extend existing User model with additional Fortify fields while preserving Filament functionality.

```php
// Enhanced User Model Architecture
class User extends Authenticatable implements 
    FilamentUser, 
    HasAppAuthentication, 
    HasAppAuthenticationRecovery,
    HasAvatar, 
    HasEmailAuthentication, 
    MustVerifyEmail,
    TwoFactorAuthenticatable  // New Fortify interface
{
    use HasFactory, 
        Notifiable, 
        SoftDeletes, 
        HasSlug, 
        LogsActivity, 
        Userstamps,
        TwoFactorAuthenticatable; // New Fortify trait
}
```

**Field Mapping Strategy**:

```php
// Dual 2FA field architecture
protected $fillable = [
    // Existing fields
    'name', 'email', 'password', 'slug', 'public_id',
    
    // Filament 2FA fields
    'has_email_authentication',
    
    // Fortify 2FA fields (added via migration)
    'two_factor_confirmed_at',
];

protected $hidden = [
    'password', 'remember_token',
    
    // Filament 2FA secrets
    'app_authentication_secret',
    'app_authentication_recovery_codes',
    
    // Fortify 2FA secrets
    'two_factor_secret',
    'two_factor_recovery_codes',
];

protected $casts = [
    // Existing casts
    'email_verified_at' => 'datetime',
    'password' => 'hashed',
    
    // Filament 2FA casts
    'app_authentication_secret' => 'encrypted',
    'app_authentication_recovery_codes' => 'encrypted:array',
    'has_email_authentication' => 'boolean',
    
    // Fortify 2FA casts
    'two_factor_confirmed_at' => 'datetime',
];
```

## 2.5.3 Service Provider Architecture

### 2.5.3.1 FortifyServiceProvider Design

**Location**: `app/Providers/FortifyServiceProvider.php`

```php
<?php

namespace App\Providers;

use App\Actions\Fortify\CreateNewUser;
use App\Actions\Fortify\ResetUserPassword;
use App\Actions\Fortify\UpdateUserPassword;
use App\Actions\Fortify\UpdateUserProfileInformation;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Laravel\Fortify\Fortify;

class FortifyServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // Service container bindings
    }

    public function boot(): void
    {
        // Fortify action bindings
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        // Rate limiting configuration
        RateLimiter::for('login', function (Request $request) {
            $email = (string) $request->email;
            return Limit::perMinute(5)->by($email.$request->ip());
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });

        // Custom view registration
        $this->registerViews();
    }

    private function registerViews(): void
    {
        Fortify::loginView(function () {
            return view('auth.login');
        });

        Fortify::twoFactorChallengeView(function () {
            return view('auth.two-factor-challenge');
        });

        Fortify::registerView(function () {
            return view('auth.register');
        });
    }
}
```

### ******* Service Provider Registration Strategy

**Boot Order Dependency**:

```php
// config/app.php - Provider registration order
'providers' => [
    // Core Laravel providers...
    
    // Application providers
    App\Providers\AppServiceProvider::class,
    App\Providers\AuthServiceProvider::class,
    App\Providers\EventServiceProvider::class,
    App\Providers\RouteServiceProvider::class,
    
    // Filament providers (existing)
    App\Providers\Filament\AdminPanelProvider::class,
    
    // Fortify provider (new - must be after AuthServiceProvider)
    App\Providers\FortifyServiceProvider::class,
],
```

## 2.5.4 Middleware Architecture

### 2.5.4.1 Middleware Stack Design

**Filament Middleware Stack** (Unchanged):
```php
// Filament handles its own 2FA middleware internally
Route::middleware(['filament.auth', 'filament.2fa'])->group(function () {
    // Admin panel routes
});
```

**Fortify Middleware Stack** (New):
```php
// Web application routes with Fortify 2FA
Route::middleware(['auth:web', 'verified', '2fa.enabled'])->group(function () {
    // Protected web application routes
});

// 2FA setup routes (authenticated but not requiring 2FA)
Route::middleware(['auth:web', 'verified'])->group(function () {
    // 2FA management routes
});
```

### ******* Custom Middleware Implementation

**TwoFactorEnabled Middleware**:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureTwoFactorEnabled
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        // Skip 2FA check for Filament routes (handled separately)
        if ($request->is('admin/*')) {
            return $next($request);
        }
        
        // Check if user has 2FA enabled for web routes
        if ($user && !$user->hasEnabledTwoFactorAuthentication()) {
            return redirect()->route('two-factor.setup')
                ->with('warning', 'Please enable two-factor authentication to continue.');
        }
        
        return $next($request);
    }
}
```

## 2.5.5 User Interface Architecture

### ******* Component Structure

**Blade Template Hierarchy**:

```
resources/views/
├── auth/
│   ├── login.blade.php                 # Standard login (Fortify)
│   ├── register.blade.php              # Registration (Fortify)
│   ├── two-factor-challenge.blade.php  # 2FA verification (Fortify)
│   └── passwords/
│       ├── email.blade.php
│       └── reset.blade.php
├── user/
│   ├── profile/
│   │   ├── show.blade.php              # Profile management
│   │   └── partials/
│   │       ├── two-factor-form.blade.php
│   │       ├── recovery-codes.blade.php
│   │       └── qr-code-display.blade.php
│   └── dashboard.blade.php
└── layouts/
    ├── app.blade.php                   # Main application layout
    ├── guest.blade.php                 # Guest/auth layout
    └── partials/
        ├── navigation.blade.php
        └── two-factor-status.blade.php
```

### 2.5.5.2 Component Design Patterns

**Two-Factor Setup Component**:

```php
// Livewire component for 2FA management
<?php

namespace App\Http\Livewire\Profile;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use Laravel\Fortify\Actions\GenerateNewRecoveryCodes;
use Livewire\Component;

class TwoFactorAuthenticationForm extends Component
{
    public bool $showingQrCode = false;
    public bool $showingConfirmation = false;
    public bool $showingRecoveryCodes = false;
    public string $code = '';

    public function enableTwoFactorAuthentication(): void
    {
        $this->resetErrorBag();

        app(EnableTwoFactorAuthentication::class)(Auth::user());

        $this->showingQrCode = true;
        $this->showingConfirmation = true;
        $this->showingRecoveryCodes = false;
    }

    public function render()
    {
        return view('profile.two-factor-authentication-form');
    }
}
```

## 2.5.6 Security Architecture

### 2.5.6.1 Encryption Strategy

**Secret Storage Security**:

```php
// Enhanced encryption for 2FA secrets
class TwoFactorSecretEncryption
{
    public static function encrypt(string $secret): string
    {
        return encrypt($secret, false); // No serialization for strings
    }
    
    public static function decrypt(string $encryptedSecret): string
    {
        return decrypt($encryptedSecret, false);
    }
    
    public static function generateSecure(): string
    {
        return app(Google2FA::class)->generateSecretKey(32);
    }
}
```

### ******* Rate Limiting Architecture

**Multi-layer Rate Limiting**:

```php
// config/fortify.php - Rate limiting configuration
'limiters' => [
    'login' => 'login',
    'two-factor' => 'two-factor',
],

// Custom rate limiters in FortifyServiceProvider
RateLimiter::for('two-factor', function (Request $request) {
    return [
        Limit::perMinute(5)->by($request->session()->get('login.id')),
        Limit::perHour(20)->by($request->ip()),
    ];
});
```

## 2.5.7 Testing Architecture

### ******* Test Structure Design

**Test Organization**:

```
tests/
├── Feature/
│   ├── Auth/
│   │   ├── TwoFactorAuthenticationTest.php
│   │   ├── TwoFactorSetupTest.php
│   │   └── RecoveryCodeTest.php
│   └── Profile/
│       └── TwoFactorManagementTest.php
├── Unit/
│   ├── Actions/
│   │   ├── EnableTwoFactorAuthenticationTest.php
│   │   └── GenerateRecoveryCodesTest.php
│   └── Models/
│       └── UserTwoFactorTest.php
└── Browser/
    ├── TwoFactorSetupTest.php
    └── TwoFactorLoginTest.php
```

### ******* Testing Strategy

**Comprehensive Test Coverage**:

```php
// Example feature test structure
class TwoFactorAuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_enable_two_factor_authentication(): void
    {
        $user = User::factory()->create();
        
        $this->actingAs($user)
            ->post('/user/two-factor-authentication')
            ->assertRedirect();
            
        $this->assertNotNull($user->fresh()->two_factor_secret);
        $this->assertNotNull($user->fresh()->two_factor_recovery_codes);
    }
    
    public function test_two_factor_authentication_can_be_confirmed(): void
    {
        // Test implementation...
    }
    
    public function test_recovery_codes_can_be_regenerated(): void
    {
        // Test implementation...
    }
}
```

## 2.5.8 Performance Architecture

### 2.5.8.1 Caching Strategy

**2FA Status Caching**:

```php
// Efficient 2FA status checking with caching
class User extends Authenticatable
{
    public function hasEnabledTwoFactorAuthentication(): bool
    {
        return Cache::remember(
            "user.{$this->id}.2fa_enabled",
            now()->addMinutes(5),
            fn () => !is_null($this->two_factor_secret) && 
                     !is_null($this->two_factor_confirmed_at)
        );
    }
    
    public function clearTwoFactorCache(): void
    {
        Cache::forget("user.{$this->id}.2fa_enabled");
    }
}
```

### 2.5.8.2 Database Optimization

**Index Strategy**:

```php
// Migration with performance indexes
Schema::table('users', function (Blueprint $table) {
    $table->text('two_factor_secret')->nullable();
    $table->text('two_factor_recovery_codes')->nullable();
    $table->timestamp('two_factor_confirmed_at')->nullable();
    
    // Performance indexes
    $table->index('two_factor_confirmed_at');
    $table->index(['email', 'two_factor_confirmed_at']);
});
```

---

**Navigation Footer**

← [Previous: Gap Analysis & Documentation](020-gap-analysis-and-documentation.md) | [Next: Database Migration Planning →](027-database-migration-planning.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/025-architecture-planning.md`
- **Document ID**: LF-2FA-003
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
