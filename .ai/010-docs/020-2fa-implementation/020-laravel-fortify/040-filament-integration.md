# 4.0 Laravel Fortify 2FA Implementation - Filament Integration

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 25 minutes

## 4.1 Executive Summary

This document provides comprehensive instructions for integrating Filament admin panel with Laravel Fortify authentication, replacing Filament's built-in authentication system while maintaining all panel functionality. The integration ensures seamless admin access through unified Fortify authentication with mandatory 2FA requirements.

## 4.2 Current Filament Configuration Analysis

### 4.2.1 Existing AdminPanelProvider Configuration

**Current Implementation** (to be modified):

<augment_code_snippet path="app/Providers/Filament/AdminPanelProvider.php" mode="EXCERPT">
````php
->multiFactorAuthentication([
    AppAuthentication::make()
        ->brandName('LFSL Filament Demo')
        ->codeWindow(4)
        ->recoverable()
        ->recoveryCodeCount(10),
    EmailAuthentication::make(),
], isRequired: true)
````
</augment_code_snippet>

**Components to Remove/Replace**:

| Current Feature | Action | Fortify Replacement |
|----------------|--------|-------------------|
| `->login()` | 🔴 Remove | Fortify login views |
| `->registration()` | 🔴 Remove | Fortify registration |
| `->passwordReset()` | 🔴 Remove | Fortify password reset |
| `->emailVerification()` | 🔴 Remove | Fortify email verification |
| `->multiFactorAuthentication([...])` | 🔴 Remove | Fortify 2FA system |
| `->profile()` | ✅ Keep | Profile management |
| `->strictAuthorization()` | ✅ Keep | Authorization logic |

## 4.3 Filament Integration Implementation

### 4.3.1 Step 1: Update AdminPanelProvider

**Modified AdminPanelProvider Configuration**:

```php
<?php
// app/Providers/Filament/AdminPanelProvider.php

namespace App\Providers\Filament;

use App\Http\Middleware\FortifyAuthenticateForFilament;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            
            // Remove Filament's built-in authentication features
            // ->login()
            // ->registration()
            // ->passwordReset()
            // ->emailVerification()
            // ->multiFactorAuthentication([
            //     AppAuthentication::make()
            //         ->brandName('LFSL Filament Demo')
            //         ->codeWindow(4)
            //         ->recoverable()
            //         ->recoveryCodeCount(10),
            //     EmailAuthentication::make(),
            // ], isRequired: true)
            
            // Configure for Fortify authentication
            ->authGuard('web')           // Use Fortify's web guard
            ->profile()                  // Keep profile management
            ->strictAuthorization()
            
            ->colors([
                'primary' => Color::Amber,
            ])
            
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                FortifyAuthenticateForFilament::class, // Custom Fortify authentication middleware
            ]);
    }
}
```

### 4.3.2 Step 2: Create Custom Filament Authentication Middleware

**Create Custom Middleware**:

```bash
# Create custom middleware for Filament-Fortify integration
php artisan make:middleware FortifyAuthenticateForFilament
```

**Implement Custom Middleware**:

```php
<?php
// app/Http/Middleware/FortifyAuthenticateForFilament.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class FortifyAuthenticateForFilament
{
    /**
     * Handle an incoming request.
     *
     * This middleware ensures users are authenticated via Fortify and have 2FA enabled
     * before accessing the Filament admin panel.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated via Fortify web guard
        if (!Auth::guard('web')->check()) {
            return redirect()->route('login')
                ->with('message', 'Please log in to access the admin panel.');
        }
        
        $user = Auth::user();
        
        // Check if user has Fortify 2FA enabled (required for admin access)
        if (!$user->hasEnabledTwoFactorAuthentication()) {
            return redirect()->route('two-factor.setup')
                ->with('status', 'two-factor-required-for-admin')
                ->with('message', 'Two-factor authentication is required to access the admin panel.');
        }
        
        // Check if user can access the current Filament panel
        $panel = \Filament\Facades\Filament::getCurrentPanel();
        if ($panel && !$user->canAccessPanel($panel)) {
            abort(403, 'You do not have permission to access this admin panel.');
        }
        
        return $next($request);
    }
}
```

### 4.3.3 Step 3: Register Custom Middleware

**Update Kernel.php**:

```php
<?php
// app/Http/Kernel.php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The application's route middleware.
     */
    protected $routeMiddleware = [
        // ... existing middleware
        
        // Authentication middleware
        'auth' => \App\Http\Middleware\Authenticate::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        
        // Custom Filament-Fortify integration middleware
        'filament.fortify.auth' => \App\Http\Middleware\FortifyAuthenticateForFilament::class,
    ];
}
```

## 4.4 Route Configuration Updates

### 4.4.1 Web Routes Configuration

**Update routes/web.php for unified authentication**:

```php
<?php
// routes/web.php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\TwoFactorAuthenticationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes - Unified Fortify Authentication
|--------------------------------------------------------------------------
*/

// Public routes
Route::get('/', function () {
    return view('welcome');
});

// Dashboard route (protected by Fortify 2FA)
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Profile management routes (authenticated but 2FA setup allowed)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    
    // Two-Factor Authentication setup routes
    Route::get('/two-factor/setup', function () {
        return view('livewire.auth.two-factor.setup');
    })->name('two-factor.setup');
    
    Route::get('/user/two-factor-authentication', [TwoFactorAuthenticationController::class, 'show'])
        ->name('two-factor.show');
    Route::post('/user/two-factor-authentication', [TwoFactorAuthenticationController::class, 'store'])
        ->name('two-factor.enable');
    Route::delete('/user/two-factor-authentication', [TwoFactorAuthenticationController::class, 'destroy'])
        ->name('two-factor.disable');
    Route::get('/user/two-factor-qr-code', [TwoFactorAuthenticationController::class, 'qrCode'])
        ->name('two-factor.qr-code');
    Route::get('/user/two-factor-recovery-codes', [TwoFactorAuthenticationController::class, 'recoveryCodes'])
        ->name('two-factor.recovery-codes');
    Route::post('/user/two-factor-recovery-codes', [TwoFactorAuthenticationController::class, 'regenerateRecoveryCodes'])
        ->name('two-factor.recovery-codes.regenerate');
});

// Note: Fortify routes are automatically registered
// Admin panel routes are handled by Filament with custom middleware
```

### 4.4.2 Admin Panel Route Handling

**Filament Route Integration**:

The admin panel routes (`/admin/*`) are automatically handled by Filament, but now use our custom `FortifyAuthenticateForFilament` middleware to ensure:

1. **Fortify Authentication**: Users must be authenticated via Fortify
2. **Mandatory 2FA**: Users must have Fortify 2FA enabled
3. **Panel Access Control**: Users must pass `canAccessPanel()` authorization

## 4.5 Configuration File Updates

### 4.5.1 Fortify Configuration

**Update config/fortify.php for Filament integration**:

```php
<?php
// config/fortify.php

use App\Providers\RouteServiceProvider;
use Laravel\Fortify\Features;

return [
    'guard' => 'web',
    'passwords' => 'users',
    'username' => 'email',
    
    // Redirect to dashboard after successful authentication
    'home' => '/dashboard',
    
    'prefix' => '',
    'domain' => null,
    'middleware' => ['web'],
    
    'limiters' => [
        'login' => 'login',
        'two-factor' => 'two-factor',
    ],
    
    'views' => true,
    
    'features' => [
        Features::registration(),
        Features::resetPasswords(),
        Features::emailVerification(),
        Features::updateProfileInformation(),
        Features::updatePasswords(),
        Features::twoFactorAuthentication([
            'confirm' => true,
            'confirmPassword' => true,
            'window' => env('TWO_FACTOR_CONFIRM_PASSWORD_TIMEOUT', 10800),
        ]),
    ],
];
```

### 4.5.2 Authentication Configuration

**Update config/auth.php for unified guards**:

```php
<?php
// config/auth.php

return [
    'defaults' => [
        'guard' => 'web',           // Single guard for all authentication
        'passwords' => 'users',
    ],

    'guards' => [
        'web' => [
            'driver' => 'session',
            'provider' => 'users',
        ],
        // Remove separate Filament guard - use unified web guard
    ],

    'providers' => [
        'users' => [
            'driver' => 'eloquent',
            'model' => App\Models\User::class,
        ],
    ],

    'passwords' => [
        'users' => [
            'provider' => 'users',
            'table' => env('AUTH_PASSWORD_RESET_TOKEN_TABLE', 'password_reset_tokens'),
            'expire' => 60,
            'throttle' => 60,
        ],
    ],

    'password_timeout' => env('AUTH_PASSWORD_TIMEOUT', 10800),
];
```

## 4.6 Two-Factor Authentication Controller

### 4.6.1 Create TwoFactorAuthenticationController

**Generate Controller**:

```bash
# Create controller for 2FA management
php artisan make:controller TwoFactorAuthenticationController
```

**Implement Controller**:

```php
<?php
// app/Http/Controllers/TwoFactorAuthenticationController.php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use Laravel\Fortify\Actions\GenerateNewRecoveryCodes;
use Laravel\Fortify\Actions\DisableTwoFactorAuthentication;

class TwoFactorAuthenticationController extends Controller
{
    /**
     * Show the two-factor authentication setup page.
     */
    public function show(Request $request)
    {
        return view('livewire.auth.two-factor.setup');
    }

    /**
     * Enable two-factor authentication for the user.
     */
    public function store(Request $request)
    {
        app(EnableTwoFactorAuthentication::class)($request->user());

        return back()->with('status', 'two-factor-authentication-enabled');
    }

    /**
     * Disable two-factor authentication for the user.
     */
    public function destroy(Request $request)
    {
        app(DisableTwoFactorAuthentication::class)($request->user());

        return back()->with('status', 'two-factor-authentication-disabled');
    }

    /**
     * Get the SVG element for the user's two-factor authentication QR code.
     */
    public function qrCode(Request $request)
    {
        return response($request->user()->twoFactorQrCodeSvg(), 200, [
            'Content-Type' => 'image/svg+xml',
        ]);
    }

    /**
     * Get the user's two-factor authentication recovery codes.
     */
    public function recoveryCodes(Request $request)
    {
        return response()->json([
            'recovery_codes' => $request->user()->recoveryCodes(),
        ]);
    }

    /**
     * Generate new recovery codes for the user.
     */
    public function regenerateRecoveryCodes(Request $request)
    {
        app(GenerateNewRecoveryCodes::class)($request->user());

        return response()->json([
            'recovery_codes' => $request->user()->recoveryCodes(),
        ]);
    }
}
```

## 4.7 Testing Filament Integration

### 4.7.1 Integration Test Implementation

**Create Filament Integration Test**:

```bash
# Create test for Filament-Fortify integration
php artisan make:test FilamentFortifyIntegrationTest
```

```php
<?php
// tests/Feature/FilamentFortifyIntegrationTest.php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Fortify\Actions\EnableTwoFactorAuthentication;
use PragmaRX\Google2FA\Google2FA;
use Tests\TestCase;

class FilamentFortifyIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_unauthenticated_user_cannot_access_admin_panel(): void
    {
        $response = $this->get('/admin');
        
        $response->assertRedirect('/login');
    }

    public function test_authenticated_user_without_2fa_cannot_access_admin_panel(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/admin');
        
        $response->assertRedirect('/two-factor/setup');
        $response->assertSessionHas('status', 'two-factor-required-for-admin');
    }

    public function test_authenticated_user_with_fortify_2fa_can_access_admin_panel(): void
    {
        $user = User::factory()->create();
        
        // Enable and confirm Fortify 2FA
        app(EnableTwoFactorAuthentication::class)($user);
        $user->forceFill(['two_factor_confirmed_at' => now()])->save();

        $response = $this->actingAs($user)->get('/admin');
        
        $response->assertStatus(200);
    }

    public function test_user_with_migrated_2fa_can_access_admin_panel(): void
    {
        $user = User::factory()->withBoth2FA()->create();

        $response = $this->actingAs($user)->get('/admin');
        
        $response->assertStatus(200);
    }

    public function test_custom_middleware_enforces_2fa_requirement(): void
    {
        $user = User::factory()->create();
        
        // Test middleware directly
        $request = \Illuminate\Http\Request::create('/admin', 'GET');
        $request->setUserResolver(function () use ($user) {
            return $user;
        });
        
        $middleware = new \App\Http\Middleware\FortifyAuthenticateForFilament();
        
        $response = $middleware->handle($request, function () {
            return response('OK');
        });
        
        $this->assertEquals(302, $response->getStatusCode());
    }

    public function test_filament_panel_access_authorization(): void
    {
        $user = User::factory()->withFortify2FA()->create();
        
        // Test canAccessPanel method
        $panel = \Filament\Facades\Filament::getDefaultPanel();
        $this->assertTrue($user->canAccessPanel($panel));
        
        // Test user without 2FA
        $userWithout2FA = User::factory()->create();
        $this->assertFalse($userWithout2FA->canAccessPanel($panel));
    }
}
```

### 4.7.2 Manual Testing Checklist

**Filament Integration Testing Checklist**:

- [ ] **Unauthenticated Access**: Visiting `/admin` redirects to login
- [ ] **Login Flow**: Fortify login works and redirects appropriately
- [ ] **2FA Requirement**: Users without 2FA are redirected to setup
- [ ] **Admin Panel Access**: Users with 2FA can access admin panel
- [ ] **Profile Management**: Filament profile features work correctly
- [ ] **Authorization**: Panel access control works as expected
- [ ] **Session Management**: Logout works correctly
- [ ] **Error Handling**: Proper error messages for access denied

---

**Navigation Footer**

← [Previous: User Model Transformation](035-user-model-transformation.md) | [Next: Volt + Flux UI Components →](045-volt-flux-ui-components.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/040-filament-integration.md`
- **Document ID**: LF-2FA-006-UNIFIED
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
