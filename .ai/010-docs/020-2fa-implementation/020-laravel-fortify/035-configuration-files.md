# 3.5 Laravel Fortify 2FA Implementation - Configuration Files

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 20 minutes

## 3.5.1 Executive Summary

This document provides comprehensive configuration file setup for Laravel Fortify 2FA implementation, including environment variables, configuration files, and middleware registration for seamless integration with the existing Filament system.

## 3.5.2 Environment Configuration

### 3.5.2.1 Environment Variables Setup

**Step 1: Update .env File**

Add the following configuration to your `.env` file:

```bash
# Laravel Fortify Configuration
FORTIFY_GUARD=web
FORTIFY_PASSWORDS=users
FORTIFY_USERNAME=email
FORTIFY_EMAIL=email
FORTIFY_HOME=/dashboard

# Two-Factor Authentication Settings
TWO_FACTOR_AUTH_ENABLED=true
TWO_FACTOR_RECOVERY_CODES=8
TWO_FACTOR_CONFIRM_PASSWORD_TIMEOUT=10800
TWO_FACTOR_QR_CODE_SIZE=200
TWO_FACTOR_QR_CODE_MARGIN=2

# Application Settings
APP_2FA_ISSUER="${APP_NAME}"
APP_2FA_DIGITS=6
APP_2FA_PERIOD=30
APP_2FA_ALGORITHM=sha1

# Session Configuration for 2FA
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_FILES_LIFETIME=120

# Rate Limiting
FORTIFY_LOGIN_RATE_LIMIT=5
FORTIFY_2FA_RATE_LIMIT=5
```

**Step 2: Update .env.example**

Ensure your `.env.example` includes the new variables:

```bash
# Add to .env.example
FORTIFY_GUARD=web
FORTIFY_PASSWORDS=users
FORTIFY_USERNAME=email
FORTIFY_EMAIL=email
FORTIFY_HOME=/dashboard

TWO_FACTOR_AUTH_ENABLED=true
TWO_FACTOR_RECOVERY_CODES=8
TWO_FACTOR_CONFIRM_PASSWORD_TIMEOUT=10800
TWO_FACTOR_QR_CODE_SIZE=200
TWO_FACTOR_QR_CODE_MARGIN=2

APP_2FA_ISSUER="${APP_NAME}"
APP_2FA_DIGITS=6
APP_2FA_PERIOD=30
APP_2FA_ALGORITHM=sha1
```

## 3.5.3 Fortify Configuration

### 3.5.3.1 Main Fortify Configuration

**File**: `config/fortify.php`

```php
<?php

use App\Providers\RouteServiceProvider;
use Laravel\Fortify\Features;

return [

    /*
    |--------------------------------------------------------------------------
    | Fortify Guard
    |--------------------------------------------------------------------------
    |
    | Here you may specify which authentication guard Fortify will use while
    | authenticating users. This value should correspond with one of your
    | guards that is already present in your "auth" configuration file.
    |
    */

    'guard' => env('FORTIFY_GUARD', 'web'),

    /*
    |--------------------------------------------------------------------------
    | Fortify Password Broker
    |--------------------------------------------------------------------------
    |
    | Here you may specify which password broker Fortify can use when a user
    | is resetting their password. This configured value should match one
    | of your password brokers setup in your "auth" configuration file.
    |
    */

    'passwords' => env('FORTIFY_PASSWORDS', 'users'),

    /*
    |--------------------------------------------------------------------------
    | Username / Email
    |--------------------------------------------------------------------------
    |
    | This value defines which model attribute should be considered as your
    | application's "username" field. Typically, this might be the email
    | address of the users but you are free to change this value here.
    |
    */

    'username' => env('FORTIFY_USERNAME', 'email'),

    /*
    |--------------------------------------------------------------------------
    | Home Path
    |--------------------------------------------------------------------------
    |
    | Here you may configure the path where users will get redirected during
    | authentication or password reset when the operations are successful
    | and the user is authenticated. You are free to change this value.
    |
    */

    'home' => env('FORTIFY_HOME', '/dashboard'),

    /*
    |--------------------------------------------------------------------------
    | Fortify Routes Prefix / Subdomain
    |--------------------------------------------------------------------------
    |
    | Here you may specify which prefix Fortify will assign to all the routes
    | that it registers with the application. If necessary, you may change
    | subdomain under which all of the Fortify routes will be available.
    |
    */

    'prefix' => '',
    'domain' => null,

    /*
    |--------------------------------------------------------------------------
    | Fortify Routes Middleware
    |--------------------------------------------------------------------------
    |
    | Here you may specify which middleware Fortify will assign to the routes
    | that it registers with the application. When necessary, you may modify
    | these middleware; however, this default value is usually sufficient.
    |
    */

    'middleware' => ['web'],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | By default, Fortify will throttle logins to five requests per minute for
    | every email and IP address combination. However, if you would like to
    | specify a custom rate limiter to call then you may specify it here.
    |
    */

    'limiters' => [
        'login' => 'login',
        'two-factor' => 'two-factor',
    ],

    /*
    |--------------------------------------------------------------------------
    | Register View Routes
    |--------------------------------------------------------------------------
    |
    | Here you may specify if the routes returning views should be disabled as
    | you may not need them when building your own application. This may be
    | especially true if you're writing a custom single-page application.
    |
    */

    'views' => true,

    /*
    |--------------------------------------------------------------------------
    | Features
    |--------------------------------------------------------------------------
    |
    | Some of the Fortify features are optional. You may disable the features
    | by removing them from this array. You're free to only remove some of
    | these features or you can even remove all of these if you need to.
    |
    */

    'features' => [
        Features::registration(),
        Features::resetPasswords(),
        Features::emailVerification(),
        Features::updateProfileInformation(),
        Features::updatePasswords(),
        Features::twoFactorAuthentication([
            'confirm' => true,
            'confirmPassword' => true,
            'window' => env('TWO_FACTOR_CONFIRM_PASSWORD_TIMEOUT', 10800),
        ]),
    ],

];
```

### 3.5.3.2 Custom Two-Factor Configuration

**File**: `config/two-factor.php` (Create new file)

```php
<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Two-Factor Authentication Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for two-factor authentication
    | in your application. You may modify these settings as needed.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Issuer Name
    |--------------------------------------------------------------------------
    |
    | This value will be used as the issuer name in the QR code and TOTP apps.
    | It should be the name of your application.
    |
    */

    'issuer' => env('APP_2FA_ISSUER', env('APP_NAME', 'Laravel')),

    /*
    |--------------------------------------------------------------------------
    | TOTP Configuration
    |--------------------------------------------------------------------------
    |
    | These values configure the Time-based One-Time Password (TOTP) settings.
    |
    */

    'totp' => [
        'digits' => env('APP_2FA_DIGITS', 6),
        'period' => env('APP_2FA_PERIOD', 30),
        'algorithm' => env('APP_2FA_ALGORITHM', 'sha1'),
        'window' => env('APP_2FA_WINDOW', 1),
    ],

    /*
    |--------------------------------------------------------------------------
    | Recovery Codes
    |--------------------------------------------------------------------------
    |
    | Configuration for recovery codes generation and management.
    |
    */

    'recovery_codes' => [
        'count' => env('TWO_FACTOR_RECOVERY_CODES', 8),
        'length' => 10,
        'blocks' => 2,
        'block_separator' => '-',
    ],

    /*
    |--------------------------------------------------------------------------
    | QR Code Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for QR code generation and display.
    |
    */

    'qr_code' => [
        'size' => env('TWO_FACTOR_QR_CODE_SIZE', 200),
        'margin' => env('TWO_FACTOR_QR_CODE_MARGIN', 2),
        'error_correction' => 'M', // L, M, Q, H
        'encoding' => 'UTF-8',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Additional security configurations for 2FA.
    |
    */

    'security' => [
        'confirm_password_timeout' => env('TWO_FACTOR_CONFIRM_PASSWORD_TIMEOUT', 10800),
        'require_confirmation' => true,
        'backup_codes_regenerate_threshold' => 3,
    ],

];
```

## 3.5.4 Authentication Configuration Updates

### 3.5.4.1 Update Auth Configuration

**File**: `config/auth.php` (Update existing)

```php
<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Defaults
    |--------------------------------------------------------------------------
    */

    'defaults' => [
        'guard' => env('AUTH_GUARD', 'web'),
        'passwords' => env('AUTH_PASSWORD_BROKER', 'users'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication Guards
    |--------------------------------------------------------------------------
    */

    'guards' => [
        'web' => [
            'driver' => 'session',
            'provider' => 'users',
        ],
        
        // Filament guard (if not already present)
        'filament' => [
            'driver' => 'session',
            'provider' => 'users',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Providers
    |--------------------------------------------------------------------------
    */

    'providers' => [
        'users' => [
            'driver' => 'eloquent',
            'model' => env('AUTH_MODEL', App\Models\User::class),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Resetting Passwords
    |--------------------------------------------------------------------------
    */

    'passwords' => [
        'users' => [
            'provider' => 'users',
            'table' => env('AUTH_PASSWORD_RESET_TOKEN_TABLE', 'password_reset_tokens'),
            'expire' => 60,
            'throttle' => 60,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Confirmation Timeout
    |--------------------------------------------------------------------------
    */

    'password_timeout' => env('AUTH_PASSWORD_TIMEOUT', 10800),

];
```

## 3.5.5 Middleware Configuration

### 3.5.5.1 Register Custom Middleware

**File**: `app/Http/Kernel.php` (Update existing)

```php
<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     */
    protected $middleware = [
        // ... existing middleware
    ];

    /**
     * The application's route middleware groups.
     */
    protected $middlewareGroups = [
        'web' => [
            // ... existing web middleware
        ],

        'api' => [
            // ... existing api middleware
        ],
    ];

    /**
     * The application's route middleware.
     */
    protected $routeMiddleware = [
        // ... existing middleware
        
        // Authentication middleware
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        
        // Two-Factor Authentication middleware
        '2fa.enabled' => \App\Http\Middleware\EnsureTwoFactorEnabled::class,
    ];
}
```

### ******* Create Custom 2FA Middleware

**File**: `app/Http/Middleware/EnsureTwoFactorEnabled.php` (Create new)

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureTwoFactorEnabled
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        // Skip 2FA check for Filament admin routes
        if ($request->is('admin/*')) {
            return $next($request);
        }
        
        // Skip 2FA check for 2FA setup routes
        if ($request->is('user/two-factor-authentication*') || 
            $request->is('user/confirmed-two-factor-authentication*') ||
            $request->is('user/two-factor-recovery-codes*')) {
            return $next($request);
        }
        
        // Check if user has 2FA enabled for web routes
        if ($user && !$user->hasEnabledTwoFactorAuthentication()) {
            return redirect()->route('profile.show')
                ->with('status', 'two-factor-authentication-required')
                ->with('message', 'Please enable two-factor authentication to access this feature.');
        }
        
        return $next($request);
    }
}
```

## 3.5.6 Route Configuration

### ******* Web Routes Configuration

**File**: `routes/web.php` (Update existing)

```php
<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\TwoFactorAuthenticationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', function () {
    return view('welcome');
});

// Dashboard route (protected by 2FA)
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified', '2fa.enabled'])->name('dashboard');

// Profile management routes (authenticated but 2FA not required for setup)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    
    // Two-Factor Authentication routes
    Route::get('/user/two-factor-authentication', [TwoFactorAuthenticationController::class, 'show'])
        ->name('two-factor.show');
    Route::post('/user/two-factor-authentication', [TwoFactorAuthenticationController::class, 'store'])
        ->name('two-factor.enable');
    Route::delete('/user/two-factor-authentication', [TwoFactorAuthenticationController::class, 'destroy'])
        ->name('two-factor.disable');
    Route::get('/user/two-factor-qr-code', [TwoFactorAuthenticationController::class, 'qrCode'])
        ->name('two-factor.qr-code');
    Route::get('/user/two-factor-recovery-codes', [TwoFactorAuthenticationController::class, 'recoveryCodes'])
        ->name('two-factor.recovery-codes');
    Route::post('/user/two-factor-recovery-codes', [TwoFactorAuthenticationController::class, 'regenerateRecoveryCodes'])
        ->name('two-factor.recovery-codes.regenerate');
});

// Protected application routes (require 2FA)
Route::middleware(['auth', 'verified', '2fa.enabled'])->group(function () {
    // Add your protected routes here
    Route::get('/secure-area', function () {
        return view('secure-area');
    })->name('secure-area');
});

// Fortify routes are automatically registered
// No need to manually define login, register, etc.
```

### 3.5.6.2 Route Service Provider Updates

**File**: `app/Providers/RouteServiceProvider.php` (Update existing)

```php
<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     */
    public const HOME = '/dashboard';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));
        });
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
        
        // Additional rate limiters are configured in FortifyServiceProvider
    }
}
```

---

**Navigation Footer**

← [Previous: Implementation Documentation](030-implementation-documentation.md) | [Next: Blade Templates →](040-blade-templates.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/035-configuration-files.md`
- **Document ID**: LF-2FA-006
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
