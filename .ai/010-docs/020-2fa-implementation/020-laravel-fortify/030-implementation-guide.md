# 3.0 Laravel Fortify 2FA Implementation - Implementation Guide

**Document Version**: 1.0  
**Last Updated**: 2025-07-01  
**Target Audience**: Junior Developers  
**Estimated Reading Time**: 30 minutes

## 3.1 Executive Summary

This document provides step-by-step implementation instructions for Laravel Fortify 2FA integration with the existing Filament 4.0-beta11 system. All commands and code examples are tested and verified for Laravel 12.19.3 with PHP 8.4, ensuring seamless dual 2FA system operation.

### 3.1.1 Implementation Roadmap

| Step | Task | Estimated Time | Dependencies |
|------|------|----------------|--------------|
| **3.2** | Package Installation | 10 minutes | Composer access |
| **3.3** | Database Migration | 5 minutes | Package installed |
| **3.4** | Service Provider Setup | 15 minutes | Migration complete |
| **3.5** | User Model Enhancement | 10 minutes | Service providers |
| **3.6** | Configuration Files | 20 minutes | Model updated |

## 3.2 Package Installation

### 3.2.1 Laravel Fortify Installation

**Step 1: Install Laravel Fortify Package**

```bash
# Navigate to project root
cd /Users/<USER>/Herd/lfsl

# Install Laravel Fortify with Laravel 12.x compatibility
composer require laravel/fortify "^1.25"
```

**Expected Output**:
```bash
Using version ^1.25.0 for laravel/fortify
./composer.json has been updated
Running composer update laravel/fortify
Loading composer repositories with package information
Updating dependencies
Lock file operations: 1 install, 0 updates, 0 removals
  - Installing laravel/fortify (v1.25.0)
Writing lock file
Installing dependencies from lock file (including require-dev)
Package operations: 1 install, 0 updates, 0 removals
  - Installing laravel/fortify (v1.25.0): Extracting archive
Generating optimized autoload files
```

**Step 2: Install Laravel Sanctum (Required Dependency)**

```bash
# Install Sanctum for API authentication (required by Fortify)
composer require laravel/sanctum "^4.0"
```

**Step 3: Publish Configuration Files**

```bash
# Publish Fortify configuration
php artisan vendor:publish --provider="Laravel\Fortify\FortifyServiceProvider"

# Publish Sanctum configuration
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

**Expected Files Created**:
```bash
# Fortify configuration
config/fortify.php

# Sanctum configuration  
config/sanctum.php

# Fortify stubs (for customization)
stubs/fortify/
```

**Step 4: Verify Installation**

```bash
# Check installed packages
composer show laravel/fortify
composer show laravel/sanctum

# Verify configuration files
ls -la config/fortify.php config/sanctum.php
```

### 3.2.2 Updated Package Dependencies

**Final composer.json State**:

```json
{
    "require": {
        "php": "^8.4",
        "laravel/framework": "^12.0",
        "laravel/fortify": "^1.25",
        "laravel/sanctum": "^4.0",
        "laravel/tinker": "^2.10.1",
        "filament/filament": "^3.2",
        "livewire/flux": "^2.1",
        "livewire/flux-pro": "^2.2",
        "livewire/volt": "^1.7.0",
        "spatie/laravel-activitylog": "^4.10",
        "spatie/laravel-data": "^4.17",
        "spatie/laravel-query-builder": "^6.3",
        "spatie/laravel-sluggable": "*",
        "wildside/userstamps": "^3.1"
    }
}
```

## 3.3 Database Migration Implementation

### 3.3.1 Create Migration Files

**Step 1: Generate Primary Migration**

```bash
# Create migration for Fortify 2FA fields
php artisan make:migration add_fortify_two_factor_columns_to_users_table --table=users
```

**Step 2: Implement Primary Migration**

Edit the generated migration file:

```php
<?php
// database/migrations/YYYY_MM_DD_HHMMSS_add_fortify_two_factor_columns_to_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Laravel Fortify 2FA columns
            $table->text('two_factor_secret')
                  ->nullable()
                  ->after('remember_token')
                  ->comment('Encrypted TOTP secret for Laravel Fortify 2FA');
                  
            $table->text('two_factor_recovery_codes')
                  ->nullable()
                  ->after('two_factor_secret')
                  ->comment('Encrypted recovery codes for Laravel Fortify 2FA');
                  
            $table->timestamp('two_factor_confirmed_at')
                  ->nullable()
                  ->after('two_factor_recovery_codes')
                  ->comment('Timestamp when Fortify 2FA was confirmed and activated');
        });
        
        Log::info('Fortify 2FA columns added to users table', [
            'timestamp' => now(),
            'migration' => class_basename(static::class)
        ]);
    }

    /**
     * Reverse the migrations with safety validation.
     */
    public function down(): void
    {
        // Safety check: Prevent rollback if users have Fortify 2FA enabled
        $usersWithFortify2FA = DB::table('users')
            ->whereNotNull('two_factor_secret')
            ->orWhereNotNull('two_factor_confirmed_at')
            ->count();
        
        if ($usersWithFortify2FA > 0) {
            throw new Exception(
                "Cannot rollback migration: {$usersWithFortify2FA} users have Fortify 2FA enabled. " .
                "Please disable 2FA for all users before rolling back."
            );
        }
        
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'two_factor_confirmed_at',
                'two_factor_recovery_codes', 
                'two_factor_secret'
            ]);
        });
        
        Log::info('Fortify 2FA columns removed from users table', [
            'timestamp' => now(),
            'affected_users' => 0
        ]);
    }
};
```

**Step 3: Create Performance Index Migration**

```bash
# Create separate migration for indexes
php artisan make:migration add_two_factor_indexes_to_users_table --table=users
```

```php
<?php
// database/migrations/YYYY_MM_DD_HHMMSS_add_two_factor_indexes_to_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Performance indexes for 2FA queries
            $table->index('two_factor_confirmed_at', 'users_2fa_confirmed_index');
            $table->index(['email', 'two_factor_confirmed_at'], 'users_email_2fa_index');
            $table->index(['two_factor_secret', 'two_factor_confirmed_at'], 'users_2fa_status_index');
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_2fa_status_index');
            $table->dropIndex('users_email_2fa_index');
            $table->dropIndex('users_2fa_confirmed_index');
        });
    }
};
```

**Step 4: Run Migrations**

```bash
# Execute migrations
php artisan migrate

# Verify migration status
php artisan migrate:status

# Check database schema
php artisan tinker
>>> Schema::getColumnListing('users')
```

## 3.4 Service Provider Implementation

### 3.4.1 Create FortifyServiceProvider

**Step 1: Generate Service Provider**

```bash
# Create Fortify service provider
php artisan make:provider FortifyServiceProvider
```

**Step 2: Implement FortifyServiceProvider**

```php
<?php
// app/Providers/FortifyServiceProvider.php

namespace App\Providers;

use App\Actions\Fortify\CreateNewUser;
use App\Actions\Fortify\ResetUserPassword;
use App\Actions\Fortify\UpdateUserPassword;
use App\Actions\Fortify\UpdateUserProfileInformation;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Laravel\Fortify\Fortify;

class FortifyServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register any bindings or singletons here if needed
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Fortify actions
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        // Configure rate limiting for security
        $this->configureRateLimiting();

        // Register Volt-based views for modern UI
        $this->registerVoltViews();
    }

    /**
     * Configure rate limiting for authentication endpoints.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('login', function (Request $request) {
            $email = (string) $request->email;
            return Limit::perMinute(5)->by($email.$request->ip());
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });
    }

    /**
     * Register Volt-based views for Fortify authentication.
     */
    protected function registerVoltViews(): void
    {
        Fortify::loginView(function () {
            return view('auth.login');
        });

        Fortify::registerView(function () {
            return view('auth.register');
        });

        Fortify::requestPasswordResetLinkView(function () {
            return view('auth.forgot-password');
        });

        Fortify::resetPasswordView(function ($request) {
            return view('auth.reset-password', ['request' => $request]);
        });

        Fortify::verifyEmailView(function () {
            return view('auth.verify-email');
        });

        Fortify::twoFactorChallengeView(function () {
            return view('auth.two-factor-challenge');
        });

        Fortify::confirmPasswordView(function () {
            return view('auth.confirm-password');
        });
    }
}
```

**Step 3: Register Service Provider**

Edit `config/app.php`:

```php
// config/app.php
'providers' => [
    // ... existing providers

    /*
     * Application Service Providers...
     */
    App\Providers\AppServiceProvider::class,
    App\Providers\AuthServiceProvider::class,
    App\Providers\EventServiceProvider::class,
    App\Providers\RouteServiceProvider::class,
    
    // Filament providers (existing)
    App\Providers\Filament\AdminPanelProvider::class,
    
    // Fortify provider (add this line)
    App\Providers\FortifyServiceProvider::class,
],
```

### 3.4.2 Create Fortify Action Classes

**Step 1: Create Actions Directory Structure**

```bash
# Create actions directory
mkdir -p app/Actions/Fortify
```

**Step 2: Create UpdateUserProfileInformation Action**

```php
<?php
// app/Actions/Fortify/UpdateUserProfileInformation.php

namespace App\Actions\Fortify;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;

class UpdateUserProfileInformation implements UpdatesUserProfileInformation
{
    /**
     * Validate and update the given user's profile information.
     */
    public function update($user, array $input): void
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($user->id),
            ],
        ])->validateWithBag('updateProfileInformation');

        if ($input['email'] !== $user->email &&
            $user instanceof MustVerifyEmail) {
            $this->updateVerifiedUser($user, $input);
        } else {
            $user->forceFill([
                'name' => $input['name'],
                'email' => $input['email'],
            ])->save();
        }
    }

    /**
     * Update the given verified user's profile information.
     */
    protected function updateVerifiedUser($user, array $input): void
    {
        $user->forceFill([
            'name' => $input['name'],
            'email' => $input['email'],
            'email_verified_at' => null,
        ])->save();

        $user->sendEmailVerificationNotification();
    }
}
```

**Step 3: Create Additional Required Actions**

```bash
# Copy Fortify action stubs
cp -r stubs/fortify/* app/Actions/Fortify/

# Or create them manually if stubs are not available
```

**CreateNewUser.php**:

```php
<?php
// app/Actions/Fortify/CreateNewUser.php

namespace App\Actions\Fortify;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Laravel\Fortify\Contracts\CreatesNewUsers;
use Laravel\Jetstream\Jetstream;

class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Validate and create a newly registered user.
     */
    public function create(array $input): User
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => $this->passwordRules(),
        ])->validate();

        return User::create([
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
        ]);
    }
}
```

## 3.5 User Model Enhancement

### 3.5.1 Update User Model with Fortify Trait

**Step 1: Add Fortify Imports and Trait**

Edit `app/Models/User.php`:

```php
<?php
// app/Models/User.php

namespace App\Models;

// Existing imports...
use Filament\Auth\MultiFactor\App\Contracts\HasAppAuthentication;
use Filament\Auth\MultiFactor\App\Contracts\HasAppAuthenticationRecovery;
use Filament\Auth\MultiFactor\Email\Contracts\HasEmailAuthentication;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Symfony\Component\Uid\Ulid;
use Wildside\Userstamps\Userstamps;

// Add Fortify imports
use Laravel\Fortify\TwoFactorAuthenticatable;

class User extends Authenticatable implements 
    FilamentUser, 
    HasAppAuthentication, 
    HasAppAuthenticationRecovery,
    HasAvatar, 
    HasEmailAuthentication, 
    MustVerifyEmail
{
    use HasFactory, 
        Notifiable, 
        SoftDeletes, 
        HasSlug, 
        LogsActivity, 
        Userstamps,
        TwoFactorAuthenticatable; // Add Fortify trait

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'email', 
        'password',
        'slug',
        'public_id',
        'has_email_authentication',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'password',
        'remember_token',
        
        // Filament 2FA fields
        'app_authentication_secret',
        'app_authentication_recovery_codes',
        
        // Fortify 2FA fields
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            
            // Filament 2FA casts
            'app_authentication_secret' => 'encrypted',
            'app_authentication_recovery_codes' => 'encrypted:array',
            'has_email_authentication' => 'boolean',
            
            // Fortify 2FA casts
            'two_factor_confirmed_at' => 'datetime',
        ];
    }

    // ... existing methods remain unchanged (boot, generatePublicIdIfEmpty, etc.)
    
    /**
     * Determine if the user has enabled two-factor authentication for Fortify.
     */
    public function hasEnabledTwoFactorAuthentication(): bool
    {
        return !is_null($this->two_factor_secret) && 
               !is_null($this->two_factor_confirmed_at);
    }
    
    /**
     * Get the QR code SVG for Fortify 2FA setup.
     */
    public function twoFactorQrCodeSvg(): string
    {
        return app(\PragmaRX\Google2FA\Google2FA::class)->getQRCodeInline(
            config('app.name'),
            $this->email,
            decrypt($this->two_factor_secret)
        );
    }
}
```

---

**Navigation Footer**

← [Previous: Database Migration Planning](025-database-migration-planning.md) | [Next: Configuration Files →](035-configuration-files.md)

---

**Document Information**
- **File Path**: `.ai/010-docs/020-2fa-implementation/020-laravel-fortify/030-implementation-guide.md`
- **Document ID**: LF-2FA-004
- **Version**: 1.0
- **Compliance**: WCAG AA, Junior Developer Guidelines
