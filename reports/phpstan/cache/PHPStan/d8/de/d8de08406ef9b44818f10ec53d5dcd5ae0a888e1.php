<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/lfsl/database
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/lfsl/database/seeders/UserSeeder.php' => 
    array (
      0 => '3e69457382e428b72a52743f2ccd8485156c43d1',
      1 => 
      array (
        0 => 'database\\seeders\\userseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/database/seeders/DatabaseSeeder.php' => 
    array (
      0 => '2fbdd290ac22861dc9631950a662c6b5a902dd37',
      1 => 
      array (
        0 => 'database\\seeders\\databaseseeder',
      ),
      2 => 
      array (
        0 => 'database\\seeders\\run',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/database/factories/UserFactory.php' => 
    array (
      0 => 'b42a861d3235af69325591435e22015406296aaf',
      1 => 
      array (
        0 => 'database\\factories\\userfactory',
      ),
      2 => 
      array (
        0 => 'database\\factories\\definition',
        1 => 'database\\factories\\unverified',
      ),
      3 => 
      array (
      ),
    ),
  ),
));