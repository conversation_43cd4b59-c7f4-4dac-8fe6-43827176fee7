<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/lfsl/bootstrap
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/lfsl/bootstrap/app.php' => 
    array (
      0 => 'ccc1e776e0750606fdd4174538627e05c9527811',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/bootstrap/providers.php' => 
    array (
      0 => 'd0f09522ad2b3cecb581247404a63a205a127800',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
  ),
));