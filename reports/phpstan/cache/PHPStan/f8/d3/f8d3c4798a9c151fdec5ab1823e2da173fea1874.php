<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/lfsl/tests
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/lfsl/tests/Unit/ExampleTest.php' => 
    array (
      0 => 'f6d938489793d83c24084962e31d194e181ac80f',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/Settings/PasswordUpdateTest.php' => 
    array (
      0 => 'ccb3917f8db32bbf14ce6b1e9d67ce324fecfb98',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/Settings/ProfileUpdateTest.php' => 
    array (
      0 => '884fd56df06cbbff5e45de752b82f1b09b2e5195',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/ExampleTest.php' => 
    array (
      0 => '29d016ef9395c65d72901dde82eb43f16df04764',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/Auth/PasswordConfirmationTest.php' => 
    array (
      0 => '2f8196bc989a7eae9a1840ed270a449733e9583f',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/Auth/EmailVerificationTest.php' => 
    array (
      0 => '1104598eb74272e30b931cb017b147ebd41ddfc4',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/Auth/RegistrationTest.php' => 
    array (
      0 => '2c9145d8086618390820860cc98bd53a4c292e55',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/Auth/AuthenticationTest.php' => 
    array (
      0 => 'ef00edfed250098537a64830d5cf483dbffecb4f',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/Auth/PasswordResetTest.php' => 
    array (
      0 => 'a0214f025507036fe8fecbd43bdef188c54bfaf9',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Feature/DashboardTest.php' => 
    array (
      0 => 'b2c84c1c0ff1b2e3f99373493a9bde24f086e319',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/Pest.php' => 
    array (
      0 => 'f4deb9032e00abdbdfa3ce50c6e04a55c378a7ad',
      1 => 
      array (
      ),
      2 => 
      array (
        0 => 'something',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/lfsl/tests/TestCase.php' => 
    array (
      0 => '5f6caea662bb5a5fc2e674f9cffcc9e5fff09a94',
      1 => 
      array (
        0 => 'tests\\testcase',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
  ),
));